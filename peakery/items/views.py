from math import ceil

from peakery.api.views import upload_images_to_s3_with_thumbnails, fix_image_orientation
from peakery.main.services import mapbox_thumbnail_service

lower = str.lower
from peakery.utils.utils import reduce_gpx_points
from django.contrib.gis.measure import D
from peakery.cache import cache_manager
from peakery.notification.models import send
from django.template.defaultfilters import slugify
from peakery.items.models import ItemGroupItem, ItemGroupHighlight, ItemGroupHighlightLogGroup, PeakRouteHighlight, PeakRouteHighlightLogGroup, ItemCountry, ItemRegion
from peakery.tempitems.models import TempItem
from peakery.cities.models import Country,Region
from django.db import connection
from django.contrib.auth.models import User
from peakery.items.forms import ItemUploadImageForm, SummitLogCommentForm, ItemAddForm,ItemResolveRegionForm,ItemResolveRegionForm1,ItemEditForm
from django.http import Http404, HttpResponseBadRequest
from peakery.items.models import Item, ItemPhoto, SummitLog, SummitLogGroup, SummitLogGroupSummit, ItemPhotoCategory, SummitRoute, PeakRoute, PeakRouteRelatedLink, PeakRouteStep, PeakRouteArchive, PeakRouteRelatedLinkArchive, PeakRouteStepArchive, AlternateName, AlternateNameItemCorrection, CountryItemCorrection, RegionItemCorrection
from peakery.items.models import SummitLogComment, SummitLogVideo, SummitLogRelatedLink, ItemGroup, ItemCorrection, ItemDuplicate, Companions, ItemHighlight, ItemHighlightLogGroup
from peakery.items import utils
from django.http import HttpResponse
from django.shortcuts import get_object_or_404, render
from django.contrib.auth.decorators import login_required
from django.core.files.storage import default_storage
import json
from operator import itemgetter
from django.contrib import messages
from django.contrib.admin.views.decorators import staff_member_required
from django.core.files.images import get_image_dimensions
from django.contrib.gis.geos import Point
from django.conf import settings
from peakery.accounts.models import UserRelation, GMAIL_SOURCE, PEAKERY_SOURCE, INPUT_SOURCE
from django.db.models import Q
from peakery.items.utils import get_new_routes_for_peak, get_link_preview_from_url
from peakery.items.models import STATUS_SUMMIT_ACTIVE, STATUS_SUMMIT_PENDING, STATUS_ROUTE_ACTIVE, STATUS_ROUTE_PENDING
from peakery.items.utils import render_to_json
from peakery.main.redis_queue_singleton import redis_queue
from django.contrib.gis.geoip2 import GeoIP2
from ipware import get_client_ip
from django.views.decorators.cache import cache_page
from django.urls import reverse
from django.http import HttpResponseRedirect
import locale
import datetime
locale.setlocale(locale.LC_ALL, settings.LOCALE)

import os
import polyline

def get_object_or_None(model, *args, **kwargs):
    try:
        return model.objects.get(*args, **kwargs)
    except model.DoesNotExist:
        return None


CONTINENTS = {
    "NA": "North America",
    "AS":"Asia",
    "EU":"Europe",
    "AF":"Africa",
    "AN":"Antarctica",
    "SA":"South America",
    "OC":"Oceania",
    }


def index(request):
    return render(request, 'items/index.html', {})

def dictfetchall(cursor):
    "Return all rows from a cursor as a dict"
    columns = [col[0] for col in cursor.description]
    return [
        dict(zip(columns, row))
        for row in cursor.fetchall()
    ]

def uniq(input):
    output = []
    for x in input:
        if x not in output:
            output.append(x)
    return output

def autocomplete_range(request):
    term = request.GET.get('term')
    items = Item.objects.filter(range__istartswith=term).values_list('range').distinct().order_by()
    #print( items)
    res = []
    for c in items:
        dict = {'id':'', 'label':c[0], 'value':c[0]}
        res.append(dict)
    return HttpResponse(json.dumps(res))

def main_peaks_search(request):
    peak_id = request.GET.get('peak_id', 0)
    q = request.GET.get('q')
    n = request.GET.get('n')
    peak_page_title = 'All mountains in the world'
    peak_page_description = 'List of all mountains in the world with maps, trip reports, and trail info'
    if q and not n:
        peak_page_title = '"%s" mountains' % (q)
        peak_page_description = 'List of all "%s" mountains in the world with maps, trip reports, and trail info' % (q)
    elif not q and n:
        peak_page_title = 'Mountains near "%s"' % (n)
        peak_page_description = 'List of all mountains near "%s" with maps, trip reports, and trail info' % (n)
    elif q and n:
        peak_page_title = '"%s" mountains near "%s"' % (q, n)
        peak_page_description = 'List of all "%s" mountains near "%s" with maps, trip reports, and trail info' % (q, n)
    if q:
        prefill_keyword = q
    else:
        prefill_keyword = ''
    if n:
        prefill_near = n
    else:
        prefill_near = ''
    lat = None
    lon = None
    if peak_id != 0:
        peak = get_object_or_404(Item, id=peak_id)
        lat = peak.lat
        lon = peak.long
    nav_peaks_style = 'color: #999;'
    nav_page_name = 'Peaks'

    if request.user.is_superuser:
        latest_challenge_with_item_added = ItemGroupItem.objects.all().order_by("-id")[:1].get()
        if latest_challenge_with_item_added:
            challenge_id = latest_challenge_with_item_added.group_id
            latest_challenge_added = ItemGroup.objects.filter(id=challenge_id)
        else:
            latest_challenge_added = ItemGroup.objects.all().order_by("-created")[:1]
    else:
        latest_challenge_added = None

    return render(request, 'items/peaks_search.html', {
        'nav_page_name':nav_page_name,
        'peak_page_title':peak_page_title,
        'peak_page_description':peak_page_description,
        'q':prefill_keyword,
        'n':prefill_near,
        'nav_peaks_style':nav_peaks_style,
        'lon':lon,
        'lat':lat,
        'latest_challenge_added':latest_challenge_added
    })

def main_peaks_map(request):
    ip = None
    lon = None
    lat = None
    centro = None
    peak_id = request.GET.get('peak_id', 0)
    lat = request.GET.get('lat', None)
    lon = request.GET.get('lng', None)

    if peak_id != 0:
        peak = Item.objects.filter(id=peak_id)
        for p in peak:
            lat = p.lat
            lon = p.long
    elif lat and lon:
        centro = {"y": lat, "x": lon}
    else:
        try:
            g = GeoIP2()
            ip = request.META.get('HTTP_X_FORWARDED_FOR', None)
            lon = g.lon_lat(ip)[0]
            lat = g.lon_lat(ip)[1]
            centro = {"y": lat, "x": lon}
        except Exception:
            pass

    nav_map_style = 'color: #00B1F2;'
    nav_page_name = 'Map'

    return render(request, 'items/peaks_map.html', {
        'ip':ip,
        'lon':lon,
        'lat':lat,
        'centro':centro,
        'nav_map_style':nav_map_style,
        'nav_page_name':nav_page_name,
        'site_url':os.environ['SITE_URL']
    })


def get_peak_info_for_superuser(user, peak_id):
    if user.is_superuser:
        sql = "SELECT ii.id FROM items_itemgroup ii INNER JOIN items_itemgroupitem ig ON (ii.id = ig.group_id) WHERE ig.item_id = %s ORDER BY ii.name ASC;"
        with connection.cursor() as cursor:
            cursor.execute(sql, [peak_id])
            peak_challenges = dictfetchall(cursor)

        latest_challenge_with_item_added = ItemGroupItem.objects.exclude(group_id__in=[o['id'] for o in peak_challenges]).order_by("-id")[:1].get()
        if latest_challenge_with_item_added:
            challenge_id = latest_challenge_with_item_added.group_id
            latest_challenge_added = ItemGroup.objects.filter(id=challenge_id)
        else:
            latest_challenge_added = ItemGroup.objects.all().order_by("-created")[:1]

        more_challenges = ItemGroup.objects.exclude(id__in=[o['id'] for o in peak_challenges]).exclude(id__in=[o.id for o in latest_challenge_added]).order_by('name')
    else:
        peak_challenges = None
        latest_challenge_added = None
        more_challenges = None

    return peak_challenges, latest_challenge_added, more_challenges


def get_peak_info_summit_data(user, peak_id):
    if user.is_authenticated:
        sql = "select " + \
            "coalesce(e.summitlog_count, 0) as your_summits, " + \
            "coalesce(f.summitlog_count, 0) as your_attempts, " + \
            "coalesce(g.challenge_count, 0) as challenge_count " + \
            "from items_item a " + \
            "left join (select e.item_id, count(e.id) as summitlog_count from items_summitlog e where e.status = 1 and e.attempt = false and e.user_id = %s group by e.item_id) e on e.item_id = a.id " + \
            "left join (select f.item_id, count(f.id) as summitlog_count from items_summitlog f where f.status = 1 and f.attempt = true and f.user_id = %s group by f.item_id) f on f.item_id = a.id " + \
            "left join (select g.item_id, count(g.id) as challenge_count from items_itemgroupitem g group by g.item_id) g on g.item_id = a.id " + \
            "where a.id = %s "

        with connection.cursor() as cursor:
            cursor.execute(sql, [user.id, user.id, peak_id])

            summit_stats = dictfetchall(cursor)
            your_summits_count = summit_stats[0]['your_summits']
            your_attempts_count = summit_stats[0]['your_attempts']
            challenge_count = summit_stats[0]['challenge_count']
    else:
        your_summits_count = 0
        your_attempts_count = 0
        challenge_count = 0

    return your_summits_count, your_attempts_count, challenge_count


def get_peak_info_nearest_items(long, lat, peak_id):
    sql = "select a.name, a.slug_new_text, ST_DistanceSphere(st_makepoint(a.long, a.lat),st_makepoint(%s, %s))/1000 as distance " + \
          "from items_item a " + \
          "where a.id != %s " + \
          "order by a.location <#> ST_SetSRID(ST_MakePoint(%s, %s), 4326) " + \
          "limit 10 "

    with connection.cursor() as cursor:
        cursor.execute(sql, [long, lat, peak_id, long, lat])
        nearest_items = dictfetchall(cursor)

    return nearest_items


def get_peak_info_get_photos_count(peak_id):
    peak_photos_count = 0
    sql = "SELECT COUNT(*) FROM items_itemphoto WHERE item_id = %s"
    with connection.cursor() as cursor:
        cursor.execute(sql, [peak_id])
        for row in cursor.fetchall():
            peak_photos_count = row[0]

    return peak_photos_count


def get_peak_info_alternate_names_list(peak_id):
    alternate_names_list = []
    sql = "SELECT ia.name FROM items_alternatename ia INNER JOIN items_item ON (ia.item_id = items_item.id) WHERE ia.item_id = %s"
    with connection.cursor() as cursor:
        cursor.execute(sql, [peak_id])
        for row in cursor.fetchall():
            alternate_names_list.append(row[0])
    return alternate_names_list


def get_peak_info_summits_count(peak_id):
    summits_count = 0
    sql = "SELECT COUNT(*) FROM items_summitlog WHERE (item_id = %s AND attempt = False AND status = 1)"
    with connection.cursor() as cursor:
        cursor.execute(sql, [peak_id])
        for row in cursor.fetchall():
            summits_count = row[0]

    return summits_count


def get_peak_info_most_recently_bagged(peak_id):
    sql = """
            SELECT i.id as id, au.username as user, i.date as date FROM items_summitlog i, auth_user au 
            WHERE i.item_id = %s AND i.attempt = False AND au.id = i.user_id 
            ORDER BY i.date_entered DESC, i.date DESC 
            LIMIT 1
        """
    with connection.cursor() as cursor:
        cursor.execute(sql, [peak_id])
        most_recently_bagged = dictfetchall(cursor)

    if most_recently_bagged:
        most_recently_bagged = most_recently_bagged[0]
    else:
        most_recently_bagged = None

    return most_recently_bagged


def get_peak_info_king_of_the_mountain(peak_id):
    sql = """
        SELECT 
            ab.id,
            ab.username,
            y.summitlog_count,
            coalesce(replace(replace(f.avatar, '/', '/resized/400/'), 'avatars/resized/400/', 'avatars/'),
            'img/default-user.png') as avatar_url
        FROM items_summitlog aa
            JOIN auth_user ab on ab.id = aa.user_id
            LEFT JOIN avatar_avatar f on f.user_id = aa.user_id
            JOIN (
                SELECT a.user_id, count(a.id) as summitlog_count
                FROM items_summitlog a
                WHERE a.item_id = %s AND a.attempt = false AND a.status = 1
                GROUP BY a.user_id
                HAVING count(a.id) = (
                    SELECT max(x.summitlog_count) as kom_summit_count
                    FROM (SELECT count(aa.id) as summitlog_count
                    FROM items_summitlog aa
                    JOIN auth_user ab on ab.id = aa.user_id
                    LEFT JOIN avatar_avatar f on f.user_id = aa.user_id
                    WHERE aa.item_id = %s
                    AND aa.attempt = false
                    AND aa.status = 1
                    GROUP BY ab.id, ab.username, replace(replace(f.avatar, '/', '/resized/400/'),
                    'avatars/resized/400/', 'avatars/')
                    ORDER BY count(aa.id) desc) x
                )
            ) y on y.user_id = aa.user_id
        WHERE 
            aa.item_id = %s
            AND aa.attempt = false
            AND aa.status = 1
        GROUP BY ab.id, ab.username, y.summitlog_count,
            replace(replace(f.avatar, '/', '/resized/400/'), 'avatars/resized/400/', 'avatars/')
        ORDER BY max(aa.created) ASC
        LIMIT 1;
    """
    king_of_the_mountain = User.objects.raw(sql, [peak_id, peak_id, peak_id])
    king_of_the_mountain_count = 0
    king_of_the_mountain_userid = 0
    for k in king_of_the_mountain:
        king_of_the_mountain_count += 1
        king_of_the_mountain_userid = k.id

    return king_of_the_mountain, king_of_the_mountain_count, king_of_the_mountain_userid


def get_peak_info_first_ascent(peak_id):
    sql = "select " + \
          "ab.id, " + \
          "ab.username, " + \
          "coalesce(replace(replace(f.avatar, '/', '/resized/400/'), 'avatars/resized/400/', 'avatars/'), 'img/default-user.png') as avatar_url, " + \
          "aa.created as log_date " + \
          "from items_summitlog aa " + \
          "join auth_user ab on ab.id = aa.user_id " + \
          "left join avatar_avatar f on f.user_id = aa.user_id " + \
          "where aa.item_id = %s and aa.attempt = false and aa.status = 1 " + \
          "order by aa.created asc limit 1 "

    first_ascent = User.objects.raw(sql, [peak_id])

    return first_ascent, len(first_ascent)

def get_peak_info_summit_stewards(peak_id, king_of_the_mountain_userid):
    sql = "select " + \
          "ab.id, " + \
          "ab.username, " + \
          "coalesce(replace(replace(f.avatar, '/', '/resized/400/'), 'avatars/resized/400/', 'avatars/'), 'img/default-user.png') as avatar_url, " + \
          "count(aa.id) as summitlog_count " + \
          "from items_summitlog aa " + \
          "join auth_user ab on ab.id = aa.user_id " + \
          "left join avatar_avatar f on f.user_id = aa.user_id " + \
          "where aa.item_id = %s and aa.attempt = false and aa.status = 1 and ab.id != %s " + \
          "group by ab.id, ab.username, replace(replace(f.avatar, '/', '/resized/400/'), 'avatars/resized/400/', 'avatars/') having count(aa.id) >= 5 " + \
          "order by count(aa.id) desc "

    summit_stewards = User.objects.raw(sql, [peak_id, king_of_the_mountain_userid])
    return summit_stewards


def get_peak_info_challenges(peak_id):
    sql = "select " + \
          "a.id, " + \
          "a.name, " + \
          "a.description, " + \
          "a.slug, " + \
          "a.thumbnail " + \
          "from items_itemgroup a join items_itemgroupitem aa on aa.group_id = a.id " + \
          "where aa.item_id = %s "

    challenges = ItemGroup.objects.raw(sql, [peak_id])
    return challenges


def get_peak_info_top_three_climbing_months(peak_id):
    sql = "select to_char(a.date, 'MM')::integer as month_number, to_char(a.date, 'Month') as summitlog_month, count(a.id) as summitlog_count " + \
          "from items_summitlog a " + \
          "where a.item_id = %s " + \
          "group by month_number, summitlog_month " + \
          "order by count(a.id) desc "

    with connection.cursor() as cursor:
        cursor.execute(sql, [peak_id])
        top_climbing_months = dictfetchall(cursor)

        top_climbing_months_total = 0
        for m in top_climbing_months:
            top_climbing_months_total = top_climbing_months_total + m.get('summitlog_count')
        top_three_months = []
        keys = ['summitlog_month', 'month_number', 'summitlog_count', 'pct_total']
        for m in top_climbing_months[:3]:
            values = [m.get('summitlog_month'), m.get('month_number'), m.get('summitlog_count'),
                      int(100 * float(m.get('summitlog_count')) / float(top_climbing_months_total))]
            top_three_months.append(dict(zip(keys, values)))

    return top_three_months


def get_peak_info_most_popular_route(peak_id):
    sql = "select b.id, b.total_distance, case when b.one_way = True then concat(b.name, ' (1-way)') else b.name end as name, b.difficulty, b.elevation_gain, b.elevation_gain * .3048 as elevation_gain_in_m " + \
          "from items_summitlog a, items_peakroute b " + \
          "where a.item_id = %s and a.attempt = false and a.status = 1 " + \
          "and a.peak_route_id = b.id " + \
          "group by b.id, b.name, b.difficulty, b.elevation_gain " + \
          "order by count(a.id) desc limit 1"

    with connection.cursor() as cursor:
        cursor.execute(sql, [peak_id])
        most_popular_route = dictfetchall(cursor)

    return most_popular_route


def get_peak_info_featured_logs(peak_id):
    # Featured summit logs
    featured_logs = cache_manager.get_full_peak_featured_logs(peak_id)
    if not featured_logs:
        sql = "select a.id, case when length(a.log) > 1200 then concat(left(a.log, 1200),'...') else a.log end as log_text, a.date as summitlog_date, b.username, " + \
              "case when min(d.image) is not null then get_thumb(min(d.image), 480) else coalesce(replace(replace(c.avatar, '/', '/resized/100/'), 'avatars/resized/100/', 'avatars/'),'img/default-user.png') end as thumbnail_url " + \
              "from items_summitlog a " + \
              "join auth_user b on b.id = a.user_id " + \
              "left join avatar_avatar c on c.user_id = a.user_id " + \
              "left join items_itemphoto d on d.summit_log_id = a.id " + \
              "where a.item_id = %s and a.status = 1 and a.date_entered = true and length(a.log) >= 175 " + \
              "group by a.id, log_text, a.date, b.username, c.avatar " + \
              "order by a.date desc limit 10 "

        with connection.cursor() as cursor:
            cursor.execute(sql, [peak_id])
            featured_logs = dictfetchall(cursor)
            cache_manager.set_full_peak_featured_logs(peak_id, featured_logs)

    return featured_logs


def get_peak_info_get_country_and_region_ranks(peak_summitlog_count, peak_id):
    region_ranks = []
    country_ranks = []

    if peak_summitlog_count >= 0:
        region_sql = """
                SELECT region_name, region_slug, country_slug, region_elevation_rank, region_prominence_rank, region_summits_rank 
                FROM items_itemregionranking where item_id = %s  ORDER BY id ASC
            """
        with connection.cursor() as cursor:
            cursor.execute(region_sql, [peak_id])
            region_ranks = dictfetchall(cursor)

        country_sql = """
                       SELECT country_name, country_slug, country_elevation_rank, country_prominence_rank, country_summits_rank
                       FROM items_itemcountryranking where item_id = %s  ORDER BY id ASC
                   """

        with connection.cursor() as cursor:
            cursor.execute(country_sql, [peak_id])
            country_ranks = dictfetchall(cursor)

    return region_ranks, country_ranks


def get_peak_info_top_four_photos(peak_id):
    # Grabbing latest four photos for hero slideshow
    sql = """
    SELECT a.id,
        get_thumb(a.image, 910) as fullsize_url,
        b.username
    FROM items_itemphoto a
        join auth_user b on b.id = a.user_id
        left join items_summitlog d on d.id = a.summit_log_id
    WHERE a.item_id = %s and not exists (select 1 from items_itemcorrection b where b.new_photo_id = a.id and b.status != 2)
    ORDER BY a.created DESC
    LIMIT 4;
    """

    top_four_photos = ItemPhoto.objects.raw(sql, [peak_id])
    return top_four_photos


def get_peak_info_most_climbed_within_100(long, lat, peak_id):
    # Most climbed peaks within 100 miles
    most_climbed_within_100 = cache_manager.get_most_climbed_peak_within_100_miles(peak_id)

    if not most_climbed_within_100:
        sql = """
            SELECT name, summitlog_count, slug_new_text
            FROM items_item
            WHERE summitlog_count > 0
            AND ST_DWithin(location::geography, ST_SetSRID(ST_MakePoint(%s, %s), 4326)::geography,
                       160000)
            AND id != %s
            ORDER BY summitlog_count DESC
            LIMIT 10;
        """
        with connection.cursor() as cursor:
            cursor.execute(sql, [long, lat, peak_id])
            most_climbed_within_100 = dictfetchall(cursor)
            cache_manager.set_most_climbed_peak_within_100_miles(peak_id, most_climbed_within_100)
    return most_climbed_within_100


def get_peak_info_highest_10_peaks(long, lat, peak_id):
    # Highest 10 peaks within 100 miles
    highest_10_nearby_peaks = cache_manager.get_highest_10_nearby_peaks_within_100_miles(peak_id)
    if not highest_10_nearby_peaks:
        sql = """
            SELECT a.name, cast(a.elevation as int) as elevation, a.slug_new_text
            FROM items_item a
            WHERE ST_DWithin(a.location::geography, ST_SetSRID(ST_MakePoint(%s, %s), 4326)::geography, 160000)
            AND a.id != %s
            ORDER BY a.elevation DESC
            LIMIT 10;
        """

        with connection.cursor() as cursor:
            cursor.execute(sql, [long, lat, peak_id])
            highest_10_nearby_peaks = dictfetchall(cursor)
            cache_manager.set_highest_10_nearby_peaks_within_100_miles(peak_id, highest_10_nearby_peaks)
    return highest_10_nearby_peaks


def get_peak_info_recent_10_climbed_peaks(long, lat, peak_id):
    # Recently climbed 10 peaks within 100 miles
    recent_10_climbed_peaks = cache_manager.get_recent_10_nearby_peaks_within_100_miles(peak_id)
    if not recent_10_climbed_peaks:
        sql = "select x.name, x.slug_new_text, x.last_summit_date " + \
              "from ( " + \
              "select a.id, a.name, a.slug_new_text, max(b.date) as last_summit_date " + \
              "from items_item a " + \
              "join items_summitlog b on b.item_id = a.id " + \
              "where a.summitlog_count > 0 " + \
              "and ST_DWithin(a.location::geography, ST_SetSRID(ST_MakePoint(%s, %s),4326)::geography, 160000) " + \
              "and b.date_entered = true " + \
              "group by a.id, a.name, a.slug_new_text) x " + \
              "where x.id != %s " + \
              "order by x.last_summit_date desc limit 10 "

        with connection.cursor() as cursor:
            cursor.execute(sql, [long, lat, peak_id])
            recent_10_climbed_peaks = dictfetchall(cursor)
            cache_manager.set_recent_10_nearby_peaks_within_100_miles(peak_id, recent_10_climbed_peaks)

    return recent_10_climbed_peaks


def get_peak_info_routes(peak_id):
    sql = """
        SELECT 
            a.id,
            case when a.one_way = True then concat(a.name, ' (1-way)') else a.name end as name,
            a.difficulty,
            a.total_distance,
            a.elevation_gain,
            a.gpx_file,
            a.encoded_polyline,
            coalesce(b.summit_count, 0) as summit_count,
            sum(c.total_trip_time) / count(c.id) as avg_trip_time
        FROM items_peakroute a
            left join (
                select b.peak_route_id, count(b.id) as summit_count
                from items_summitlog b
                where b.item_id = %s
                and b.status = 1
                and b.attempt = false
                group by b.peak_route_id) as b on b.peak_route_id = a.id
            left join (
                select c.id, c.peak_route_id, c.total_trip_time
                from items_summitlog c
                where c.item_id = %s
                and c.status = 1
                and c.total_trip_time > 0) as c on c.peak_route_id = a.id
        WHERE 
            a.item_id = %s
            and a.status = 1
            and a.gpx_file is not null
            and coalesce(b.summit_count, 0) > 0
        GROUP BY a.id, a.name, a.difficulty, a.elevation_gain, a.gpx_file, b.summit_count
        ORDER BY summit_count DESC, a.name ASC;
    """

    with connection.cursor() as cursor:
        cursor.execute(sql, [peak_id, peak_id, peak_id])
        routes = dictfetchall(cursor)
    return routes


#@cache_page(60 * 60)
def view(request, item_slug):

    def filter_routes(routes):
        """
        If a peak has routes with at least 10 summits, hide all routes with only 1 summit
        If a peak has no routes with at least 10 summits, show all routes including those with 1 summit.
        """
        route_with_10_or_more_summits = False
        for route in routes:
            if route['summit_count'] >= 10:
                route_with_10_or_more_summits = True
                break

        if route_with_10_or_more_summits:
            routes = [route for route in routes if route['summit_count'] > 1]
        return routes

    peak = get_object_or_404(Item.objects.filter(slug_new_text__exact=item_slug))

    # Peak highlights
    highlights = ItemHighlight.objects.filter(item_id=peak.id).order_by('created')
    nav_page_name = peak.name

    # Peak Photo
    if peak.thumbnail:
        default_photo = None
    else:
        default_photo = 'img/default.png'

    peak_photos_count = get_peak_info_get_photos_count(peak.id)
    alternate_names_list = get_peak_info_alternate_names_list(peak.id)
    summits_count = get_peak_info_summits_count(peak.id)
    most_recently_bagged = get_peak_info_most_recently_bagged(peak.id)
    your_summits_count, your_attempts_count, challenge_count = get_peak_info_summit_data(request.user, peak.id)
    nearest_items = get_peak_info_nearest_items(peak.long, peak.lat, peak.id)
    king_of_the_mountain, king_of_the_mountain_count, king_of_the_mountain_userid = get_peak_info_king_of_the_mountain(peak.id)
    first_ascent, first_ascent_count = get_peak_info_first_ascent(peak.id)
    summit_stewards = get_peak_info_summit_stewards(peak.id, king_of_the_mountain_userid)
    challenges_list = get_peak_info_challenges(peak.id)
    top_three_months = get_peak_info_top_three_climbing_months(peak.id)
    most_popular_route = get_peak_info_most_popular_route(peak.id)
    featured_logs = get_peak_info_featured_logs(peak.id)
    region_ranks, country_ranks = get_peak_info_get_country_and_region_ranks(peak.summitlog_count, peak.id)
    peak_challenges, latest_challenge_added, more_challenges = get_peak_info_for_superuser(request.user, peak.id)
    top_four_photos = get_peak_info_top_four_photos(peak.id)
    most_climbed_within_100 = get_peak_info_most_climbed_within_100(peak.long, peak.lat, peak.id)
    highest_10_nearby_peaks = get_peak_info_highest_10_peaks(peak.long, peak.lat, peak.id)
    recent_10_climbed_peaks = get_peak_info_recent_10_climbed_peaks(peak.long, peak.lat, peak.id)
    routes = get_peak_info_routes(peak.id)
    routes = filter_routes(routes)


    peak_data = {}
    peak_data['peakname_title'] = peak.get_peakname_title()
    peak_data['peaklocation_title'] = peak.get_peaklocation_title()
    peak_data['peak_meta_description'] = peak.get_peak_meta_description()
    peak_data['thumbnail_480'] = peak.get_thumbnail_480()
    peak_data['thumbnail_910'] = peak.get_thumbnail_910()
    peak_data['thumbnail_1920'] = peak.get_thumbnail_1920()
    peak_data['absolute_url'] = peak.get_absolute_url()
    peak_data['elevation'] = peak.get_elevation()
    peak_data['elevation_in_feet'] = peak.get_elevation_in_feet()
    peak_data['ubicacion_onlycountryname'] = peak.get_ubicacion_onlycountryname()
    peak_data['prominence'] = peak.get_prominence()
    peak_data['main_photo'] = peak.get_main_photo()
    peak_data['is_usa'] = peak.is_usa()
    peak_data['is_usa_but_not_alaska'] = peak.is_usa_but_not_alaska()

    peak_data['countries'] = None
    peak_data['regions'] = None

    country_codes = None
    if peak.country:
        peak_data['countries'] = peak.country.all()
        country_codes = [c.code for c in peak_data['countries']]
    if peak.region:
        peak_data['regions'] = peak.region.all()

    peak_thumbnail = mapbox_thumbnail_service.get_mapbox_thumbnail_s3_url_lng_lat(lng=peak.long, lat=peak.lat, add_marker=False, country_codes=country_codes)

    if routes:
        parameters = []
        for r in routes:
            if r['gpx_file'] and r['encoded_polyline']:
                params = {'external_unique_id': str(r['id']) + "route", 'lng': str(peak.long),
                          'lat': str(peak.lat),
                          'width': 320, 'height': 240, 'gpx_path': r['gpx_file'],
                          'gpx_polyline': r['encoded_polyline']
                          }
                parameters.append(params)

        if parameters:
            routes_thumbnails = mapbox_thumbnail_service.get_mapbox_thumbnail_s3_url_gpx_lng_lat_batched(parameters)
            for r in routes:
                if str(r['id']) + "route" in routes_thumbnails:
                    r['thumbnail'] = routes_thumbnails[str(r['id']) + "route"]

    response = render(request, 'items/view_peak.html',
            {'peak':peak,
             'peak_thumbnail' : peak_thumbnail,
             'peak_data' : peak_data,
             'default_photo':default_photo,
             'peak_photos_count':peak_photos_count,
             'summits_count':summits_count,
             'your_summit_count': your_summits_count,
             'your_summits_count': your_summits_count,
             'your_attempts_count':your_attempts_count,
             'challenge_count': challenge_count,
             'nearest_items':nearest_items,
             'most_recently_bagged':most_recently_bagged,
             'top_three_months':top_three_months,
             'most_popular_route':most_popular_route,
             'region_ranks':region_ranks,
             'country_ranks':country_ranks,
             'item_slug': item_slug,
             'alternate_names_list':alternate_names_list,
             'subnav_info_style': 'font-weight: 500; color: #F24100;',
             'king_of_the_mountain':king_of_the_mountain,
             'king_of_the_mountain_count':king_of_the_mountain_count,
             'first_ascent':first_ascent,
             'first_ascent_count':first_ascent_count,
             'summit_stewards':summit_stewards,
             'challenges':challenges_list,
             'highlights':highlights,
             'featured_logs':featured_logs,
             'nav_page_name':nav_page_name,
             'peak_challenges':peak_challenges,
             'latest_challenge_added':latest_challenge_added,
             'more_challenges':more_challenges,
             'most_climbed_within_100':most_climbed_within_100,
             'highest_10_nearby_peaks':highest_10_nearby_peaks,
             'recent_10_climbed_peaks':recent_10_climbed_peaks,
             'top_four_photos':top_four_photos,
             'routes':routes,
             'fixed_subnav_class': ''
             })
    return response


def peak_view_summit(request, item_slug, summitlog_id):

    peak = get_object_or_404(Item.objects.filter(slug_new_text__exact=item_slug))
    summit = get_object_or_404(SummitLog.objects.filter(id=summitlog_id, item_id=peak.id).select_related("user", "route_up", "route_down", "peak_route", "item"))

    alternate_names_list = peak.alternate_name_field.select_related('item').all()

    sql = "select " + \
        "coalesce(replace(replace(f.avatar, '/', '/resized/400/'), 'avatars/resized/400/', 'avatars/'), 'img/default-user.png') as avatar_url " + \
        "from items_summitlog aa, avatar_avatar f " + \
        "where f.user_id = aa.user_id and aa.id = %s limit 1"

    with connection.cursor() as cursor:
        cursor.execute(sql, [summitlog_id])
        summit_user = dictfetchall(cursor)
    if summit_user:
        avatar_url = summit_user[0]['avatar_url']
    else:
        avatar_url = "img/default-user.png"

    sql = "select ab.id, ab.username, coalesce(replace(replace(f.avatar, '/', '/resized/100/'), 'avatars/resized/100/', 'avatars/'), 'img/default-user.png') as avatar_url, ac.created_on " + \
        "from items_summitlog aa, favorites_favorite ac " + \
        "join auth_user ab on ab.id = ac.user_id " + \
        "left join avatar_avatar f on f.user_id = ab.id " + \
        "where aa.id = %s and ac.object_id = cast(aa.id as text) " + \
        "order by ac.created_on desc "

    with connection.cursor() as cursor:
        cursor.execute(sql, [summit.id])
        users_liked = dictfetchall(cursor)
    like_count = len(users_liked)

    sql = "select ab.id, ab.username, ac.id as comment_id, coalesce(replace(replace(f.avatar, '/', '/resized/100/'), 'avatars/resized/100/', 'avatars/'), 'img/default-user.png') as avatar_url, ac.created, ac.comment " + \
        "from items_summitlog aa, items_summitlogcomment ac " + \
        "join auth_user ab on ab.id = ac.user_id " + \
        "left join avatar_avatar f on f.user_id = ab.id " + \
        "where aa.id = %s and ac.summit_log_id = aa.id " + \
        "order by ac.created asc "

    with connection.cursor() as cursor:
        cursor.execute(sql, [summit.id])
        comments = dictfetchall(cursor)

    sql = "select a.* from items_summitlog a where a.user_id = %s and a.status = 1 and a.attempt = false and a.item_id = %s and a.date <= %s "
    with connection.cursor() as cursor:
        cursor.execute(sql, [summit.user.id, peak.id, summit.date])

        user_summits = dictfetchall(cursor)
    user_summit_count = len(user_summits)

    if request.user.is_authenticated:
        sql = "select a.id, " + \
            "coalesce(e.summitlog_count, 0) as your_summits, " + \
            "coalesce(f.summitlog_count, 0) as your_attempts, " + \
            "coalesce(g.challenge_count, 0) as challenge_count " + \
            "from items_item a " + \
            "left join (select e.item_id, count(e.id) as summitlog_count from items_summitlog e where e.status = 1 and e.attempt = false and e.user_id = %s group by e.item_id) e on e.item_id = a.id " + \
            "left join (select f.item_id, count(f.id) as summitlog_count from items_summitlog f where f.status = 1 and f.attempt = true and f.user_id = %s group by f.item_id) f on f.item_id = a.id " + \
            "left join (select g.item_id, count(g.id) as challenge_count from items_itemgroupitem g group by g.item_id) g on g.item_id = a.id " + \
            "where a.id = %s "

        with connection.cursor() as cursor:
            cursor.execute(sql, [request.user.id, request.user.id, peak.id])

            summit_stats = dictfetchall(cursor)
        your_summits_count = summit_stats[0]['your_summits']
        your_attempts_count = summit_stats[0]['your_attempts']
        challenge_count = summit_stats[0]['challenge_count']
    else:
        your_summits_count = 0
        your_attempts_count = 0
        challenge_count = 0

    if request.user.is_authenticated:
        sql = "select 1 from favorites_favorite a where a.user_id = %s and a.content_type_id = 18 and cast(a.object_id as integer) = %s limit 1"
        with connection.cursor() as cursor:
            cursor.execute(sql, [request.user.id, summit.id])

            request_user_liked = dictfetchall(cursor)
        request_user_like_count = len(request_user_liked)
        if request_user_like_count > 0:
            class_liked = 'liked'
        else:
            class_liked = ''

    else:
        class_liked = ''

    if summit.trip_metadata:
        j = json.loads(summit.trip_metadata)
        if 'gear' in j:
            gear = j["gear"]
        else:
            gear = []

        if 'type' in j:
            trip_type = j["type"]
        else:
            trip_type = []

        if 'activities' in j:
            activities = j["activities"]
        else:
            activities = []

        if 'challenges' in j:
            challenges = j["challenges"]
        else:
            challenges = []
    else:
        gear = []
        trip_type = []
        activities = []
        challenges = []

    sql = "select " + \
        "c.id, " + \
        "c.username, " + \
        "coalesce(replace(replace(f.avatar, '/', '/resized/400/'), 'avatars/resized/400/', 'avatars/'), 'img/default-user.png') as avatar_url " + \
        "from items_companions a " + \
        "join accounts_userrelation b on b.id = a.user_relation_id " + \
        "join auth_user c on c.id = b.to_user_id " + \
        "left join avatar_avatar f on f.user_id = c.id " + \
        "where a.summit_log_id = %s " + \
        "group by c.id, c.username, replace(replace(f.avatar, '/', '/resized/400/'), 'avatars/resized/400/', 'avatars/') " + \
        "union select 0 as id, x.name as username, 'img/default-user.png' as avatar_url " + \
        "from items_summitfellowbagger x where x.summit_log_id = %s " + \
        "order by id asc "

    companions = User.objects.raw(sql, [summit.id, summit.id])

    related_links = SummitLogRelatedLink.objects.filter(summit_log_id=summit.id)
    link_list = []
    for r in related_links:
        related_link = {}
        related_link['title'] = r.title
        related_link['description'] = r.description
        related_link['image'] = r.image
        related_link['url'] = r.related_url
        link_list.append(related_link)

    related_videos = SummitLogVideo.objects.filter(summit_log_id=summit.id)
    for r in related_videos:
        related_link = {}
        related_link['title'] = r.title
        related_link['description'] = r.description
        related_link['image'] = r.image
        related_link['url'] = r.video_url
        link_list.append(related_link)

    nav_page_name = peak.name
    summit_badges = []

    other_summits = SummitLog.objects.filter(summitlog_group_id=summit.summitlog_group_id).select_related("user", "route_up", "route_down", "peak_route", "item").order_by('id')

    no_peak_id = getattr(settings, "NO_PEAK_ID")
    show_subnav = True
    if str(peak.id) == str(no_peak_id):
        show_subnav = False
        user_summit_count = 0


    peak_data = {}
    peak_data['is_usa'] = peak.is_usa()
    peak_data['is_usa_but_not_alaska'] = peak.is_usa_but_not_alaska()
    peak_data['ubication_names_title'] = peak.get_ubication_names_title()
    peak_data['absolute_url'] = peak.get_absolute_url()
    peak_data['thumbnail_480'] = peak.get_thumbnail_480()
    peak_data['thumbnail_745'] = peak.get_thumbnail_745()

    country_string = ''
    region_string = ''
    if peak.country:
        peak_data['countries'] = peak.country.all()
        for c in peak_data['countries']:
            country_string = '%s%s / ' % (country_string, c.name)
        country_string = country_string[:-3]

    if peak.region:
        peak_data['regions'] = peak.region.all()
        for r in peak_data['regions']:
            region_string = '%s%s, %s / ' % (region_string, r.name, r.country.name)
        region_string = region_string[:-3]


    if region_string == '':
        region_string = country_string

    peak_data['meta_description'] = create_meta_description(summit.user.username, summit.date.strftime("%B %d, %Y"), peak.name, peak.elevation, peak.range, region_string)
    peak_data['countries'] = None
    peak_data['regions'] = None

    return render(request, 'items/view_peak_summit.html', {
        'peak':peak,
        'peak_data':peak_data,
        'alternate_names_list':alternate_names_list,
        'subnav_summits_style': 'color: #F24100;',
        'summit':summit,
        'other_summits':other_summits,
        'avatar_url':avatar_url,
        'users_liked':users_liked,
        'like_count':like_count,
        'comments':comments,
        'user_summit_count':user_summit_count,
        'your_summit_count': your_summits_count,
        'your_summits_count': your_summits_count,
        'your_attempts_count':your_attempts_count,
        'challenge_count': challenge_count,
        'gear':gear,
        'trip_type':trip_type,
        'activities':activities,
        'challenges':challenges,
        'companions':companions,
        'related_links':link_list,
        'class_liked':class_liked,
        'summit_badges':summit_badges,
        'nav_page_name':nav_page_name,
        'show_subnav': show_subnav,
        'fixed_subnav_class' : ""
    })


def create_meta_description(username, create_ts, peak_name, elevation, range, peak_location):
    if range:
        text = """
        %(username)s's %(date)s climb of %(peak_name)s, a %(peak_elevation)s mountain in the %(peak_range)s of %(peak_location)s.
        """ % ({'username':username, 'date': create_ts, 'peak_name':peak_name,'peak_elevation':elevation,'peak_range':range,'peak_location': peak_location})
    else:
        text = """
        %(username)s's %(date)s climb of %(peak_name)s, a %(peak_elevation)s mountain.
        """ % ({'username':username, 'date': create_ts, 'peak_name':peak_name,'peak_elevation':elevation})
    return text.strip()[:160]


def peak_view_summit_badges(request, item_slug, summitlog_id):
    if not request.session.get('show_badges', False):
        return HttpResponseRedirect(reverse('peak_view_summit', kwargs={'item_slug':item_slug,'summitlog_id':summitlog_id}))

    peak = get_object_or_404(Item.objects.filter(slug_new_text__exact=item_slug))
    summit = get_object_or_404(SummitLog.objects.filter(id=summitlog_id, item_id=peak.id))
    photos = ItemPhoto.objects.filter(summit_log = summit)

    sql = "select " + \
        "ab.id, " + \
        "ab.username, " + \
        "coalesce(replace(replace(f.avatar, '/', '/resized/400/'), 'avatars/resized/400/', 'avatars/'), 'img/default-user.png') as avatar_url " + \
        "from items_summitlog aa " + \
        "join auth_user ab on ab.id = aa.user_id " + \
        "left join avatar_avatar f on f.user_id = aa.user_id " + \
        "where aa.id = %s "

    summit_user = User.objects.raw(sql, [summitlog_id])
    for s in summit_user:
        avatar_url = s.avatar_url
        bagger = s

    sql = "select " + \
        "ab.id, " + \
        "ab.username, " + \
        "coalesce(replace(replace(f.avatar, '/', '/resized/400/'), 'avatars/resized/400/', 'avatars/'), 'img/default-user.png') as avatar_url, " + \
        "x.oldest_log_date, " + \
        "count(aa.id) as summitlog_count " + \
        "from items_summitlog aa " + \
        "join auth_user ab on ab.id = aa.user_id " + \
        "join (select x.user_id, x.item_id, min(x.created) as oldest_log_date from items_summitlog x group by x.user_id, x.item_id) x on x.user_id = aa.user_id and x.item_id = aa.item_id " + \
        "left join avatar_avatar f on f.user_id = aa.user_id " + \
        "where aa.item_id = %s and aa.status = 1 and aa.attempt = false " + \
        "group by ab.id, ab.username, replace(replace(f.avatar, '/', '/resized/400/'), 'avatars/resized/400/', 'avatars/'), x.oldest_log_date " + \
        "order by count(aa.id) desc, x.oldest_log_date asc limit 2 "

    with connection.cursor() as cursor:
        cursor.execute(sql, [peak.id])
        result = dictfetchall(cursor)
        if cursor.rowcount > 0:
            king_of_the_mountain = result[0]
        else:
            king_of_the_mountain = {'id': 0, 'username': '', 'avatar_url': '', 'oldest_log_date': '', 'summitlog_count': 0}
        if cursor.rowcount > 1:
            second_place = result[1]
            second_place_summits = second_place.get('summitlog_count')
        else:
            second_place_summits = 0

    sql = "select " + \
        "ab.id, " + \
        "ab.username, " + \
        "coalesce(replace(replace(f.avatar, '/', '/resized/400/'), 'avatars/resized/400/', 'avatars/'), 'img/default-user.png') as avatar_url, " + \
        "aa.created as log_date " + \
        "from items_summitlog aa " + \
        "join auth_user ab on ab.id = aa.user_id " + \
        "left join avatar_avatar f on f.user_id = aa.user_id " + \
        "where aa.item_id = %s and aa.status = 1 and aa.attempt = false " + \
        "order by aa.created asc limit 1 "

    with connection.cursor() as cursor:
        cursor.execute(sql, [peak.id])
        if cursor.rowcount > 0:
            first_ascent = dictfetchall(cursor)[0]
        else:
            first_ascent = {'id': 0, 'username': '', 'avatar_url': '', 'log_date': ''}

    sql = "select " + \
        "ab.id, " + \
        "ab.username, " + \
        "coalesce(replace(replace(f.avatar, '/', '/resized/400/'), 'avatars/resized/400/', 'avatars/'), 'img/default-user.png') as avatar_url, " + \
        "count(aa.id) as summitlog_count " + \
        "from items_summitlog aa " + \
        "join auth_user ab on ab.id = aa.user_id " + \
        "left join avatar_avatar f on f.user_id = aa.user_id " + \
        "where aa.item_id = %s and aa.status = 1 and aa.attempt = false " + \
        "group by ab.id, ab.username, replace(replace(f.avatar, '/', '/resized/400/'), 'avatars/resized/400/', 'avatars/') having count(aa.id) >= 5 " + \
        "order by count(aa.id) desc "

    with connection.cursor() as cursor:
        cursor.execute(sql, [peak.id])
        summit_stewards = dictfetchall(cursor)

    sql = "select ab.id, ab.username, coalesce(replace(replace(f.avatar, '/', '/resized/100/'), 'avatars/resized/100/', 'avatars/'), 'img/default-user.png') as avatar_url, ac.created_on " + \
        "from items_summitlog aa, favorites_favorite ac " + \
        "join auth_user ab on ab.id = ac.user_id " + \
        "left join avatar_avatar f on f.user_id = ab.id " + \
        "where aa.id = %s and ac.object_id = cast(aa.id as text) " + \
        "order by ac.created_on desc "

    with connection.cursor() as cursor:
        cursor.execute(sql, [summit.id])
        users_liked = dictfetchall(cursor)
    like_count = len(users_liked)

    sql = "select ab.id, ab.username, ac.id as comment_id, coalesce(replace(replace(f.avatar, '/', '/resized/100/'), 'avatars/resized/100/', 'avatars/'), 'img/default-user.png') as avatar_url, ac.created, ac.comment " + \
        "from items_summitlog aa, items_summitlogcomment ac " + \
        "join auth_user ab on ab.id = ac.user_id " + \
        "left join avatar_avatar f on f.user_id = ab.id " + \
        "where aa.id = %s and ac.summit_log_id = aa.id " + \
        "order by ac.created asc "

    with connection.cursor() as cursor:
        cursor.execute(sql, [summit.id])
        comments = dictfetchall(cursor)

    sql = "select a.* from items_summitlog a where a.user_id = %s and a.status = 1 and a.attempt = false and a.item_id = %s "
    with connection.cursor() as cursor:
        cursor.execute(sql, [summit.user.id, peak.id])

        user_summits_all = dictfetchall(cursor)
    user_summit_count_all = len(user_summits_all)

    sql = "select distinct b.id, b.name, b.slug_new_text, b.elevation, floor(b.elevation*.3048) as elevation_in_meters from items_summitlog a, items_item b where a.user_id = %s and a.status = 1 and a.attempt = false and a.item_id = b.id order by b.elevation desc "
    with connection.cursor() as cursor:
        cursor.execute(sql, [summit.user.id])

        user_summits_all_peaks = dictfetchall(cursor)
    user_summit_count_all_peaks = len(user_summits_all_peaks)

    sql = "select a.* from items_summitlog a where a.user_id = %s and a.status = 1 and a.attempt = false and a.item_id = %s and a.date <= %s "
    with connection.cursor() as cursor:
        cursor.execute(sql, [summit.user.id, peak.id, summit.date])

        user_summits = dictfetchall(cursor)
    user_summit_count = len(user_summits)

    sql = "select distinct(user_id) from items_summitlog where item_id = %s and date_entered = true and date_part('year', date) = date_part('year', CURRENT_DATE) "
    with connection.cursor() as cursor:
        cursor.execute(sql, [peak.id])

        summits_this_year = dictfetchall(cursor)
    summits_this_year_count = len(summits_this_year)

    if request.user.is_authenticated:
        sql = "select a.* from items_summitlog a where a.user_id = %s and a.status = 1 and a.attempt = false and a.item_id = %s "
        with connection.cursor() as cursor:
            cursor.execute(sql, [request.user.id, peak.id])

            your_summits = dictfetchall(cursor)
        your_summit_count = len(your_summits)

        sql = "select a.* from favorites_favorite a where a.user_id = %s and a.content_type_id = 18 and cast(a.object_id as integer) = %s "
        with connection.cursor() as cursor:
            cursor.execute(sql, [request.user.id, summit.id])

            request_user_liked = dictfetchall(cursor)
        request_user_like_count = len(request_user_liked)
        if request_user_like_count > 0:
            class_liked = 'liked'
        else:
            class_liked = ''

    else:
        your_summit_count = 0
        request_user_like_count = 0
        class_liked = ''

    if summit.trip_metadata:
        j = summit.trip_metadata
        if 'gear' in j:
            gear = j["gear"]
        else:
            gear = []

        if 'type' in j:
            trip_type = j["type"]
        else:
            trip_type = []

        if 'activities' in j:
            activities = j["activities"]
        else:
            activities = []

        if 'challenges' in j:
            challenges = j["challenges"]
        else:
            challenges = []
    else:
        gear = []
        trip_type = []
        activities = []
        challenges = []

    sql = "select " + \
        "c.id, " + \
        "c.username, " + \
        "coalesce(replace(replace(f.avatar, '/', '/resized/400/'), 'avatars/resized/400/', 'avatars/'), 'img/default-user.png') as avatar_url " + \
        "from items_companions a " + \
        "join accounts_userrelation b on b.id = a.user_relation_id " + \
        "join auth_user c on c.id = b.to_user_id " + \
        "left join avatar_avatar f on f.user_id = c.id " + \
        "where a.summit_log_id = %s " + \
        "group by c.id, c.username, replace(replace(f.avatar, '/', '/resized/400/'), 'avatars/resized/400/', 'avatars/') " + \
        "union select 0 as id, x.name as username, 'img/default-user.png' as avatar_url " + \
        "from items_summitfellowbagger x where x.summit_log_id = %s " + \
        "order by id asc "

    companions = User.objects.raw(sql, [summit.id, summit.id])

    related_links = SummitLogRelatedLink.objects.filter(summit_log_id=summit.id)
    link_list = []
    for r in related_links:
        try:
            link = get_link_preview_from_url(r.related_url)
            title = link.title or r.related_url  # Fallback to URL if title is None
            description = link.description or ""
            image = link.absolute_image  # May be None if no image is found

            # If no image is found, use OpenGraph to try to get the image
            if not image:
                image = link.opengraph.image

            # Create a dictionary with the extracted metadata
            related_link = {
                'title': title,
                'description': description,
                'image': image,
                'url': r.related_url
            }
            link_list.append(related_link)

        except Exception as e:
            pass  # Silently handle any errors

    related_videos = SummitLogVideo.objects.filter(summit_log_id=summit.id)
    from linkpreview import Link
    link_list = []

    for r in related_videos:
        try:
            # Use Link from linkpreview to fetch metadata
            link = Link(r.video_url)
            title = link.title or r.video_url  # Fallback to URL if title is None
            description = link.description or ""
            image = link.image  # May be None if no image is found

            # If no image is found, use OpenGraph to try to get the image
            if not image:
                from linkpreview import OpenGraph
                og = OpenGraph(r.video_url, ["og:image"])
                image = og.get("og:image")  # Fetch image from OpenGraph metadata

            # Create a dictionary with the extracted metadata
            related_link = {
                'title': title,
                'description': description,
                'image': image,
                'url': r.video_url
            }
            link_list.append(related_link)

        except Exception as e:
            pass  # Silently handle any errors

    #Need to get badges?
    badge_challenges = None
    last_badge_index = 0
    summit_badges = []
    if bagger.id == request.user.id:
        #if first time summited, get challenges for badges
        if user_summit_count_all == 1:
            sql = "select a.id, a.name, a.thumbnail, count(distinct c.item_id) as summited_peak_count, d.total_peak_count, ceil(cast(count(distinct c.item_id) as float) / cast(d.total_peak_count as float) * 100) as completion_pct " + \
                "from items_itemgroup a " + \
                "join items_itemgroupitem b on b.group_id = a.id " + \
                "join items_summitlog c on c.item_id = b.item_id and c.user_id = %s and c.attempt = false and c.status = 1 " + \
                "join (select x.group_id, count(x.item_id) as total_peak_count from items_itemgroupitem x group by x.group_id) d on d.group_id = a.id " + \
                "where exists (select 1 from items_itemgroupitem b where b.item_id = %s and b.group_id = a.id) " + \
                "group by a.id, a.name, a.thumbnail, d.total_peak_count "

            badge_challenges = ItemGroup.objects.raw(sql, [summit.user.id, peak.id])

        #get any badges
        is_first_ascent = False
        is_kom = False
        is_steward = False
        if peak.summitlog_count == 1 and first_ascent.get('username') == summit.user.username:
            badge = {}
            badge['type'] = 'first_ascent'
            summit_badges.append(badge)
            is_first_ascent = True
            #send notifications
            #followers = Follow.objects.get_followers_for_object(summit.user)
            followers = summit.user.person.get_followers_for_user()
            try:
                redis_queue.enqueue(send(followers,'following_new_first_ascent',{'user':summit.user, 'summit':summit , 'item':summit.item, 'summitlogId':summit.id}, on_site=True, sender=summit.user, summitlog=summit, summitlog_comment=None, receiver=None))
            except:
                pass

        if summit.attempt == False and not is_first_ascent:
            badge = {}
            badge['type'] = 'summary'
            badge['summit_count'] = user_summit_count_all
            if user_summit_count_all == 1:
                badge['summits_this_year_count'] = summits_this_year_count
            else:
                badge['summits_this_year_count'] = 0
            summit_badges.append(badge)

        if user_summit_count_all == 5:
            badge = {}
            badge['type'] = 'summit_steward'
            badge['steward_count'] = len(summit_stewards)
            summit_badges.append(badge)
            is_steward = True
            #send notifications
            #followers = Follow.objects.get_followers_for_object(summit.user)
            followers = summit.user.person.get_followers_for_user()
            try:
                redis_queue.enqueue(send(followers,'following_new_steward',{'user':summit.user, 'summit':summit , 'item':summit.item, 'summitlogId':summit.id}, on_site=True, sender=summit.user, summitlog=summit, summitlog_comment=None, receiver=None))
            except:
                pass

        if king_of_the_mountain.get('username') == summit.user.username and (int(king_of_the_mountain.get('summitlog_count')) - int(second_place_summits) == 1):
            badge = {}
            badge['type'] = 'king_of_the_mountain'
            summit_badges.append(badge)
            is_kom = True
            #send notifications
            if not is_first_ascent:
                #followers = Follow.objects.get_followers_for_object(summit.user)
                followers = summit.user.person.get_followers_for_user()
                try:
                    redis_queue.enqueue(send(followers,'following_new_kom',{'user':summit.user, 'summit':summit , 'item':summit.item, 'summitlogId':summit.id}, on_site=True, sender=summit.user, summitlog=summit, summitlog_comment=None, receiver=None))
                except:
                    pass

        if user_summit_count_all_peaks == 50:
            badge = {}
            badge['type'] = '50_peaks'
            summit_badges.append(badge)
        if user_summit_count_all_peaks == 100:
            badge = {}
            badge['type'] = '100_peaks'
            summit_badges.append(badge)
        if user_summit_count_all_peaks == 200:
            badge = {}
            badge['type'] = '200_peaks'
            summit_badges.append(badge)
        if user_summits_all_peaks:
            if peak.summitlog_count == 1 and user_summits_all_peaks[0]['id'] == summit.item.id:
                badge = {}
                if len(user_summits_all_peaks) > 1:
                    new_record_delta = float(user_summits_all_peaks[0]['elevation']) - float(user_summits_all_peaks[1]['elevation'])
                    new_record_delta_meters = float(user_summits_all_peaks[0]['elevation_in_meters']) - float(user_summits_all_peaks[1]['elevation_in_meters'])
                else:
                    new_record_delta = float(user_summits_all_peaks[0]['elevation'])
                    new_record_delta_meters = float(user_summits_all_peaks[0]['elevation_in_meters'])
                badge['type'] = 'elevation_pr'
                if len(user_summits_all_peaks) > 1:
                    badge['previous_record_peak'] = user_summits_all_peaks[1]['name']
                    badge['previous_record_elevation'] = user_summits_all_peaks[1]['elevation']
                else:
                    badge['previous_record_peak'] = 'None'
                    badge['previous_record_elevation'] = 0
                badge['new_record_delta'] = locale.format_string('%d', new_record_delta, grouping=True)
                badge['new_record_delta_meters'] = locale.format_string('%d', new_record_delta_meters, grouping=True)
                summit_badges.append(badge)

        if badge_challenges:
            for b in badge_challenges:
                finisher_count = 0
                for f in b.get_finishers(b.total_peak_count):
                    finisher_count = finisher_count + 1
                if b.completion_pct == 0:
                    completion_pct = 1
                elif b.completion_pct == 100 and b.summited_peak_count != b.total_peak_count:
                    completion_pct = 99
                else:
                    completion_pct = b.completion_pct
                badge = {}
                badge['type'] = 'challenge'
                badge['name'] = b.name
                badge['thumbnail'] = b.thumbnail
                badge['summited_peaks'] = b.summited_peak_count
                badge['total_peaks'] = b.total_peak_count
                badge['completion_pct'] = completion_pct
                badge['finisher_count'] = finisher_count
                summit_badges.append(badge)

        last_badge_index = len(summit_badges)-1

        #if need to send the summit log
        if not is_first_ascent and not is_kom and not is_steward:
            #followers = Follow.objects.get_followers_for_object(request.user)
            followers = request.user.person.get_followers_for_user()
            try:
                redis_queue.enqueue(send(followers,'new_following_summitlog',{'user':request.user, 'summit':summit , 'item':summit.item, 'summitlogId':summit.id}, on_site=True, sender=request.user, summitlog=summit, summitlog_comment=None, receiver=None))
            except:
                pass

    nav_page_name = peak.name
    request.session['show_badges'] = False
    other_summits = SummitLog.objects.filter(summitlog_group_id=summit.summitlog_group_id).order_by('id')

    return render(request, 'items/view_peak_summit.html', {
        'peak':peak,
        'subnav_summits_style': 'color: #F24100;',
        'summit':summit,
        'other_summits':other_summits,
        'avatar_url':avatar_url,
        'photos':photos,
        'users_liked':users_liked,
        'like_count':like_count,
        'comments':comments,
        'user_summits':user_summits,
        'user_summit_count':user_summit_count,
        'your_summit_count':your_summit_count,
        'gear':gear,
        'trip_type':trip_type,
        'activities':activities,
        'challenges':challenges,
        'companions':companions,
        'related_links':link_list,
        'request_user_like_count':request_user_like_count,
        'class_liked':class_liked,
        'summit_badges':summit_badges,
        'last_badge_index':last_badge_index,
        'nav_page_name':nav_page_name
    })

def peak_view_summits(request, item_slug):
    peak = get_object_or_404(Item.objects.filter(slug_new_text__exact=item_slug))

    alternate_names_list = peak.alternate_name_field.select_related('item').all()

    sql = "select a.id, a.name, count(b.*) as summit_count " + \
        "from items_peakroute a, items_summitlog b " + \
        "where b.item_id = %s " + \
        "and b.status = 1 " + \
        "and a.gpx_file is not null " + \
        "and b.peak_route_id = a.id " + \
        "and b.item_id = a.item_id " + \
        "group by a.id, a.name " + \
        "order by a.name asc "
    with connection.cursor() as cursor:
        cursor.execute(sql, [peak.id])
        routes = dictfetchall(cursor)

    if request.user.is_authenticated:
        sql = "select a.id, " + \
            "coalesce(d.summitlog_count, 0) as total_summits, " + \
            "coalesce(e.summitlog_count, 0) as your_summits, " + \
            "coalesce(f.summitlog_count, 0) as your_attempts, " + \
            "coalesce(g.challenge_count, 0) as challenge_count " + \
            "from items_item a " + \
            "left join (select d.item_id, count(d.id) as summitlog_count from items_summitlog d where d.status = 1 and d.attempt = false group by d.item_id) d on d.item_id = a.id " + \
            "left join (select e.item_id, count(e.id) as summitlog_count from items_summitlog e where e.status = 1 and e.attempt = false and e.user_id = %s group by e.item_id) e on e.item_id = a.id " + \
            "left join (select f.item_id, count(f.id) as summitlog_count from items_summitlog f where f.status = 1 and f.attempt = true and f.user_id = %s group by f.item_id) f on f.item_id = a.id " + \
            "left join (select g.item_id, count(g.id) as challenge_count from items_itemgroupitem g group by g.item_id) g on g.item_id = a.id " + \
            "where a.id = %s "

        with connection.cursor() as cursor:
            cursor.execute(sql, [request.user.id, request.user.id, peak.id])

            summit_stats = dictfetchall(cursor)
        total_summits_count = summit_stats[0]['total_summits']
        your_summits_count = summit_stats[0]['your_summits']
        your_attempts_count = summit_stats[0]['your_attempts']
        challenge_count = summit_stats[0]['challenge_count']
    else:
        total_summits_count = 0
        your_summits_count = 0
        your_attempts_count = 0
        challenge_count = 0

    subnav_summits_style = 'color: #F24100;'

    nav_page_name = peak.name

    return render(request, 'items/view_peak_summits.html', {
        'peak':peak,
        'alternate_names_list':alternate_names_list,
        'routes':routes,
        'subnav_summits_style':subnav_summits_style,
        'your_summit_count': your_summits_count,
        'total_summits_count': total_summits_count,
        'your_summits_count': your_summits_count,
        'your_attempts_count':your_attempts_count,
        'challenge_count': challenge_count,
        'nav_page_name':nav_page_name
    })

def summit_badge_peak(request, item_slug, summitlog_id):
    peak = get_object_or_404(Item.objects.filter(slug_new_text__exact=item_slug))
    summit = get_object_or_404(SummitLog.objects.filter(id=summitlog_id, item_id=peak.id))

    sql = "select a.* from items_summitlog a where a.user_id = %s and a.item_id = %s "
    with connection.cursor() as cursor:
        cursor.execute(sql, [summit.user.id, peak.id])
        user_summits = dictfetchall(cursor)
    user_summit_count = len(user_summits)

    sql = "select a.* from items_summitlog a where a.item_id = %s and date_part('year', created) = date_part('year', CURRENT_DATE) "
    with connection.cursor() as cursor:
        cursor.execute(sql, [peak.id])
        this_year_summits = dictfetchall(cursor)
    this_year_summit_count = len(this_year_summits)

    sql = "select a.* from items_summitlog a where a.item_id = %s and date_part('year', created) = date_part('year', CURRENT_DATE) and date_part('month', created) = date_part('month', CURRENT_DATE) "
    with connection.cursor() as cursor:
        cursor.execute(sql, [peak.id])
        this_month_summits = dictfetchall(cursor)
    this_month_summit_count = len(this_year_summits)

    now = datetime.datetime.now()

    return render(request, 'items/ajax/splash_badge_peak.html', {
        'peak':peak,
        'user_summit_count':user_summit_count,
        'this_year_summit_count':this_year_summit_count,
        'current_year':now.year,
        'this_month_summit_count':this_month_summit_count,
        'current_month':now.strftime('%B')
    })

def summit_badge_first_ascent(request, item_slug, summitlog_id):
    peak = get_object_or_404(Item.objects.filter(slug_new_text__exact=item_slug))

    return render(request, 'items/ajax/splash_badge_first_ascent.html', {
        'peak':peak
    })


def peak_view_route(request, item_slug, route_id):
    peak = get_object_or_404(Item.objects.filter(slug_new_text__exact=item_slug))

    sql = "select a.id, a.elevation_loss, a.max_elevation, a.start_elevation, a.route_metadata, a.gpx_geom::bytea, case when a.one_way = True then concat(a.name, ' (1-way)') else a.name end as name, a.slug, a.difficulty, a.distance_to_summit, a.total_distance, a.elevation_gain, a.gpx_file, a.gpx_start_index, a.gpx_end_index, coalesce(b.summit_count, 0) as summit_count, sum(c.total_trip_time) as total_time, count(c.id) as summmits_with_trip_time, sum(c.total_trip_time) / count(c.id) as avg_trip_time " + \
          "from items_peakroute a " + \
          "left join (select b.peak_route_id, count(b.id) as summit_count from items_summitlog b where b.item_id = %s and b.status = 1 and b.attempt = false group by b.peak_route_id)  as b on b.peak_route_id = a.id " + \
          "left join (select c.id, c.peak_route_id, c.total_trip_time from items_summitlog c where c.item_id = %s and c.status = 1 and c.total_trip_time > 0) as c on c.peak_route_id = a.id " + \
          "where a.item_id = %s and a.id = %s and coalesce(b.summit_count, 0) > 0 " + \
          "group by a.id, a.name, a.slug, a.difficulty, a.start_elevation, a.distance_to_summit, a.elevation_gain, a.elevation_loss, a.max_elevation, a.gpx_file, a.gpx_start_index, a.gpx_end_index, b.summit_count " + \
          "order by summit_count desc, a.name asc "

    with connection.cursor() as cursor:
        cursor.execute(sql, [peak.id, peak.id, peak.id, route_id])
        routes = dictfetchall(cursor)
    route = None
    for r in routes:
        route = r

    avg_time_to_summit = PeakRoute.get_avg_time_to_summit(peak.id, route_id)
    avg_total_trip_time = PeakRoute.get_avg_total_trip_time(peak.id, route_id)
    summits_count = PeakRoute.get_summits_count(peak.id, route_id)
    summits = PeakRoute.get_summits(peak.id, route_id)

    alternate_names_list = peak.alternate_name_field.select_related('item').all()

    sql = "select a.id, a.gpx_geom::bytea, case when a.one_way = True then concat(a.name, ' (1-way)') else a.name end as name, a.slug, a.difficulty, a.distance_to_summit, a.total_distance, a.elevation_gain, a.gpx_file, a.gpx_start_index, a.gpx_end_index, coalesce(b.summit_count, 0) as summit_count, sum(c.total_trip_time) as total_time, count(c.id) as summmits_with_trip_time, sum(c.total_trip_time) / count(c.id) as avg_trip_time " + \
          "from items_peakroute a " + \
          "left join (select b.peak_route_id, count(b.id) as summit_count from items_summitlog b where b.item_id = %s and b.status = 1 and b.attempt = false group by b.peak_route_id)  as b on b.peak_route_id = a.id " + \
          "left join (select c.id, c.peak_route_id, c.total_trip_time from items_summitlog c where c.item_id = %s and c.status = 1 and c.total_trip_time > 0) as c on c.peak_route_id = a.id " + \
          "where a.item_id = %s and a.status = 1 and a.gpx_file is not null and coalesce(b.summit_count, 0) > 0 " + \
          "group by a.id, a.name, a.slug, a.difficulty, a.distance_to_summit, a.elevation_gain, a.gpx_file, a.gpx_start_index, a.gpx_end_index, b.summit_count " + \
          "order by summit_count desc, a.name asc "

    with connection.cursor() as cursor:
        cursor.execute(sql, [peak.id, peak.id, peak.id])
        routes = dictfetchall(cursor)


    route_rank = 1
    color_rank = 1
    actual_rank = 1
    overall_rank = 1
    summmits_with_trip_time = 0
    last_summit_count = 0
    rank_color = ''
    counter = 1
    if routes:
        for r in routes:
            if r['summit_count'] != last_summit_count:
                actual_rank = counter
            if str(r['id']) == str(route_id):
                route_rank = counter
                color_rank = counter
                overall_rank = actual_rank
                summmits_with_trip_time = r['summmits_with_trip_time']
            counter += 1
            last_summit_count = r['summit_count']

    if route_rank > 7:
        color_rank = route_rank % 7

    if color_rank == 1:
        rank_color = '#fc202e'
    elif color_rank == 2:
        rank_color = '#f28300'
    elif color_rank == 3:
        rank_color = '#f2ca00'
    elif color_rank == 4:
        rank_color = '#00b330'
    elif color_rank == 5:
        rank_color = '#00b1f2'
    elif color_rank == 6:
        rank_color = '#8d00f2'
    elif color_rank == 7:
        rank_color = '#f200f2'

    sql = "select a.id, " + \
        "get_thumb(a.image, 910) as fullsize_url, " + \
        "get_thumb(a.image, 910) as thumbnail_url, " + \
        "a.caption, " + \
        "b.username, " + \
        "c.gpx_geom::bytea as gpx_geom, " + \
        "concat(to_char(a.created, 'YYYY'),'-',to_char(a.created, 'MM'),'-',to_char(a.created, 'DD')) as created, " + \
        "1 as sort_order " + \
        "from items_itemphoto a, auth_user b, items_summitlog c " + \
        "where a.item_id = %s " + \
        "and a.user_id = b.id " + \
        "and a.summit_log_id = c.id " + \
        "and c.peak_route_id = %s " + \
        "union " + \
        "select a.id, " + \
        "replace(concat(replace(replace(concat('images/',a.image),'routes','routes/cache'),'main','main/cache'),'.910x680_q95',lower(right(a.image,4))),'images/.910x680_q95','images/img/cache/default.png.350x245_q95_crop.jpg') as fullsize_url, " + \
        "replace(concat(replace(replace(concat('images/',a.image),'routes','routes/cache'),'main','main/cache'),'.910x680_q95',lower(right(a.image,4))),'images/.910x680_q95','images/img/cache/default.png.350x245_q95_crop.jpg') as thumbnail_url, " + \
        "'' as caption, " + \
        "'' as username, " + \
        "'' as gpx_geom, " + \
        "concat(to_char(a.created, 'YYYY'),'-',to_char(a.created, 'MM'),'-',to_char(a.created, 'DD')) as created, " + \
        "2 as sort_order " + \
        "from items_peakroutestep a " + \
        "where a.route_id = %s and length(a.image) > 0 " + \
        "order by sort_order asc, created desc "

    photos = ItemPhoto.objects.raw(sql, [peak.id, route_id, route_id])

    subnav_routes_style = 'color: #F24100;'

    if route:
        if route['route_metadata']:
            j = json.loads(route['route_metadata'])
            if 'types' in j:
                types = j["types"]
            else:
                types = []
        else:
            types = []
    else:
        types = []

    if request.user.is_authenticated:
        sql = "select a.id, " + \
            "coalesce(d.summitlog_count, 0) as total_summits, " + \
            "coalesce(e.summitlog_count, 0) as your_summits, " + \
            "coalesce(f.summitlog_count, 0) as your_attempts, " + \
            "coalesce(g.challenge_count, 0) as challenge_count " + \
            "from items_item a " + \
            "left join (select d.item_id, count(d.id) as summitlog_count from items_summitlog d where d.status = 1 and d.attempt = false group by d.item_id) d on d.item_id = a.id " + \
            "left join (select e.item_id, count(e.id) as summitlog_count from items_summitlog e where e.status = 1 and e.attempt = false and e.user_id = %s group by e.item_id) e on e.item_id = a.id " + \
            "left join (select f.item_id, count(f.id) as summitlog_count from items_summitlog f where f.status = 1 and f.attempt = true and f.user_id = %s group by f.item_id) f on f.item_id = a.id " + \
            "left join (select g.item_id, count(g.id) as challenge_count from items_itemgroupitem g group by g.item_id) g on g.item_id = a.id " + \
            "where a.id = %s "

        with connection.cursor() as cursor:
            cursor.execute(sql, [request.user.id, request.user.id, peak.id])

            summit_stats = dictfetchall(cursor)
        total_summits_count = summit_stats[0]['total_summits']
        your_summits_count = summit_stats[0]['your_summits']
        your_attempts_count = summit_stats[0]['your_attempts']
        challenge_count = summit_stats[0]['challenge_count']
    else:
        total_summits_count = 0
        your_summits_count = 0
        your_attempts_count = 0
        challenge_count = 0

    nav_page_name = peak.name

    return render(request, 'items/view_peak_route.html', {
        'peak':peak,
        'alternate_names_list':alternate_names_list,
        'subnav_routes_style':subnav_routes_style,
        'route':route,
        'route_rank':route_rank,
        'overall_rank':overall_rank,
        'summmits_with_trip_time':summmits_with_trip_time,
        'rank_color':rank_color,
        'photos':photos,
        'types':types,
        'avg_time_to_summit': avg_time_to_summit,
        'avg_total_trip_time': avg_total_trip_time,
        'summits_count': summits_count,
        'summits': summits,
        'your_summit_count': your_summits_count,
        'total_summits_count': total_summits_count,
        'your_summits_count': your_summits_count,
        'your_attempts_count':your_attempts_count,
        'challenge_count': challenge_count,
        'nav_page_name':nav_page_name,
        'fixed_subnav_class' : ''
    })

def peak_view_routes(request, item_slug):
    peak = get_object_or_404(Item.objects.filter(slug_new_text__exact=item_slug))

    alternate_names_list = peak.alternate_name_field.select_related('item').all()

    sql = "select a.id, a.name, a.slug, a.difficulty, a.distance_to_summit, a.elevation_gain, a.gpx_file, a.gpx_start_index, a.gpx_end_index, coalesce(b.summit_count, 0) as summit_count, sum(c.time_to_summit) as total_time, count(c.id) as summmits_with_trip_time, sum(c.time_to_summit) / count(c.id) as avg_time_to_summit " + \
        "from items_peakroute a " + \
        "left join (select b.peak_route_id, count(b.id) as summit_count from items_summitlog b where b.item_id = %s and b.status = 1 group by b.peak_route_id)  as b on b.peak_route_id = a.id " + \
        "left join (select c.id, c.peak_route_id, c.time_to_summit from items_summitlog c where c.item_id = %s and c.status = 1 and c.time_to_summit > 0) as c on c.peak_route_id = a.id " + \
        "where a.item_id = %s and a.status = 1 " + \
        "group by a.id, a.name, a.slug, a.difficulty, a.distance_to_summit, a.elevation_gain, a.gpx_file, a.gpx_start_index, a.gpx_end_index, b.summit_count " + \
        "order by summit_count desc, a.name asc "
    with connection.cursor() as cursor:
        cursor.execute(sql, [peak.id, peak.id, peak.id])
        routes = dictfetchall(cursor)

    if request.user.is_authenticated:
        sql = "select a.id, " + \
            "coalesce(d.summitlog_count, 0) as total_summits, " + \
            "coalesce(e.summitlog_count, 0) as your_summits, " + \
            "coalesce(f.summitlog_count, 0) as your_attempts, " + \
            "coalesce(g.challenge_count, 0) as challenge_count " + \
            "from items_item a " + \
            "left join (select d.item_id, count(d.id) as summitlog_count from items_summitlog d where d.status = 1 and d.attempt = false group by d.item_id) d on d.item_id = a.id " + \
            "left join (select e.item_id, count(e.id) as summitlog_count from items_summitlog e where e.status = 1 and e.attempt = false and e.user_id = %s group by e.item_id) e on e.item_id = a.id " + \
            "left join (select f.item_id, count(f.id) as summitlog_count from items_summitlog f where f.status = 1 and f.attempt = true and f.user_id = %s group by f.item_id) f on f.item_id = a.id " + \
            "left join (select g.item_id, count(g.id) as challenge_count from items_itemgroupitem g group by g.item_id) g on g.item_id = a.id " + \
            "where a.id = %s "

        with connection.cursor() as cursor:
            cursor.execute(sql, [request.user.id, request.user.id, peak.id])

            summit_stats = dictfetchall(cursor)
        total_summits_count = summit_stats[0]['total_summits']
        your_summits_count = summit_stats[0]['your_summits']
        your_attempts_count = summit_stats[0]['your_attempts']
        challenge_count = summit_stats[0]['challenge_count']
    else:
        total_summits_count = 0
        your_summits_count = 0
        your_attempts_count = 0
        challenge_count = 0

    subnav_routes_style = 'color: #F24100;'

    nav_page_name = peak.name

    meta_description = peak.get_peak_routes_meta_description(routes)

    return render(request, 'items/view_peak_routes.html', {
        'peak':peak,
        'alternate_names_list':alternate_names_list,
        'routes':routes,
        'subnav_routes_style':subnav_routes_style,
        'your_summit_count': your_summits_count,
        'total_summits_count': total_summits_count,
        'your_summits_count': your_summits_count,
        'your_attempts_count':your_attempts_count,
        'challenge_count': challenge_count,
        'nav_page_name':nav_page_name,
        'meta_description':meta_description
    })

def peak_view_map(request, item_slug):
    peak = get_object_or_404(Item.objects.filter(slug_new_text__exact=item_slug))

    alternate_names_list = peak.alternate_name_field.select_related('item').all()

    if request.user.is_authenticated:
        sql = "select a.id, " + \
            "coalesce(d.summitlog_count, 0) as total_summits, " + \
            "coalesce(e.summitlog_count, 0) as your_summits, " + \
            "coalesce(f.summitlog_count, 0) as your_attempts, " + \
            "coalesce(g.challenge_count, 0) as challenge_count " + \
            "from items_item a " + \
            "left join (select d.item_id, count(d.id) as summitlog_count from items_summitlog d where d.status = 1 and d.attempt = false group by d.item_id) d on d.item_id = a.id " + \
            "left join (select e.item_id, count(e.id) as summitlog_count from items_summitlog e where e.status = 1 and e.attempt = false and e.user_id = %s group by e.item_id) e on e.item_id = a.id " + \
            "left join (select f.item_id, count(f.id) as summitlog_count from items_summitlog f where f.status = 1 and f.attempt = true and f.user_id = %s group by f.item_id) f on f.item_id = a.id " + \
            "left join (select g.item_id, count(g.id) as challenge_count from items_itemgroupitem g group by g.item_id) g on g.item_id = a.id " + \
            "where a.id = %s "

        with connection.cursor() as cursor:
            cursor.execute(sql, [request.user.id, request.user.id, peak.id])

            summit_stats = dictfetchall(cursor)
        total_summits_count = summit_stats[0]['total_summits']
        your_summits_count = summit_stats[0]['your_summits']
        your_attempts_count = summit_stats[0]['your_attempts']
        challenge_count = summit_stats[0]['challenge_count']
    else:
        total_summits_count = 0
        your_summits_count = 0
        your_attempts_count = 0
        challenge_count = 0

    subnav_map_style = 'color: #F24100;'

    nav_page_name = peak.name

    return render(request, 'items/view_peak_map.html', {
        'peak':peak,
        'alternate_names_list':alternate_names_list,
        'subnav_map_style':subnav_map_style,
        'your_summit_count':your_summits_count,
        'your_summits_count':your_summits_count,
        'total_summits_count':total_summits_count,
        'your_attempts_count':your_attempts_count,
        'challenge_count':challenge_count,
        'nav_page_name':nav_page_name
    })


@login_required
def summit_comments(request, summit_id):
    summit_log = SummitLog.objects.get(id=summit_id)
    if request.method == 'POST':
        comment = request.POST.get('comment')
        summit_log_comment = SummitLogComment.objects.create(user=request.user, summit_log=summit_log, comment=comment)

        if summit_log.user != request.user:
            #Send the news to summitlog owner
            try:
                send([summit_log.user],'new_comment_summitlog',{'summit':summit_log,'item':summit_log.item,'user':request.user,'owner':summit_log.user,'comment':summit_log_comment}, on_site=True, sender=request.user, summitlog=summit_log, summitlog_comment=summit_log_comment)
            except:
                pass

        #Send the news that someone you follow commented in a summitlog, only if they haven't already commented on this summitlog
        sql = "select a.id " + \
            "from auth_user a " + \
            "join follow_follow b on b.follower_id = a.id " + \
            "where b.user_id = %s " + \
            "and a.id != %s " + \
            "and not exists (select 1 from items_summitlogcomment x where x.summit_log_id = %s and x.user_id = a.id)"
        recipients = User.objects.raw(sql,[request.user.id, summit_log.user.id, summit_log.id])
        try:
            send(recipients,'new_comment_following_summitlog',{'summit':summit_log,'item':summit_log.item,'user_comment':summit_log.user,'user':request.user,'comment':summit_log_comment}, on_site=True, sender=request.user, summitlog=summit_log, summitlog_comment=summit_log_comment)
        except:
            pass

        #Send the news to the other users that commented before: Exclude the owner of the summitlog and the commenter
        sql = "select distinct a.id " + \
            "from auth_user a " + \
            "join items_summitlogcomment b on b.user_id = a.id " + \
            "where b.summit_log_id = %s " + \
            "and a.id != %s " + \
            "and a.id != %s "
        recipients = User.objects.raw(sql,[summit_log.id, summit_log.user.id, request.user.id])
        try:
            send(recipients,'non_following_comment_summitlog',{'summit':summit_log,'item':summit_log.item,'user_comment':summit_log.user,'user':request.user,'comment':summit_log_comment}, on_site=True, sender=request.user, summitlog=summit_log, summitlog_comment=summit_log_comment)
        except:
            pass

        return render(request, 'summits/ajax/added_comment.html', {'comment':summit_log_comment})
    else:
        comment = request.GET.get('comment')
        summit_log_comment = SummitLogComment.objects.create(user=request.user, summit_log=summit_log, comment=comment)
        messages.success(request, 'Comment added')
        return render(request, "summits/comment_added.html",{'summit_log':summit_log})


def summit_view(request, summit_id):
    summit = get_object_or_404(SummitLog, id=summit_id)
    return HttpResponseRedirect(reverse('peak_view_summit', kwargs={'item_slug':summit.item.slug_new_text,'summitlog_id':summit.id}))


@login_required
def summit_edit(request, item_slug, summitlog_id):

    summit = SummitLog.objects.get(id=summitlog_id)
    if summit.user != request.user:
        raise Http404
    peak = summit.item
    photos = ItemPhoto.objects.filter(summit_log=summit).order_by('id')
    photo_minsize = [settings.MIN_SUMMIT_IMG_WIDTH, settings.MIN_SUMMIT_IMG_HEIGHT]

    sql = "select " + \
        "c.id, " + \
        "c.username, " + \
        "coalesce(replace(replace(f.avatar, '/', '/resized/400/'), 'avatars/resized/400/', 'avatars/'), 'img/default-user.png') as avatar_url " + \
        "from items_companions a " + \
        "join accounts_userrelation b on b.id = a.user_relation_id " + \
        "join auth_user c on c.id = b.to_user_id " + \
        "left join avatar_avatar f on f.user_id = c.id " + \
        "where a.summit_log_id = %s " + \
        "group by c.id, c.username, replace(replace(f.avatar, '/', '/resized/400/'), 'avatars/resized/400/', 'avatars/') " + \
        "union select 0 as id, x.name as username, 'img/default-user.png' as avatar_url " + \
        "from items_summitfellowbagger x where x.summit_log_id = %s " + \
        "order by id asc "

    companions = User.objects.raw(sql, [summit.id, summit.id])

    if request.method == 'POST':
        #process step 2
        date = request.POST.get('summit-date')
        date_entered = True
        log = request.POST.get('log')
        if log=='':log=None
        if date=='':
            date = datetime.date.today()
            date_entered = False
        summit.status = STATUS_SUMMIT_ACTIVE
        summit.log = log
        summit.date = date
        summit.date_entered = date_entered
        if request.POST.getlist('rdoOutcome')[0] == '1':
            summit.attempt = True
        else:
            summit.attempt = False

        photos = ItemPhoto.objects.filter(summit_log=summit).order_by('id')
        for p in photos:
            photo_element = 'photo%s' % p.id
            if request.POST.get(photo_element) == '' or not request.POST.get(photo_element):
                photo = ItemPhoto.objects.get(id=p.id)
                photo.delete()
            else:
                caption_element = 'caption%s' % p.id
                photo_index_element = 'photoindex%s' % p.id
                if request.POST.get(caption_element) != '':
                    photo = ItemPhoto.objects.get(id=p.id)
                    photo.caption = request.POST.get(caption_element)
                    photo.user = request.user
                    photo.item = summit.item
                    photo.photo_index = request.POST.get(photo_index_element)
                    photo.save()
                else:
                    photo = ItemPhoto.objects.get(id=p.id)
                    photo.user = request.user
                    photo.item = summit.item
                    photo.photo_index = request.POST.get(photo_index_element)
                    photo.save()

        gpx_file = request.POST.get('gpx-file',None)
        summit.gpx_file = gpx_file

        if request.POST.get('distance-to-summit') != '' and request.POST.get('distance-to-summit-units') == 'km':
            distance_to_summit = float(request.POST.get('distance-to-summit'))/1.60934
            summit.distance_to_summit = distance_to_summit
        elif request.POST.get('distance-to-summit') != '':
            distance_to_summit = request.POST.get('distance-to-summit')
            summit.distance_to_summit = distance_to_summit
        elif request.POST.get('distance-to-summit') == '':
            summit.distance_to_summit = None

        if request.POST.get('total-trip-distance') != '' and request.POST.get('total-trip-distance-units') == 'km':
            total_distance = float(request.POST.get('total-trip-distance'))/1.60934
            summit.total_distance = total_distance
        elif request.POST.get('total-trip-distance') != '':
            total_distance = request.POST.get('total-trip-distance')
            summit.total_distance = total_distance
        elif request.POST.get('total-trip-distance') == '':
            summit.total_distance = None

        if request.POST.get('elevation-start') != '' and request.POST.get('elevation-start-units') == 'm':
            elevation_start = float(request.POST.get('elevation-start'))/0.3048
            summit.start_elevation = elevation_start
        elif request.POST.get('elevation-start') != '':
            elevation_start = request.POST.get('elevation-start')
            summit.start_elevation = elevation_start
        elif request.POST.get('elevation-start') == '':
            summit.start_elevation = None

        if request.POST.get('elevation-gain') != '' and request.POST.get('elevation-gain-units') == 'm':
            elevation_gain = float(request.POST.get('elevation-gain'))/0.3048
            summit.elevation_gain = elevation_gain
        elif request.POST.get('elevation-gain') != '':
            elevation_gain = request.POST.get('elevation-gain')
            summit.elevation_gain = elevation_gain
        elif request.POST.get('elevation-gain') == '':
            summit.elevation_gain = None

        time_to_summit = 0
        if request.POST.get('time-to-summit-min') != '':
            time_to_summit = 60*float(request.POST.get('time-to-summit-min'))
        if request.POST.get('time-to-summit-hrs') != '':
            time_to_summit = time_to_summit + (3600*float(request.POST.get('time-to-summit-hrs')))
        if time_to_summit > 0:
            summit.time_to_summit = time_to_summit
        else:
            summit.time_to_summit = None

        total_trip_time = 0
        if request.POST.get('total-trip-time-min') != '':
            total_trip_time = 60*float(request.POST.get('total-trip-time-min'))
        if request.POST.get('total-trip-time-hrs') != '':
            total_trip_time = total_trip_time + (3600*float(request.POST.get('total-trip-time-hrs')))
        if total_trip_time > 0:
            summit.total_trip_time = total_trip_time
        else:
            summit.total_trip_time = None

        trip_metadata = '{"type":%s, "activities":%s, "challenges":%s, "gear":%s}' % (json.dumps(request.POST.getlist('rdoTripType')), json.dumps(request.POST.getlist('chkActivityTags')), json.dumps(request.POST.getlist('chkChallengeTags')), json.dumps(request.POST.getlist('chkGearTags')))
        summit.trip_metadata = trip_metadata

        summit.quick_save()

        #update route if gpx file uploaded
        if gpx_file != '':
            #update the route with gpx file
            encoded_mapbox_polyline = mapbox_thumbnail_service.get_encoded_polyline_for_mapbox(gpx_file)
            sql = "update items_peakroute set gpx_file = %s, " + \
                  "gpx_start_index = 0, gpx_end_index = 500, " + \
                  "distance_to_summit = items_summitlog.distance_to_summit, " + \
                  "start_elevation = items_summitlog.start_elevation, " + \
                  "elevation_gain = items_summitlog.elevation_gain, " + \
                  "gpx_geom = items_summitlog.gpx_geom, " + \
                  "encoded_polyline = %s, " + \
                  "total_distance = items_summitlog.total_distance, " + \
                  "elevation_loss = items_summitlog.elevation_loss, " + \
                  "max_elevation = items_summitlog.max_elevation " + \
                  "from items_summitlog " + \
                  "where items_peakroute.id = %s and items_summitlog.id = %s "
            with connection.cursor() as cursor:
                cursor.execute(sql, [gpx_file, encoded_mapbox_polyline, summit.peak_route_id, summit.id])

        cache_path = '/members/%s/' % request.user.username
        cache_manager.invalidate_user_cache(cache_path, request.user.id)
        from peakery.accounts.utils import update_member_profile_cache
        from urllib.parse import urlparse
        redis_queue.enqueue(update_member_profile_cache, request.user)

        #Companions.objects.get(summit_log=summit).delete()
        sql = "delete from items_companions where summit_log_id = %s "
        with connection.cursor() as cursor:
            cursor.execute(sql, [summit.id])
        sql = "delete from items_summitfellowbagger where summit_log_id = %s "
        with connection.cursor() as cursor:
            cursor.execute(sql, [summit.id])

        userrelations = request.POST.get('fellow_selected_users','')
        if userrelations:
            userrelations = userrelations.split(',')
            userrelations = [ int(x[1:-1]) for x in userrelations if x]
            relations_to_add = userrelations
            for userrelation in relations_to_add:
                if userrelation:
                    to_user = User.objects.get(id=userrelation)
                    exists = UserRelation.check_relation(request.user, to_user, None, None)
                    if exists:
                        newrelation = exists
                    else:
                        newrelation = UserRelation(from_user=request.user, to_user=to_user, first_name=to_user.first_name, last_name=to_user.last_name, source=2)
                        newrelation.save()
                    companion = Companions(summit_log = summit, user = request.user, user_relation = newrelation)
                    companion.save()

        nonmember_baggers = request.POST.get('fellow_nonmember_baggers','')
        if nonmember_baggers:
            nonmember_baggers = nonmember_baggers.split(',')
            baggers_to_add = nonmember_baggers
            for bagger in baggers_to_add:
                if bagger:
                    sql = "insert into items_summitfellowbagger (summit_log_id, name, resolved_relation) values (%s, %s, False) "
                    with connection.cursor() as cursor:
                        cursor.execute(sql, [summit.id, bagger[1:-1]])

        SummitLogVideo.objects.filter(summit_log_id=summit.id).delete()
        summitvideos = request.POST.get('summit_video_links','')
        if summitvideos:
            from peakery.items.utils import save_video_data_from_webpage
            summitvideos = summitvideos.split(',')
            summitvideos = [ x[1:-1] for x in summitvideos if x]
            videos_to_add = summitvideos
            for video in videos_to_add:
                if video:
                    summit_video = SummitLogVideo.objects.create(summit_log_id=summit.id, video_url=video)
                    redis_queue.enqueue(save_video_data_from_webpage, summit_video.id)

        SummitLogRelatedLink.objects.filter(summit_log_id=summit.id).delete()
        summitlinks = request.POST.get('summit_related_links','')
        if summitlinks:
            from peakery.items.utils import save_related_link_data_from_webpage
            summitlinks = summitlinks.split(',')
            summitlinks = [ x[1:-1] for x in summitlinks if x]
            links_to_add = summitlinks
            for link in links_to_add:
                if link:
                    related_link = SummitLogRelatedLink.objects.create(summit_log_id=summit.id, related_url=link)
                    redis_queue.enqueue(save_related_link_data_from_webpage, related_link.id)

        return HttpResponseRedirect(reverse('peak_view_summit', kwargs={'item_slug':summit.item.slug_new_text,'summitlog_id':summitlog_id}))
    else:
        routes = peak.get_summits_routes_list()

        related_links = SummitLogRelatedLink.objects.filter(summit_log_id=summit.id)
        link_list = []
        for r in related_links:
            related_link = {}
            related_link['title'] = r.title
            related_link['description'] = r.description
            related_link['image'] = r.image
            related_link['url'] = r.related_url
            link_list.append(related_link)

        related_videos = SummitLogVideo.objects.filter(summit_log_id=summit.id)
        video_list = []
        for r in related_videos:
            related_link = {}
            related_link['title'] = r.title
            related_link['description'] = r.description
            related_link['image'] = r.image
            related_link['url'] = r.video_url
            video_list.append(related_link)

        if routes:
            routes = 'true'
        else:
            routes = 'false'

    return render(request, 'items/summit_2_edit.html', {
        'photo_minsize':photo_minsize,
        'peak':peak,
        'summit':summit,
        'routes':routes,
        'photos':photos,
        'companions':companions,
        'videos':video_list,
        'related_links':link_list,
        'companions':companions
    })


#@login_required
def log_climb_multi_simple(request):

    photo_minsize = [settings.MIN_SUMMIT_IMG_WIDTH, settings.MIN_SUMMIT_IMG_HEIGHT]
    init_gpx_file = None
    default_route_name = False
    if request.method == 'POST':
        import time
        start_time = time.time()
        from urllib.parse import urlparse
        from peakery.items.utils import save_related_link_data_from_webpage
        from peakery.items.utils import save_video_data_from_webpage
        summit_id = request.POST.get('summit_id')
        init_summit_ids = request.POST.get('summit_ids', None)
        summit = SummitLog.objects.get(id=summit_id)
        category = ItemPhotoCategory.objects.get(name='summit')

        first_summit_distance_to_summit = 0
        first_summit_total_distance = 0
        first_summit_start_elevation = 0
        first_summit_elevation_gain = 0
        first_summit_time_to_summit = 0
        first_summit_total_trip_time = 0
        first_summit_gpx_geom = None
        first_summit_elevation_loss = 0
        first_summit_max_elevation = 0

        climbed_peak_ids = request.POST.get('climbed_peak_ids', None)
        if climbed_peak_ids:
            multi_peak_summit = False
            #did we start with initial summit logs?
            if init_summit_ids:
                #just update the initial summit logs
                #first get the summits
                summits = init_summit_ids.split(",")

                #save first summit
                first_summit = summit
                first_created_summit = None

                #save first peak
                first_peak = None
                gpx_date = request.POST.get('summit-date', None)
                gpx_file = request.POST.get('summit-gpx-file', None)

                sql = "select a.id, b.id as summit_log_id, b.date as summit_log_date, b.gpx_file, b.distance_to_summit, b.total_distance, " + \
                    "b.start_elevation, b.elevation_gain, b.time_to_summit, b.total_trip_time, b.gpx_geom, b.encoded_polyline, b.elevation_loss, b.max_elevation " + \
                    "from items_item a, items_summitlog b " + \
                    "where cast(b.id as varchar) = any(%s) and b.item_id = a.id "

                peaks = Item.objects.raw(sql, [summits])

                climbed_peak_ids = climbed_peak_ids.split(',')
                if len(climbed_peak_ids) > 1:
                    multi_peak_summit = True
                    #create group for these summits
                    summit_log_group = SummitLogGroup(user=request.user)
                    summit_log_group.save()
                for climbed_peak in climbed_peak_ids:
                    peak_id = climbed_peak
                    peak = Item.objects.get(id=peak_id)
                    #was this peak part of the init summits?
                    matched_peak = False
                    for p in peaks:
                        if str(p.id) == str(peak_id):
                            summit = SummitLog.objects.get(id=p.summit_log_id)
                            summit.status = STATUS_SUMMIT_ACTIVE
                            matched_peak = True

                            # save first summit stats
                            first_summit_distance_to_summit = p.distance_to_summit
                            first_summit_total_distance = p.total_distance
                            first_summit_start_elevation = p.start_elevation
                            first_summit_elevation_gain = p.elevation_gain
                            first_summit_time_to_summit = p.time_to_summit
                            first_summit_total_trip_time = p.total_trip_time
                            first_summit_gpx_geom = p.gpx_geom
                            first_summit_encoded_polyline = p.encoded_polyline
                            first_summit_elevation_loss = p.elevation_loss
                            first_summit_max_elevation = p.max_elevation

                            if not first_peak:
                                first_peak = p

                    if not matched_peak:
                        import polyline
                        import gpxpy.gpx
                        f = default_storage.open(gpx_file, 'r')
                        gpx = gpxpy.parse(f)
                        reduce_gpx_points(gpx)
                        points = []
                        if gpx.tracks:
                            for track in gpx.tracks:
                                for segment in track.segments:
                                    for point in segment.points:
                                        points.append((point.latitude, point.longitude))
                        elif gpx.routes:
                            for route in gpx.routes:
                                for point in route.points:
                                    points.append([point.latitude, point.longitude])
                        first_summit_encoded_polyline = polyline.encode(points, 5)
                        summit = SummitLog(user=request.user, status=STATUS_SUMMIT_ACTIVE, date=gpx_date, gpx_file=gpx_file, distance_to_summit=first_summit_distance_to_summit, total_distance=first_summit_total_distance, start_elevation=first_summit_start_elevation, elevation_gain=first_summit_elevation_gain, time_to_summit=first_summit_time_to_summit, total_trip_time=first_summit_total_trip_time, gpx_geom=first_summit_gpx_geom, encoded_polyline=first_summit_encoded_polyline, elevation_loss=first_summit_elevation_loss, max_elevation=first_summit_max_elevation)
                        summit.item = peak
                        if not first_peak:
                            first_peak = peak
                        summit.date = gpx_date
                        summit.gpx_file = gpx_file
                        summit.date_entered = True

                    log = request.POST.get('log')
                    if log == '':
                        log = None
                    summit.log = log

                    outcomeElement = 'rdoOutcome%s' % peak_id
                    if request.POST.getlist(outcomeElement)[0] == '1':
                        summit.attempt = True
                    else:
                        summit.attempt = False

                    photos = ItemPhoto.objects.filter(summit_log=first_summit).order_by('id')
                    for p in photos:
                        photo_element = 'photo%s' % p.id
                        caption_element = 'caption%s' % p.id
                        photo_index_element = 'photoindex%s' % p.id
                        if request.POST.get(photo_element) == '' or not request.POST.get(photo_element):
                            photo = ItemPhoto.objects.get(id=p.id)
                            photo.delete()
                        else:
                            if request.POST.get(caption_element) != '':
                                photo = ItemPhoto(caption=request.POST.get(caption_element), user=request.user, item=peak, summit_log_id=summit.id, category=category, image=p.image, uuid=p.uuid, photo_index=request.POST.get(photo_index_element))
                                photo.save()
                            else:
                                photo = ItemPhoto(user=request.user, item=peak, summit_log_id=summit.id, category=category, image=p.image, uuid=p.uuid, photo_index=request.POST.get(photo_index_element))
                                photo.save()

                    trip_metadata = '{"type":%s, "activities":%s, "challenges":%s, "gear":%s}' % (json.dumps(request.POST.getlist('rdoTripType')), json.dumps(request.POST.getlist('chkActivityTags')), json.dumps(request.POST.getlist('chkChallengeTags')), json.dumps(request.POST.getlist('chkGearTags')))
                    summit.trip_metadata = trip_metadata
                    summit.multi_peak_summit = multi_peak_summit
                    if multi_peak_summit:
                        summit.summitlog_group_id = summit_log_group.id
                    summit.quick_save()
                    #save the summit group
                    if multi_peak_summit:
                        SummitLogGroupSummit.objects.create(summit=summit, group=summit_log_group)
                    #update summitlog count for item
                    summitlog_count = SummitLog.objects.filter(item=peak, attempt=False, status=STATUS_SUMMIT_ACTIVE).count()
                    peak.summitlog_count = summitlog_count
                    peak.save_summitlog_count()

                    if not first_created_summit:
                        first_created_summit = summit

                    userrelations = request.POST.get('fellow_selected_users','')
                    if userrelations:
                        userrelations = userrelations.split(',')
                        userrelations = [ int(x[1:-1]) for x in userrelations if x]
                        relations_to_add = userrelations
                        for userrelation in relations_to_add:
                            if userrelation:
                                to_user = User.objects.get(id=userrelation)
                                exists = UserRelation.check_relation(request.user, to_user, None, None)
                                if exists:
                                    newrelation = exists
                                else:
                                    newrelation = UserRelation(from_user=request.user, to_user=to_user, first_name=to_user.first_name, last_name=to_user.last_name, source=2)
                                    newrelation.save()
                                companion = Companions(summit_log = summit, user = request.user, user_relation = newrelation)
                                companion.save()
                                companion_user = User.objects.filter(id=userrelation)
                                if companion_user:
                                    for c in companion_user:
                                        try:
                                            redis_queue.enqueue(send(companion_user,'user_added_to_summit_log',{'user':request.user, 'summit':summit , 'item':peak, 'summitlogId':summit_id, 'summit_date':gpx_date}, on_site=True, sender=request.user, summitlog=summit, summitlog_comment=None, receiver=c))
                                        except:
                                            pass

                    nonmember_baggers = request.POST.get('fellow_nonmember_baggers','')
                    if nonmember_baggers:
                        nonmember_baggers = nonmember_baggers.split(',')
                        baggers_to_add = nonmember_baggers
                        for bagger in baggers_to_add:
                            if bagger:
                                sql = "insert into items_summitfellowbagger (summit_log_id, name, resolved_relation) values (%s, %s, False) "
                                with connection.cursor() as cursor:
                                    cursor.execute(sql, [summit.id, bagger[1:-1]])

                    summitvideos = request.POST.get('summit_video_links','')
                    if summitvideos:
                        summitvideos = summitvideos.split(',')
                        summitvideos = [ x[1:-1] for x in summitvideos if x]
                        videos_to_add = summitvideos
                        for video in videos_to_add:
                            if video:
                                summit_video = SummitLogVideo.objects.create(summit_log_id=summit_id, video_url=video)
                                redis_queue.enqueue(save_video_data_from_webpage, summit_video.id)

                    summitlinks = request.POST.get('summit_related_links','')
                    process_summit_links(summit_id, summitlinks)

                #reset first summit to first actually created, for redirect purposes
                first_summit = first_created_summit
            else:
                climbed_peak_ids = climbed_peak_ids.split(',')
                if len(climbed_peak_ids) > 1:
                    multi_peak_summit = True
                    #create group for these summits
                    summit_log_group = SummitLogGroup(user=request.user)
                    summit_log_group.save()
                peak_id = climbed_peak_ids[0]
                peak = Item.objects.get(id=peak_id)
                summit.item = peak

                #save first peak and summit
                first_summit = summit
                first_peak = peak

                #save summit log(s)
                date = request.POST.get('summit-date')
                date_entered = True
                log = request.POST.get('log')
                if log=='':log=None
                if date=='':
                    date = datetime.date.today()
                    date_entered = False

                summit.status = STATUS_SUMMIT_ACTIVE
                summit.log = log
                summit.date = date
                summit.date_entered = date_entered
                outcomeElement = 'rdoOutcome%s' % peak_id
                if request.POST.getlist(outcomeElement)[0] == '1':
                    summit.attempt = True
                else:
                    summit.attempt = False

                #photos are already assigned to the first summit, just update them
                photos = ItemPhoto.objects.filter(summit_log=summit).order_by('id')
                for p in photos:
                    photo_element = 'photo%s' % p.id
                    if request.POST.get(photo_element) == '' or not request.POST.get(photo_element):
                        photo = ItemPhoto.objects.get(id=p.id)
                        photo.delete()
                    else:
                        caption_element = 'caption%s' % p.id
                        photo_index_element = 'photoindex%s' % p.id
                        if request.POST.get(caption_element) != '':
                            photo = ItemPhoto.objects.get(id=p.id)
                            photo.caption = request.POST.get(caption_element)
                            photo.user = request.user
                            photo.item = peak
                            photo.photo_index = request.POST.get(photo_index_element)
                            photo.save()
                        else:
                            photo = ItemPhoto.objects.get(id=p.id)
                            photo.user = request.user
                            photo.item = peak
                            photo.photo_index = request.POST.get(photo_index_element)
                            photo.save()

                trip_metadata = '{"type":%s, "activities":%s, "challenges":%s, "gear":%s}' % (json.dumps(request.POST.getlist('rdoTripType')), json.dumps(request.POST.getlist('chkActivityTags')), json.dumps(request.POST.getlist('chkChallengeTags')), json.dumps(request.POST.getlist('chkGearTags')))
                summit.trip_metadata = trip_metadata
                summit.multi_peak_summit = multi_peak_summit
                if multi_peak_summit:
                    summit.summitlog_group_id = summit_log_group.id

                manual_route_name = request.POST.get('manual-summit-route-name')
                if manual_route_name != '':
                    summit.peak_route_name = manual_route_name

                summit.quick_save()
                #save the summit group
                if multi_peak_summit:
                    SummitLogGroupSummit.objects.create(summit=summit, group=summit_log_group)
                #update summitlog count for item
                summitlog_count = SummitLog.objects.filter(item=peak, attempt=False, status=STATUS_SUMMIT_ACTIVE).count()
                peak.summitlog_count = summitlog_count
                peak.save_summitlog_count()

                userrelations = request.POST.get('fellow_selected_users','')
                if userrelations:
                    userrelations = userrelations.split(',')
                    userrelations = [ int(x[1:-1]) for x in userrelations if x]
                    relations_to_add = userrelations
                    for userrelation in relations_to_add:
                        if userrelation:
                            to_user = User.objects.get(id=userrelation)
                            exists = UserRelation.check_relation(request.user, to_user, None, None)
                            if exists:
                                newrelation = exists
                            else:
                                newrelation = UserRelation(from_user=request.user, to_user=to_user, first_name=to_user.first_name, last_name=to_user.last_name, source=2)
                                newrelation.save()
                            companion = Companions(summit_log = summit, user = request.user, user_relation = newrelation)
                            companion.save()
                            companion_user = User.objects.filter(id=userrelation)
                            if companion_user:
                                for c in companion_user:
                                    try:
                                        redis_queue.enqueue(send(companion_user,'user_added_to_summit_log',{'user':request.user, 'summit':summit , 'item':peak, 'summitlogId':summit_id, 'summit_date':date}, on_site=True, sender=request.user, summitlog=summit, summitlog_comment=None, receiver=c))
                                    except:
                                        pass

                nonmember_baggers = request.POST.get('fellow_nonmember_baggers','')
                if nonmember_baggers:
                    nonmember_baggers = nonmember_baggers.split(',')
                    baggers_to_add = nonmember_baggers
                    for bagger in baggers_to_add:
                        if bagger:
                            sql = "insert into items_summitfellowbagger (summit_log_id, name, resolved_relation) values (%s, %s, False) "
                            with connection.cursor() as cursor:
                                cursor.execute(sql, [summit.id, bagger[1:-1]])

                summitvideos = request.POST.get('summit_video_links','')
                if summitvideos:
                    summitvideos = summitvideos.split(',')
                    summitvideos = [ x[1:-1] for x in summitvideos if x]
                    videos_to_add = summitvideos
                    for video in videos_to_add:
                        if video:
                            summit_video = SummitLogVideo.objects.create(summit_log_id=summit_id, video_url=video)
                            redis_queue.enqueue(save_video_data_from_webpage, summit_video.id)

                summitlinks = request.POST.get('summit_related_links','')
                process_summit_links(summit_id, summitlinks)

                #any other peaks to save?
                if len(climbed_peak_ids) > 1:
                    remaining_climbed_peaks = climbed_peak_ids[1:]
                    for climbed_peak in remaining_climbed_peaks:
                        peak_id = climbed_peak
                        peak = Item.objects.get(id=peak_id)
                        summit = SummitLog(user=request.user, status=STATUS_SUMMIT_ACTIVE)
                        summit.item = peak

                        #save summit log(s)
                        date = request.POST.get('summit-date')
                        date_entered = True
                        log = request.POST.get('log')
                        if log=='':log=None
                        if date=='':
                            date = datetime.date.today()
                            date_entered = False

                        summit.log = log
                        summit.date = date
                        summit.date_entered = date_entered
                        outcomeElement = 'rdoOutcome%s' % peak_id
                        if request.POST.getlist(outcomeElement)[0] == '1':
                            summit.attempt = True
                        else:
                            summit.attempt = False

                        trip_metadata = '{"type":%s, "activities":%s, "challenges":%s, "gear":%s}' % (json.dumps(request.POST.getlist('rdoTripType')), json.dumps(request.POST.getlist('chkActivityTags')), json.dumps(request.POST.getlist('chkChallengeTags')), json.dumps(request.POST.getlist('chkGearTags')))
                        summit.trip_metadata = trip_metadata
                        summit.multi_peak_summit = multi_peak_summit
                        if multi_peak_summit:
                            summit.summitlog_group_id = summit_log_group.id

                        if manual_route_name != '':
                            summit.peak_route_name = manual_route_name

                        summit.quick_save()
                        #save the summit group
                        if multi_peak_summit:
                            SummitLogGroupSummit.objects.create(summit=summit, group=summit_log_group)

                        #get photos from the first summit and add them for this summit
                        photos = ItemPhoto.objects.filter(summit_log=first_summit).order_by('id')
                        for p in photos:
                            photo_element = 'photo%s' % p.id
                            caption_element = 'caption%s' % p.id
                            photo_index_element = 'photoindex%s' % p.id
                            if request.POST.get(photo_element) == '' or not request.POST.get(photo_element):
                                photo = ItemPhoto.objects.get(id=p.id)
                                photo.delete()
                            else:
                                if request.POST.get(caption_element) != '':
                                    photo = ItemPhoto(caption=request.POST.get(caption_element), user=request.user, item=peak, summit_log_id=summit.id, category=category, image=p.image, uuid=p.uuid, photo_index=request.POST.get(photo_index_element))
                                    photo.save()
                                else:
                                    photo = ItemPhoto(user=request.user, item=peak, summit_log_id=summit.id, category=category, image=p.image, uuid=p.uuid, photo_index=request.POST.get(photo_index_element))
                                    photo.save()

                        #update summitlog count for item
                        summitlog_count = SummitLog.objects.filter(item=peak, attempt=False, status=STATUS_SUMMIT_ACTIVE).count()
                        peak.summitlog_count = summitlog_count
                        peak.save_summitlog_count()

                        userrelations = request.POST.get('fellow_selected_users','')
                        if userrelations:
                            userrelations = userrelations.split(',')
                            userrelations = [ int(x[1:-1]) for x in userrelations if x]
                            relations_to_add = userrelations
                            for userrelation in relations_to_add:
                                if userrelation:
                                    to_user = User.objects.get(id=userrelation)
                                    exists = UserRelation.check_relation(request.user, to_user, None, None)
                                    if exists:
                                        newrelation = exists
                                    else:
                                        newrelation = UserRelation(from_user=request.user, to_user=to_user, first_name=to_user.first_name, last_name=to_user.last_name, source=2)
                                        newrelation.save()
                                    companion = Companions(summit_log = summit, user = request.user, user_relation = newrelation)
                                    companion.save()
                                    companion_user = User.objects.filter(id=userrelation)
                                    if companion_user:
                                        for c in companion_user:
                                            try:
                                                redis_queue.enqueue(send(companion_user,'user_added_to_summit_log',{'user':request.user, 'summit':summit , 'item':peak, 'summitlogId':summit_id, 'summit_date':date}, on_site=True, sender=request.user, summitlog=summit, summitlog_comment=None, receiver=c))
                                            except:
                                                pass

                        nonmember_baggers = request.POST.get('fellow_nonmember_baggers','')
                        if nonmember_baggers:
                            nonmember_baggers = nonmember_baggers.split(',')
                            baggers_to_add = nonmember_baggers
                            for bagger in baggers_to_add:
                                if bagger:
                                    sql = "insert into items_summitfellowbagger (summit_log_id, name, resolved_relation) values (%s, %s, False) "
                                    with connection.cursor() as cursor:
                                        cursor.execute(sql, [summit.id, bagger[1:-1]])

                        summitvideos = request.POST.get('summit_video_links','')
                        if summitvideos:
                            summitvideos = summitvideos.split(',')
                            summitvideos = [ x[1:-1] for x in summitvideos if x]
                            videos_to_add = summitvideos
                            for video in videos_to_add:
                                if video:
                                    summit_video = SummitLogVideo.objects.create(summit_log_id=summit_id, video_url=video)
                                    redis_queue.enqueue(save_video_data_from_webpage, summit_video.id)

                        summitlinks = request.POST.get('summit_related_links','')
                        process_summit_links(summit_id, summitlinks)

            #refresh member profile
            cache_path = '/members/%s/' % request.user.username
            cache_manager.invalidate_user_cache(cache_path, request.user.id)
            #refresh member summits
            #expire_path_cache("/api/user/summits/?user=%s&year=&region=&type=all&page=1" % request.user.id)
            from peakery.accounts.utils import update_member_profile_cache
            redis_queue.enqueue(update_member_profile_cache, request.user)

            #save new route name(s) if necessary
            new_route_name = request.POST.get('summit-route-name')
            route_up_id = request.POST.get('route_up')
            if new_route_name != '':
                if init_summit_ids:
                    summit_ids = init_summit_ids.split(",")
                    sql = "update items_peakroute set default_name = False, name = %s, slug = %s " + \
                        "from items_summitlog " + \
                        "where cast(items_summitlog.id as varchar) = any(%s) " + \
                        "and items_summitlog.peak_route_id = items_peakroute.id " + \
                        "and items_peakroute.default_name = True "
                    with connection.cursor() as cursor:
                        cursor.execute(sql, [new_route_name, slugify(new_route_name), summit_ids])

            request.session['show_badges'] = True
            request.session['badges_summit_id'] = first_summit.id

            end_time = time.time() - start_time
            print('perfmon - log_summit - %s' % end_time)

            return HttpResponseRedirect(reverse('user_summit_badges', kwargs={'username':request.user.username}))

        else:
            summit = SummitLog(user=request.user, item_id=0, date=datetime.date.today(), status = STATUS_SUMMIT_PENDING)
            summit.quick_save()
    else:
        if not request.user.is_authenticated:
            #try to log user in if coming from mobile app
            if request.META.get('HTTP_PEAKMOBILEAPP', None) == 'com.peakery.android':
                user = None
                peak = None
                auth_token = request.GET.get('auth_token', None)
                peak_id = request.GET.get('peak', None)
                if peak_id:
                    peak = Item.objects.get(id=peak_id)
                if auth_token:
                    from jose import jws
                    from django.contrib.auth import login
                    decoded_dict = jws.verify(auth_token, '7il1oZvu8BnzMtDrrGtXJPZd53VZ57fe', algorithms=['HS256'])
                    username = json.loads(decoded_dict)['username']
                    user = User.objects.get(username=username)
                    if user:
                        user.backend = 'django.contrib.auth.backends.ModelBackend'
                        login(request, user)
                    else:
                        if peak:
                            return HttpResponseRedirect('/%s/' % peak.slug_new_text)
                        else:
                            return HttpResponseRedirect('/')
                else:
                    if peak:
                        return HttpResponseRedirect('/%s/' % peak.slug_new_text)
                    else:
                        return HttpResponseRedirect('/')
            else:
                return HttpResponseRedirect('/')

        init_summit_ids = request.GET.get('summits', None)
        show_manual_route_name = False
        if init_summit_ids:
            summit_ids = init_summit_ids.split(",")
            sql = "select a.id, a.gpx_file, b.default_name " + \
                "from items_summitlog a, items_peakroute b " + \
                "where cast(a.id as varchar) = any(%s) and a.peak_route_id = b.id "

            summits = SummitLog.objects.raw(sql, [summit_ids])

            for s in summits:
                init_gpx_file = s.gpx_file
                if s.default_name:
                    default_route_name = True
        else:
            show_manual_route_name = True

        summit = SummitLog(user=request.user, item_id=0, date=datetime.date.today(), status = STATUS_SUMMIT_PENDING)
        summit.quick_save()

    return render(request, 'items/log_climb_multi_simple.html', {
        'photo_minsize':photo_minsize,
        'init_gpx_file':init_gpx_file,
        'default_route_name':default_route_name,
        'show_manual_route_name':show_manual_route_name,
        'summit':summit,
        'route':summit.peak_route
    })


def process_summit_links(summit_id, summitlinks):
    from peakery.items.utils import save_related_link_data_from_webpage
    if summitlinks:
        summitlinks = summitlinks.split(',')
        summitlinks = [x[1:-1] for x in summitlinks if x]
        links_to_add = summitlinks
        for link in links_to_add:
            if link:
                related_link = SummitLogRelatedLink.objects.create(summit_log_id=summit_id, related_url=link)
                redis_queue.enqueue(save_related_link_data_from_webpage, related_link.id)


#@login_required
def edit_climb_multi_simple(request):
    photo_minsize = [settings.MIN_SUMMIT_IMG_WIDTH, settings.MIN_SUMMIT_IMG_HEIGHT]
    init_gpx_file = None
    if request.method == 'POST':
        import time
        start_time = time.time()

        from urllib.parse import urlparse
        from peakery.items.utils import save_related_link_data_from_webpage
        from peakery.items.utils import save_video_data_from_webpage

        climb_log_id = request.POST.get('summit_id')
        track_notes = request.POST.get('log')

        addl_summits = []
        summit_logs = []

        peaks = request.POST.get('climbed_peak_ids', None)
        summit = SummitLog.objects.get(id=climb_log_id)
        peak = Item.objects.get(id=summit.item_id)

        if summit and peak:
            try:
                # already a multi peak summit? remove additional summits, we'll add them back later
                if summit.summitlog_group_id:
                    sql = "delete from items_summitloggroupsummit where group_id = %s and summit_id != %s "
                    with connection.cursor() as cursor:
                        cursor.execute(sql, [summit.summitlog_group_id, summit.id])

                    sql = "delete from items_summitlog where summitlog_group_id = %s and item_id != %s "
                    with connection.cursor() as cursor:
                        cursor.execute(sql, [summit.summitlog_group_id, summit.item_id])

                # update summit date, attempt and description
                summit.log = track_notes
                if len(peaks) > 1:
                    summit.multi_peak_summit = True
                else:
                    summit.multi_peak_summit = False
                    summit.summitlog_group_id = None
                for p in peaks:
                    if str(p['id']) == str(peak.id):
                        if p['attempted'] == 'true':
                            summit.attempt = True
                    else:
                        addl_summits.append(p['id'])
                summit.quick_save()

                # add photos if needed
                if len(photos) > 0:
                    category = ItemPhotoCategory.objects.get(name='summit')
                    for photo_index, track_photo in enumerate(photos, start=1):
                        filename = track_photo['photo_filename']
                        caption = track_photo['photo_caption']
                        upload_path = 'items/users/%s' % str(filename)
                        extension = os.path.splitext(filename)[1][1:].lower()
                        ext_len = len(extension) + 1
                        uuid = filename[0:-ext_len]
                        photo_object = ItemPhoto(user=edit_user, item=summit.item, category=category,
                                                 summit_log=summit,
                                                 image=upload_path, uuid=uuid, photo_index=photo_index, caption=caption)
                        photo_object.save()

                # if multiple peaks, create new summits
                if len(peaks) > 1:
                    # create group for these summits
                    summit_log_group = SummitLogGroup(user=edit_user)
                    summit_log_group.save()
                    summit.summitlog_group_id = summit_log_group.id
                    summit.quick_save()

                    for p in peaks:
                        if str(p['id']) != str(peak.id):
                            new_summit = SummitLog(user=edit_user, item_id=p['id'], gpx_source=summit.gpx_source,
                                                   log_source=summit.log_source, date=summit.date,
                                                   date_entered=summit.date_entered, status=summit.status,
                                                   log=summit.log,
                                                   gpx_file=summit.gpx_file, gpx_geom=summit.gpx_geom,
                                                   encoded_polyline=summit.encoded_polyline,
                                                   distance_to_summit=summit.distance_to_summit,
                                                   total_distance=summit.total_distance,
                                                   start_elevation=summit.start_elevation,
                                                   elevation_gain=summit.elevation_gain,
                                                   time_to_summit=summit.time_to_summit,
                                                   total_trip_time=summit.total_trip_time,
                                                   max_elevation=summit.max_elevation,
                                                   elevation_loss=summit.elevation_loss,
                                                   moving_time=summit.moving_time)
                            new_summit.quick_save()
                            summit_logs.append(new_summit.id)

                    sql = "update items_summitlog set multi_peak_summit = true, summitlog_group_id = %s where id = any(%s) "

                    with connection.cursor() as cursor:
                        cursor.execute(sql, [summit.summitlog_group_id, summit_logs])

                    for log in summit_logs:
                        # save the summit group
                        SummitLogGroupSummit.objects.create(summit_id=log, group_id=summit.summitlog_group_id)

                    SummitLogGroupSummit.objects.create(summit_id=summit.id, group_id=summit.summitlog_group_id)

                edit_user.person.refresh_latest_cache_for_user()

                return render(request, 'api/mobile_summits_edit_v2_3.html', {
                    'response_status': 'success',
                    'response_message': 'Summit updated.'
                })
            except:
                return render(request, 'api/mobile_summits_edit_v2_3.html', {
                    'response_status': 'error',
                    'response_message': 'Unable to edit summit, please try again later.'
                })

        if peaks:
            multi_peak_summit = False
            #did we start with initial summit logs?
            if init_summit_ids:
                #just update the initial summit logs
                #first get the summits
                summits = init_summit_ids.split(",")

                sql = "select a.id, b.id as summit_log_id, b.date as summit_log_date, b.gpx_file, b.distance_to_summit, b.total_distance, " + \
                    "b.start_elevation, b.elevation_gain, b.time_to_summit, b.total_trip_time, b.gpx_geom, b.elevation_loss, b.max_elevation " + \
                    "from items_item a, items_summitlog b " + \
                    "where cast(b.id as varchar) = any(%s) and b.item_id = a.id "

                peaks = Item.objects.raw(sql, [summits])

                climbed_peak_ids = climbed_peak_ids.split(',')
                if len(climbed_peak_ids) > 1:
                    multi_peak_summit = True
                    #create group for these summits
                    summit_log_group = SummitLogGroup(user=request.user)
                    summit_log_group.save()
                for climbed_peak in climbed_peak_ids:
                    peak_id = climbed_peak
                    peak = Item.objects.get(id=peak_id)
                    #was this peak part of the init summits?
                    matched_peak = False
                    for p in peaks:
                        if str(p.id) == str(peak_id):
                            summit = SummitLog.objects.get(id=p.summit_log_id)
                            summit.status = STATUS_SUMMIT_ACTIVE
                            matched_peak = True

                            # save first summit stats
                            first_summit_distance_to_summit = p.distance_to_summit
                            first_summit_total_distance = p.total_distance
                            first_summit_start_elevation = p.start_elevation
                            first_summit_elevation_gain = p.elevation_gain
                            first_summit_time_to_summit = p.time_to_summit
                            first_summit_total_trip_time = p.total_trip_time
                            first_summit_gpx_geom = p.gpx_geom
                            first_summit_encoded_polyline = p.encoded_polyline
                            first_summit_elevation_loss = p.elevation_loss
                            first_summit_max_elevation = p.max_elevation

                            if not first_peak:
                                first_peak = p

                    if not matched_peak:
                        summit = SummitLog(user=request.user, status=STATUS_SUMMIT_ACTIVE, date=gpx_date, gpx_file=gpx_file, distance_to_summit=first_summit_distance_to_summit, total_distance=first_summit_total_distance, start_elevation=first_summit_start_elevation, elevation_gain=first_summit_elevation_gain, time_to_summit=first_summit_time_to_summit, total_trip_time=first_summit_total_trip_time, gpx_geom=first_summit_gpx_geom, encoded_polyline=first_summit_encoded_polyline, elevation_loss=first_summit_elevation_loss, max_elevation=first_summit_max_elevation)
                        summit.item = peak
                        if not first_peak:
                            first_peak = peak
                        summit.date = gpx_date
                        summit.gpx_file = gpx_file
                        summit.date_entered = True

                    log = request.POST.get('log')
                    if log == '':
                        log = None
                    summit.log = log

                    outcomeElement = 'rdoOutcome%s' % peak_id
                    if request.POST.getlist(outcomeElement)[0] == '1':
                        summit.attempt = True
                    else:
                        summit.attempt = False

                    photos = ItemPhoto.objects.filter(summit_log=first_summit).order_by('id')
                    for p in photos:
                        photo_element = 'photo%s' % p.id
                        caption_element = 'caption%s' % p.id
                        if request.POST.get(photo_element) == '' or not request.POST.get(photo_element):
                            photo = ItemPhoto.objects.get(id=p.id)
                            photo.delete()
                        else:
                            if request.POST.get(caption_element) != '':
                                photo = ItemPhoto(caption=request.POST.get(caption_element), user=request.user, item=peak, summit_log_id=summit.id, category=category, image=p.image, uuid=p.uuid, photo_index=p.photo_index)
                                photo.save()
                            else:
                                photo = ItemPhoto(user=request.user, item=peak, summit_log_id=summit.id, category=category, image=p.image, uuid=p.uuid, photo_index=p.photo_index)
                                photo.save()

                    trip_metadata = '{"type":%s, "activities":%s, "challenges":%s, "gear":%s}' % (json.dumps(request.POST.getlist('rdoTripType')), json.dumps(request.POST.getlist('chkActivityTags')), json.dumps(request.POST.getlist('chkChallengeTags')), json.dumps(request.POST.getlist('chkGearTags')))
                    summit.trip_metadata = trip_metadata
                    summit.multi_peak_summit = multi_peak_summit
                    if multi_peak_summit:
                        summit.summitlog_group_id = summit_log_group.id
                    summit.quick_save()
                    #save the summit group
                    if multi_peak_summit:
                        SummitLogGroupSummit.objects.create(summit=summit, group=summit_log_group)
                    #update summitlog count for item
                    summitlog_count = SummitLog.objects.filter(item=peak, attempt=False, status=STATUS_SUMMIT_ACTIVE).count()
                    peak.summitlog_count = summitlog_count
                    peak.save_summitlog_count()

                    if not first_created_summit:
                        first_created_summit = summit

                    userrelations = request.POST.get('fellow_selected_users','')
                    if userrelations:
                        userrelations = userrelations.split(',')
                        userrelations = [ int(x[1:-1]) for x in userrelations if x]
                        relations_to_add = userrelations
                        for userrelation in relations_to_add:
                            if userrelation:
                                to_user = User.objects.get(id=userrelation)
                                exists = UserRelation.check_relation(request.user, to_user, None, None)
                                if exists:
                                    newrelation = exists
                                else:
                                    newrelation = UserRelation(from_user=request.user, to_user=to_user, first_name=to_user.first_name, last_name=to_user.last_name, source=2)
                                    newrelation.save()
                                companion = Companions(summit_log = summit, user = request.user, user_relation = newrelation)
                                companion.save()
                                companion_user = User.objects.filter(id=userrelation)
                                if companion_user:
                                    for c in companion_user:
                                        try:
                                            redis_queue.enqueue(send(companion_user,'user_added_to_summit_log',{'user':request.user, 'summit':summit , 'item':peak, 'summitlogId':summit_id, 'summit_date':gpx_date}, on_site=True, sender=request.user, summitlog=summit, summitlog_comment=None, receiver=c))
                                        except:
                                            pass

                    nonmember_baggers = request.POST.get('fellow_nonmember_baggers','')
                    if nonmember_baggers:
                        nonmember_baggers = nonmember_baggers.split(',')
                        baggers_to_add = nonmember_baggers
                        for bagger in baggers_to_add:
                            if bagger:
                                sql = "insert into items_summitfellowbagger (summit_log_id, name, resolved_relation) values (%s, %s, False) "
                                with connection.cursor() as cursor:
                                    cursor.execute(sql, [summit.id, bagger[1:-1]])

                    summitvideos = request.POST.get('summit_video_links','')
                    if summitvideos:
                        summitvideos = summitvideos.split(',')
                        summitvideos = [ x[1:-1] for x in summitvideos if x]
                        videos_to_add = summitvideos
                        for video in videos_to_add:
                            if video:
                                summit_video = SummitLogVideo.objects.create(summit_log_id=summit_id, video_url=video)
                                redis_queue.enqueue(save_video_data_from_webpage, summit_video.id)

                    summitlinks = request.POST.get('summit_related_links','')
                    if summitlinks:
                        summitlinks = summitlinks.split(',')
                        summitlinks = [ x[1:-1] for x in summitlinks if x]
                        links_to_add = summitlinks
                        for link in links_to_add:
                            if link:
                                related_link = SummitLogRelatedLink.objects.create(summit_log_id=summit_id, related_url=link)
                                redis_queue.enqueue(save_related_link_data_from_webpage, related_link.id)

                #reset first summit to first actually created, for redirect purposes
                first_summit = first_created_summit
            else:
                climbed_peak_ids = climbed_peak_ids.split(',')
                if len(climbed_peak_ids) > 1:
                    multi_peak_summit = True
                    #create group for these summits
                    summit_log_group = SummitLogGroup(user=request.user)
                    summit_log_group.save()
                peak_id = climbed_peak_ids[0]
                peak = Item.objects.get(id=peak_id)
                summit.item = peak

                #save first peak and summit
                first_summit = summit
                first_peak = peak

                #save summit log(s)
                date = request.POST.get('summit-date')
                date_entered = True
                log = request.POST.get('log')
                if log=='':log=None
                if date=='':
                    date = datetime.date.today()
                    date_entered = False

                summit.status = STATUS_SUMMIT_ACTIVE
                summit.log = log
                summit.date = date
                summit.date_entered = date_entered
                outcomeElement = 'rdoOutcome%s' % peak_id
                if request.POST.getlist(outcomeElement)[0] == '1':
                    summit.attempt = True
                else:
                    summit.attempt = False

                #photos are already assigned to the first summit, just update them
                photos = ItemPhoto.objects.filter(summit_log=summit).order_by('id')
                for p in photos:
                    photo_element = 'photo%s' % p.id
                    if request.POST.get(photo_element) == '' or not request.POST.get(photo_element):
                        photo = ItemPhoto.objects.get(id=p.id)
                        photo.delete()
                    else:
                        caption_element = 'caption%s' % p.id
                        if request.POST.get(caption_element) != '':
                            photo = ItemPhoto.objects.get(id=p.id)
                            photo.caption = request.POST.get(caption_element)
                            photo.user = request.user
                            photo.item = peak
                            photo.save()
                        else:
                            photo = ItemPhoto.objects.get(id=p.id)
                            photo.user = request.user
                            photo.item = peak
                            photo.save()

                trip_metadata = '{"type":%s, "activities":%s, "challenges":%s, "gear":%s}' % (json.dumps(request.POST.getlist('rdoTripType')), json.dumps(request.POST.getlist('chkActivityTags')), json.dumps(request.POST.getlist('chkChallengeTags')), json.dumps(request.POST.getlist('chkGearTags')))
                summit.trip_metadata = trip_metadata
                summit.multi_peak_summit = multi_peak_summit
                if multi_peak_summit:
                    summit.summitlog_group_id = summit_log_group.id
                summit.quick_save()
                #save the summit group
                if multi_peak_summit:
                    SummitLogGroupSummit.objects.create(summit=summit, group=summit_log_group)
                #update summitlog count for item
                summitlog_count = SummitLog.objects.filter(item=peak, attempt=False, status=STATUS_SUMMIT_ACTIVE).count()
                peak.summitlog_count = summitlog_count
                peak.save_summitlog_count()

                userrelations = request.POST.get('fellow_selected_users','')
                if userrelations:
                    userrelations = userrelations.split(',')
                    userrelations = [ int(x[1:-1]) for x in userrelations if x]
                    relations_to_add = userrelations
                    for userrelation in relations_to_add:
                        if userrelation:
                            to_user = User.objects.get(id=userrelation)
                            exists = UserRelation.check_relation(request.user, to_user, None, None)
                            if exists:
                                newrelation = exists
                            else:
                                newrelation = UserRelation(from_user=request.user, to_user=to_user, first_name=to_user.first_name, last_name=to_user.last_name, source=2)
                                newrelation.save()
                            companion = Companions(summit_log = summit, user = request.user, user_relation = newrelation)
                            companion.save()
                            companion_user = User.objects.filter(id=userrelation)
                            if companion_user:
                                for c in companion_user:
                                    try:
                                        redis_queue.enqueue(send(companion_user,'user_added_to_summit_log',{'user':request.user, 'summit':summit , 'item':peak, 'summitlogId':summit_id, 'summit_date':date}, on_site=True, sender=request.user, summitlog=summit, summitlog_comment=None, receiver=c))
                                    except:
                                        pass

                nonmember_baggers = request.POST.get('fellow_nonmember_baggers','')
                if nonmember_baggers:
                    nonmember_baggers = nonmember_baggers.split(',')
                    baggers_to_add = nonmember_baggers
                    for bagger in baggers_to_add:
                        if bagger:
                            sql = "insert into items_summitfellowbagger (summit_log_id, name, resolved_relation) values (%s, %s, False) "
                            with connection.cursor() as cursor:
                                cursor.execute(sql, [summit.id, bagger[1:-1]])

                summitvideos = request.POST.get('summit_video_links','')
                if summitvideos:
                    summitvideos = summitvideos.split(',')
                    summitvideos = [ x[1:-1] for x in summitvideos if x]
                    videos_to_add = summitvideos
                    for video in videos_to_add:
                        if video:
                            summit_video = SummitLogVideo.objects.create(summit_log_id=summit_id, video_url=video)
                            redis_queue.enqueue(save_video_data_from_webpage, summit_video.id)

                summitlinks = request.POST.get('summit_related_links','')
                if summitlinks:
                    summitlinks = summitlinks.split(',')
                    summitlinks = [ x[1:-1] for x in summitlinks if x]
                    links_to_add = summitlinks
                    for link in links_to_add:
                        if link:
                            related_link = SummitLogRelatedLink.objects.create(summit_log_id=summit_id, related_url=link)
                            redis_queue.enqueue(save_related_link_data_from_webpage, related_link.id)

                #any other peaks to save?
                if len(climbed_peak_ids) > 1:
                    remaining_climbed_peaks = climbed_peak_ids[1:]
                    for climbed_peak in remaining_climbed_peaks:
                        peak_id = climbed_peak
                        peak = Item.objects.get(id=peak_id)
                        summit = SummitLog(user=request.user, status=STATUS_SUMMIT_ACTIVE)
                        summit.item = peak

                        #save summit log(s)
                        date = request.POST.get('summit-date')
                        date_entered = True
                        log = request.POST.get('log')
                        if log=='':log=None
                        if date=='':
                            date = datetime.date.today()
                            date_entered = False

                        summit.log = log
                        summit.date = date
                        summit.date_entered = date_entered
                        outcomeElement = 'rdoOutcome%s' % peak_id
                        if request.POST.getlist(outcomeElement)[0] == '1':
                            summit.attempt = True
                        else:
                            summit.attempt = False

                        trip_metadata = '{"type":%s, "activities":%s, "challenges":%s, "gear":%s}' % (json.dumps(request.POST.getlist('rdoTripType')), json.dumps(request.POST.getlist('chkActivityTags')), json.dumps(request.POST.getlist('chkChallengeTags')), json.dumps(request.POST.getlist('chkGearTags')))
                        summit.trip_metadata = trip_metadata
                        summit.multi_peak_summit = multi_peak_summit
                        if multi_peak_summit:
                            summit.summitlog_group_id = summit_log_group.id
                        summit.quick_save()
                        #save the summit group
                        if multi_peak_summit:
                            SummitLogGroupSummit.objects.create(summit=summit, group=summit_log_group)

                        #get photos from the first summit and add them for this summit
                        photos = ItemPhoto.objects.filter(summit_log=first_summit).order_by('id')
                        for p in photos:
                            photo_element = 'photo%s' % p.id
                            caption_element = 'caption%s' % p.id
                            if request.POST.get(photo_element) == '' or not request.POST.get(photo_element):
                                photo = ItemPhoto.objects.get(id=p.id)
                                photo.delete()
                            else:
                                if request.POST.get(caption_element) != '':
                                    photo = ItemPhoto(caption=request.POST.get(caption_element), user=request.user, item=peak, summit_log_id=summit.id, category=category, image=p.image, uuid=p.uuid, photo_index=p.photo_index)
                                    photo.save()
                                else:
                                    photo = ItemPhoto(user=request.user, item=peak, summit_log_id=summit.id, category=category, image=p.image, uuid=p.uuid, photo_index=p.photo_index)
                                    photo.save()

                        #update summitlog count for item
                        summitlog_count = SummitLog.objects.filter(item=peak, attempt=False, status=STATUS_SUMMIT_ACTIVE).count()
                        peak.summitlog_count = summitlog_count
                        peak.save_summitlog_count()

                        userrelations = request.POST.get('fellow_selected_users','')
                        if userrelations:
                            userrelations = userrelations.split(',')
                            userrelations = [ int(x[1:-1]) for x in userrelations if x]
                            relations_to_add = userrelations
                            for userrelation in relations_to_add:
                                if userrelation:
                                    to_user = User.objects.get(id=userrelation)
                                    exists = UserRelation.check_relation(request.user, to_user, None, None)
                                    if exists:
                                        newrelation = exists
                                    else:
                                        newrelation = UserRelation(from_user=request.user, to_user=to_user, first_name=to_user.first_name, last_name=to_user.last_name, source=2)
                                        newrelation.save()
                                    companion = Companions(summit_log = summit, user = request.user, user_relation = newrelation)
                                    companion.save()
                                    companion_user = User.objects.filter(id=userrelation)
                                    if companion_user:
                                        for c in companion_user:
                                            try:
                                                redis_queue.enqueue(send(companion_user,'user_added_to_summit_log',{'user':request.user, 'summit':summit , 'item':peak, 'summitlogId':summit_id, 'summit_date':date}, on_site=True, sender=request.user, summitlog=summit, summitlog_comment=None, receiver=c))
                                            except:
                                                pass

                        nonmember_baggers = request.POST.get('fellow_nonmember_baggers','')
                        if nonmember_baggers:
                            nonmember_baggers = nonmember_baggers.split(',')
                            baggers_to_add = nonmember_baggers
                            for bagger in baggers_to_add:
                                if bagger:
                                    sql = "insert into items_summitfellowbagger (summit_log_id, name, resolved_relation) values (%s, %s, False) "
                                    with connection.cursor() as cursor:
                                        cursor.execute(sql, [summit.id, bagger[1:-1]])

                        summitvideos = request.POST.get('summit_video_links','')
                        if summitvideos:
                            summitvideos = summitvideos.split(',')
                            summitvideos = [ x[1:-1] for x in summitvideos if x]
                            videos_to_add = summitvideos
                            for video in videos_to_add:
                                if video:
                                    summit_video = SummitLogVideo.objects.create(summit_log_id=summit_id, video_url=video)
                                    redis_queue.enqueue(save_video_data_from_webpage, summit_video.id)

                        summitlinks = request.POST.get('summit_related_links','')
                        if summitlinks:
                            summitlinks = summitlinks.split(',')
                            summitlinks = [ x[1:-1] for x in summitlinks if x]
                            links_to_add = summitlinks
                            for link in links_to_add:
                                if link:
                                    related_link = SummitLogRelatedLink.objects.create(summit_log_id=summit_id, related_url=link)
                                    redis_queue.enqueue(save_related_link_data_from_webpage, related_link.id)

            #  Refresh member profile
            cache_path = '/members/%s/' % request.user.username
            cache_manager.invalidate_user_cache(cache_path, request.user.id)
            #  refresh member summits
            #expire_path_cache("/api/user/summits/?user=%s&year=&region=&type=all&page=1" % request.user.id)
            from peakery.accounts.utils import update_member_profile_cache
            redis_queue.enqueue(update_member_profile_cache, request.user)

            #save new route name(s) if necessary
            new_route_name = request.POST.get('summit-route-name')
            route_up_id = request.POST.get('route_up')
            if new_route_name != '':
                if init_summit_ids:
                    summit_ids = init_summit_ids.split(",")
                    sql = "update items_peakroute set default_name = False, name = %s, slug = %s " + \
                        "from items_summitlog " + \
                        "where cast(items_summitlog.id as varchar) = any(%s) " + \
                        "and items_summitlog.peak_route_id = items_peakroute.id " + \
                        "and items_peakroute.default_name = True "
                    with connection.cursor() as cursor:
                        cursor.execute(sql, [new_route_name, slugify(new_route_name), summit_ids])
            elif route_up_id != '' and route_up_id != '0':
                if init_summit_ids:
                    summit_ids = init_summit_ids.split(",")
                    #update the summitlog where peak route id
                    sql = "update items_summitlog set peak_route_id = %s " + \
                        "where cast(items_summitlog.id as varchar) = any(%s) "
                    with connection.cursor() as cursor:
                        cursor.execute(sql, [route_up_id, summit_ids])

                    #update the route with gpx file
                    encoded_mapbox_polyline = mapbox_thumbnail_service.get_encoded_polyline_for_mapbox(gpx_file)
                    sql = "update items_peakroute set gpx_file = %s, " + \
                          "gpx_start_index = 0, gpx_end_index = 500, " + \
                          "distance_to_summit = items_summitlog.distance_to_summit, " + \
                          "start_elevation = items_summitlog.start_elevation, " + \
                          "elevation_gain = items_summitlog.elevation_gain, " + \
                          "gpx_geom = items_summitlog.gpx_geom, " + \
                          "encoded_polyline = %s, " + \
                          "total_distance = items_summitlog.total_distance, " + \
                          "elevation_loss = items_summitlog.elevation_loss, " + \
                          "max_elevation = items_summitlog.max_elevation " + \
                          "from items_summitlog " + \
                          "where items_peakroute.id = %s and items_summitlog.id = %s "
                    with connection.cursor() as cursor:
                        cursor.execute(sql, [gpx_file, encoded_mapbox_polyline,route_up_id, summit.id])


            request.session['show_badges'] = True
            request.session['badges_summit_id'] = first_summit.id

            end_time = time.time() - start_time
            print('perfmon - log_summit - %s' % end_time)

            return HttpResponseRedirect(reverse('user_summit_badges', kwargs={'username':request.user.username}))

        else:
            summit = SummitLog(user=request.user, item_id=0, date=datetime.date.today(), status = STATUS_SUMMIT_PENDING)
            summit.quick_save()
    else:
        init_summit_ids = request.GET.get('summits', None)
        init_summit_id = 0
        summit = None
        if init_summit_ids:
            summit_ids = init_summit_ids.split(",")
            sql = "select a.id, a.gpx_file " + \
                "from items_summitlog a " + \
                "join items_item aa on aa.id = a.item_id " + \
                "where cast(a.id as varchar) = any(%s) " + \
                "order by a.date desc, a.summitlog_group_id, aa.elevation desc, a.id asc "

            summits = SummitLog.objects.raw(sql, [summit_ids])

            for s in summits:
                init_gpx_file = s.gpx_file
                init_summit_id = s.id
                break

            summit = SummitLog.objects.get(id=init_summit_id)
            photos = ItemPhoto.objects.filter(summit_log=summit).order_by('id')

    return render(request, 'items/edit_climb_multi_simple.html', {
        'photo_minsize': photo_minsize,
        'init_gpx_file': init_gpx_file,
        'summit': summit,
        'summits': summits,
        'photos': photos
    })


@login_required
def summit_2(request, peak_id, is_edit=False):
    photo_minsize = [settings.MIN_SUMMIT_IMG_WIDTH, settings.MIN_SUMMIT_IMG_HEIGHT]
    peak = Item.objects.get(id=peak_id)
    if request.method == 'POST':
        #process step 2
        date = request.POST.get('summit-date')
        date_entered = True
        log = request.POST.get('log')
        if log=='':log=None
        if date=='':
            date = datetime.date.today()
            date_entered = False
        summit_id = request.POST.get('summit_id')
        summit = SummitLog.objects.get(id=summit_id)
        summit.status = STATUS_SUMMIT_ACTIVE
        summit.log = log
        summit.date = date
        summit.date_entered = date_entered
        if request.POST.getlist('rdoOutcome')[0] == '1':
            summit.attempt = True
        else:
            summit.attempt = False

        photos = ItemPhoto.objects.filter(summit_log=summit).order_by('id')
        for p in photos:
            photo_element = 'photo%s' % p.id
            if request.POST.get(photo_element) == '' or not request.POST.get(photo_element):
                photo = ItemPhoto.objects.get(id=p.id)
                photo.delete()
            else:
                caption_element = 'caption%s' % p.id
                if request.POST.get(caption_element) != '':
                    photo = ItemPhoto.objects.get(id=p.id)
                    photo.caption = request.POST.get(caption_element)
                    photo.user = request.user
                    photo.item = summit.item
                    photo.save()
                else:
                    photo = ItemPhoto.objects.get(id=p.id)
                    photo.user = request.user
                    photo.item = summit.item
                    photo.save()

        gpx_file = request.POST.get('gpx-file',None)
        summit.gpx_file = gpx_file

        route_up_name = request.POST.get('txtNewSummitRoute',None)
        route_up_id = request.POST.get('route_up',None)
        if route_up_name != '':
            route_up, created = PeakRoute.objects.get_or_create(name=route_up_name, item=peak, user=request.user)
            summit.peak_route_id = route_up
        elif route_up_id != '' and route_up_id != '0' and route_up_id != 'add_new_route':
            summit.peak_route_id = route_up_id

        if request.POST.get('distance-to-summit') != '' and request.POST.get('distance-to-summit-units') == 'km':
            distance_to_summit = float(request.POST.get('distance-to-summit'))/1.60934
            summit.distance_to_summit = distance_to_summit
        elif request.POST.get('distance-to-summit') != '':
            distance_to_summit = request.POST.get('distance-to-summit')
            summit.distance_to_summit = distance_to_summit

        if request.POST.get('total-trip-distance') != '' and request.POST.get('total-trip-distance-units') == 'km':
            total_distance = float(request.POST.get('total-trip-distance'))/1.60934
            summit.total_distance = total_distance
        elif request.POST.get('total-trip-distance') != '':
            total_distance = request.POST.get('total-trip-distance')
            summit.total_distance = total_distance

        if request.POST.get('elevation-start') != '' and request.POST.get('elevation-start-units') == 'm':
            elevation_start = float(request.POST.get('elevation-start'))/0.3048
            summit.start_elevation = elevation_start
        elif request.POST.get('elevation-start') != '':
            elevation_start = request.POST.get('elevation-start')
            summit.start_elevation = elevation_start

        if request.POST.get('elevation-gain') != '' and request.POST.get('elevation-gain-units') == 'm':
            elevation_gain = float(request.POST.get('elevation-gain'))/0.3048
            summit.elevation_gain = elevation_gain
        elif request.POST.get('elevation-gain') != '':
            elevation_gain = request.POST.get('elevation-gain')
            summit.elevation_gain = elevation_gain

        time_to_summit = 0
        if request.POST.get('time-to-summit-min') != '':
            time_to_summit = 60*float(request.POST.get('time-to-summit-min'))
        if request.POST.get('time-to-summit-hrs') != '':
            time_to_summit = time_to_summit + (3600*float(request.POST.get('time-to-summit-hrs')))
        if time_to_summit > 0:
            summit.time_to_summit = time_to_summit

        total_trip_time = 0
        if request.POST.get('total-trip-time-min') != '':
            total_trip_time = 60*float(request.POST.get('total-trip-time-min'))
        if request.POST.get('total-trip-time-hrs') != '':
            total_trip_time = total_trip_time + (3600*float(request.POST.get('total-trip-time-hrs')))
        if total_trip_time > 0:
            summit.total_trip_time = total_trip_time

        trip_metadata = '{"type":%s, "activities":%s, "challenges":%s, "gear":%s}' % (json.dumps(request.POST.getlist('rdoTripType')), json.dumps(request.POST.getlist('chkActivityTags')), json.dumps(request.POST.getlist('chkChallengeTags')), json.dumps(request.POST.getlist('chkGearTags')))
        summit.trip_metadata = trip_metadata

        summit.quick_save()
        #update summitlog count for item
        summitlog_count = SummitLog.objects.filter(item=peak, attempt=False, status=STATUS_SUMMIT_ACTIVE).count()
        peak.summitlog_count = summitlog_count
        peak.quick_save()
        #  Refresh member profile
        cache_path = '/members/%s/' % request.user.username
        cache_manager.invalidate_user_cache(cache_path, request.user.id)

        from peakery.accounts.utils import update_member_profile_cache
        from urllib.parse import urlparse

        redis_queue.enqueue(update_member_profile_cache, request.user)

        #refresh latest page caches
        peak.refresh_latest_cache_for_peak()
        request.user.person.refresh_latest_cache_for_user()

        userrelations = request.POST.get('fellow_selected_users','')
        if userrelations:
            userrelations = userrelations.split(',')
            userrelations = [ int(x[1:-1]) for x in userrelations if x]
            relations_to_add = userrelations
            for userrelation in relations_to_add:
                if userrelation:
                    to_user = User.objects.get(id=userrelation)
                    exists = UserRelation.check_relation(request.user, to_user, None, None)
                    if exists:
                        newrelation = exists
                    else:
                        newrelation = UserRelation(from_user=request.user, to_user=to_user, first_name=to_user.first_name, last_name=to_user.last_name, source=2)
                        newrelation.save()
                    companion = Companions(summit_log = summit, user = request.user, user_relation = newrelation)
                    companion.save()
                    companion_user = User.objects.filter(id=userrelation)
                    if companion_user:
                        for c in companion_user:
                            try:
                                send(companion_user,'user_added_to_summit_log',{'user':request.user, 'summit':summit , 'item':summit.item, 'summitlogId':summit_id, 'summit_date':date}, on_site=True, sender=request.user, summitlog=summit, summitlog_comment=None, receiver=c)
                            except:
                                pass

        nonmember_baggers = request.POST.get('fellow_nonmember_baggers','')
        if nonmember_baggers:
            nonmember_baggers = nonmember_baggers.split(',')
            baggers_to_add = nonmember_baggers
            for bagger in baggers_to_add:
                if bagger:
                    sql = "insert into items_summitfellowbagger (summit_log_id, name, resolved_relation) values (%s, %s, False) "
                    with connection.cursor() as cursor:
                        cursor.execute(sql, [summit.id, bagger[1:-1]])

        summitvideos = request.POST.get('summit_video_links','')
        if summitvideos:
            from peakery.items.utils import save_video_data_from_webpage
            summitvideos = summitvideos.split(',')
            summitvideos = [ x[1:-1] for x in summitvideos if x]
            videos_to_add = summitvideos
            for video in videos_to_add:
                if video:
                    summit_video = SummitLogVideo.objects.create(summit_log_id=summit_id, video_url=video)
                    redis_queue.enqueue(save_video_data_from_webpage, summit_video.id)

        summitlinks = request.POST.get('summit_related_links','')
        if summitlinks:
            from peakery.items.utils import save_related_link_data_from_webpage
            summitlinks = summitlinks.split(',')
            summitlinks = [ x[1:-1] for x in summitlinks if x]
            links_to_add = summitlinks
            for link in links_to_add:
                if link:
                    related_link = SummitLogRelatedLink.objects.create(summit_log_id=summit_id, related_url=link)
                    redis_queue.enqueue(save_related_link_data_from_webpage, related_link.id)

        request.session['show_badges'] = True

        return HttpResponseRedirect(reverse('peak_view_summit_badges', kwargs={'item_slug':summit.item.slug_new_text,'summitlog_id':summit_id}))
    else:
        summit = SummitLog(user=request.user, item=peak, date=datetime.date.today(), status = STATUS_SUMMIT_PENDING)
#        summit.save()
        summit.quick_save()
        if request.GET.has_key("first_time"):
            request.session['show_first_time_splash'] = True

        """ if user click the x in the upper right corner of the lighbox, dont bag the peak for that user. """
        request.session['user_closed_summit_step_1'] = True
        routes = peak.get_summits_routes_list()
        #all_routes_up = peak.summits.select_related('route_up').all().exclude(route_up=None)
        all_routes_up = get_new_routes_for_peak(peak)

        if routes:
            routes = 'true'
        else:
            routes = 'false'

    return render(request, 'items/summit_2.html', {
        'photo_minsize':photo_minsize,
        'peak':peak,
        'summit':summit,
        'routes':routes,
        'all_routes_up':all_routes_up
    })


#add new route
@login_required
def add_route(request, peak_id, is_edit=False):
    photo_minsize = [settings.MIN_SUMMIT_IMG_WIDTH, settings.MIN_SUMMIT_IMG_HEIGHT]
    peak = Item.objects.get(id=peak_id)
    if request.method == 'POST':
        #process add route form
        route_id = request.POST.get('route_id')
        route_name = request.POST.get('route-name',None)
        difficulty = request.POST.get('difficulty-rating',None)
        start_location = request.POST.get('start-location',None)
        getting_there = request.POST.get('getting-there',None)
        red_tape = request.POST.get('red-tape',None)
        route = PeakRoute.objects.get(id=route_id)
        route.status = STATUS_ROUTE_ACTIVE
        route.name = route_name
        route.difficulty = difficulty
        route.start_location = start_location
        route.getting_there = getting_there
        route.red_tape = red_tape

        #gpx stuff
        #if we got a gpx filename
        if request.POST.get('gpx-file') != '':
            route.gpx_file = request.POST.get('gpx-file')
            if request.POST.get('gpx-start-index') != '':
                route.gpx_start_index = int(float(request.POST.get('gpx-start-index')))
            if request.POST.get('gpx-end-index') != '':
                route.gpx_end_index = int(float(request.POST.get('gpx-end-index')))
            if request.POST.get('gpx-start-lat') != '':
                route.gpx_start_lat = request.POST.get('gpx-start-lat')
            if request.POST.get('gpx-start-lon') != '':
                route.gpx_start_lon = request.POST.get('gpx-start-lon')
            if request.POST.get('gpx-end-lat') != '':
                route.gpx_end_lat = request.POST.get('gpx-end-lat')
            if request.POST.get('gpx-end-lon') != '':
                route.gpx_end_lon = request.POST.get('gpx-end-lon')
        else:
            route.gpx_file = None
            route.gpx_start_index = None
            route.gpx_end_index = None
            route.gpx_start_lat = None
            route.gpx_start_lon = None
            route.gpx_end_lat = None
            route.gpx_end_lon = None

        if request.POST.get('distance-to-summit') != '' and request.POST.get('distance-to-summit') != 'None' and request.POST.get('distance-to-summit-units') == 'km':
            distance_to_summit = round(float(request.POST.get('distance-to-summit').replace(',',''))/1.60934,1)
            route.distance_to_summit = distance_to_summit
        elif request.POST.get('distance-to-summit') != '' and request.POST.get('distance-to-summit') != 'None':
            distance_to_summit = round(float(request.POST.get('distance-to-summit').replace(',','')),1)
            route.distance_to_summit = distance_to_summit

        if request.POST.get('elevation-start') != '' and request.POST.get('elevation-start') != 'None' and request.POST.get('elevation-start-units') == 'm':
            elevation_start = int(float(request.POST.get('elevation-start').replace(',',''))/0.3048)
            route.start_elevation = elevation_start
        elif request.POST.get('elevation-start') != '' and request.POST.get('elevation-start') != 'None':
            elevation_start = int(float(request.POST.get('elevation-start').replace(',','')))
            route.start_elevation = elevation_start

        if request.POST.get('elevation-gain') != '' and request.POST.get('elevation-gain') != 'None' and request.POST.get('elevation-gain-units') == 'm':
            elevation_gain = int(float(request.POST.get('elevation-gain').replace(',',''))/0.3048)
            route.elevation_gain = elevation_gain
        elif request.POST.get('elevation-gain') != '' and request.POST.get('elevation-gain') != 'None':
            elevation_gain = int(float(request.POST.get('elevation-gain').replace(',','')))
            route.elevation_gain = elevation_gain

        route_metadata_types = request.POST.getlist('chkRouteTypes')
        unique_types = uniq(route_metadata_types)
        route_metadata_features = request.POST.getlist('chkRouteFeatures')
        unique_features = uniq(route_metadata_features)
        route_metadata_challenges = request.POST.getlist('chkRouteChallenges')
        unique_challenges = uniq(route_metadata_challenges)
        route_metadata_gear = request.POST.getlist('chkGearTags')
        unique_gear = uniq(route_metadata_gear)
        route_metadata = '{"types":%s, "features":%s, "challenges":%s, "gear":%s}' % (json.dumps(unique_types), json.dumps(unique_features), json.dumps(unique_challenges), json.dumps(unique_gear))
        route.route_metadata = route_metadata
        encoded_mapbox_polyline = mapbox_thumbnail_service.get_encoded_polyline_for_mapbox(route.gpx_file)
        route.encoded_polyline = encoded_mapbox_polyline
        route.save()

        routelinks = request.POST.get('route_related_links','')
        PeakRouteRelatedLink.objects.filter(peak_route_id=route.id).delete()
        if routelinks:
            routelinks = routelinks.split(',')
            routelinks = [ x[1:-1] for x in routelinks if x]
            links_to_add = routelinks
            for link in links_to_add:
                if link:
                    PeakRouteRelatedLink.objects.create(peak_route_id=route_id, related_url=link)

        #route steps
        PeakRouteStep.objects.filter(route=route).delete()

        #get list of steps in order
        step_list = request.POST.get('route-step-file-list')
        steps = step_list.split(",")

        #loop through steps
        step_to_add = 1
        for this_step in steps:
            step_text = 'step-%s-details' % this_step
            step_image = 'route-step-file-%s' % this_step
            if request.POST.get(step_text) != '' and request.POST.get(step_text) is not None:
                route_step, created = PeakRouteStep.objects.get_or_create(route=route, step_number = step_to_add)
                route_step.description = request.POST.get(step_text)
                route_step.image = request.POST.get(step_image)
                route_step.save()
                step_to_add = step_to_add + 1

        return HttpResponseRedirect(reverse('peak_view_route', kwargs={'item_slug':route.item.slug_new_text,'route_id':route_id}))
    else:
        route = PeakRoute(user=request.user, item=peak, status = STATUS_ROUTE_PENDING)
        route.save()

    return render(request, 'items/add_peak_route.html', {
        'photo_minsize':photo_minsize,
        'peak':peak,
        'route':route,
    })

#combine routes (admin mode only)
@login_required
def combine_routes(request, peak_id, is_edit=False):
    if request.user.is_superuser:
        peak = Item.objects.get(id=peak_id)
        if request.method == 'POST':
            from_route_id = request.POST.get('rdoFromRoute', None)
            to_route_id = request.POST.get('rdoToRoute', None)
            to_gpx_id = request.POST.get('rdoToGpx', None)

            if from_route_id and to_route_id and to_gpx_id:
                #routes must be different
                if from_route_id != to_route_id:
                    #if GPX is staying on the To Route
                    if to_route_id == to_gpx_id:
                        #reassign summit logs from old route to new route
                        sql = "update items_summitlog set peak_route_id = %s where peak_route_id = %s "
                        with connection.cursor() as cursor:
                            cursor.execute(sql, [to_route_id, from_route_id])

                        #update status of old route to hide it
                        sql = "update items_peakroute set status = 2 where id = %s "
                        with connection.cursor() as cursor:
                            cursor.execute(sql, [from_route_id])
                    else:
                        #get gpx filename of new route
                        sql = "select name, gpx_file, total_distance from items_peakroute where id = %s "
                        with connection.cursor() as cursor:
                            cursor.execute(sql, [to_route_id])
                            routes = dictfetchall(cursor)
                        gpx_file = routes[0]['gpx_file']
                        to_route_name = routes[0]['name']
                        length_2d = float(routes[0]['total_distance'])

                        # rename from route with name from to route
                        sql = "update items_peakroute set name = %s where id = %s "
                        with connection.cursor() as cursor:
                            cursor.execute(sql, [to_route_name, from_route_id])

                        # reassign summit logs from new route to old route, except the one that originally uploaded with new route gpx file
                        sql = "update items_summitlog set peak_route_id = %s where peak_route_id = %s and coalesce(gpx_file,'') != %s "
                        with connection.cursor() as cursor:
                            cursor.execute(sql, [from_route_id, to_route_id, gpx_file])

                        if peak.is_usa():
                            from_route_name = '%s %s mi route' % (peak.name, round(length_2d, 1))
                        else:
                            from_route_name = '%s %s km route' % (peak.name, round(length_2d * 1.609, 1))

                        # rename to route with name from from route
                        sql = "update items_peakroute set name = %s where id = %s "
                        with connection.cursor() as cursor:
                            cursor.execute(sql, [from_route_name, to_route_id])

        return HttpResponseRedirect(reverse('peak_view', kwargs={'item_slug': peak.slug_new_text}))

#delete route
@login_required
def delete_route(request, peak_id, route_id, is_edit=False):
    if request.user.is_superuser:
        peak = Item.objects.get(id=peak_id)
        if route_id:
            # hide the route
            sql = "update items_peakroute set gpx_file = null where id = %s "
            with connection.cursor() as cursor:
                cursor.execute(sql, [route_id])

        return HttpResponseRedirect(reverse('peak_view', kwargs={'item_slug': peak.slug_new_text}))

#edit route
@login_required
def edit_route(request, peak_id, route_id, is_edit=False):
    photo_minsize = [settings.MIN_PEAK_IMG_WIDTH, settings.MIN_PEAK_IMG_HEIGHT]
    peak = Item.objects.get(id=peak_id)
    if request.method == 'POST':
        #process add route form
        route_id = request.POST.get('route_id')
        route_name = request.POST.get('route-name',None)
        difficulty = request.POST.get('difficulty-rating',None)

        route = PeakRoute.objects.get(id=route_id)

        #save the current route in archive
        route_archive = PeakRouteArchive(route_id=route.id, user_id=route.user_id, item_id=route.item_id, name=route.name, difficulty=route.difficulty, start_location=route.start_location, status=route.status, gpx_file=route.gpx_file, gpx_start_index=route.gpx_start_index, gpx_end_index=route.gpx_end_index, gpx_start_lat=route.gpx_start_lat, gpx_start_lon=route.gpx_start_lon, gpx_end_lat=route.gpx_end_lat, gpx_end_lon=route.gpx_end_lon, distance_to_summit=route.distance_to_summit, start_elevation=route.start_elevation, elevation_gain=route.elevation_gain, route_metadata=json.dumps(route.route_metadata), getting_there=route.getting_there, red_tape=route.red_tape)
        route_archive.save()
        current_steps = PeakRouteStep.objects.filter(route=route.id)
        for s in current_steps:
            route_step_archive = PeakRouteStepArchive(route_archive_id=route_archive.id, step_number=s.step_number, image=s.image, description=s.description)
            route_step_archive.save()
        current_links = PeakRouteRelatedLink.objects.filter(peak_route=route.id)
        for l in current_links:
            related_link_archive = PeakRouteRelatedLinkArchive(route_archive_id=route_archive.id, related_url=l.related_url)
            related_link_archive.save()

        route.status = STATUS_ROUTE_ACTIVE
        route.name = route_name
        route.difficulty = difficulty

        route_metadata_types = request.POST.getlist('chkRouteTypes')
        unique_types = uniq(route_metadata_types)
        route_metadata = '{"types":%s}' % (json.dumps(unique_types))
        route.route_metadata = route_metadata
        route.save()

        return HttpResponseRedirect(reverse('peak_view_route', kwargs={'item_slug':route.item.slug_new_text,'route_id':route_id}))
    else:
        route = PeakRoute.objects.get(id=route_id)

    return render(request, 'items/edit_peak_route.html', {
        'photo_minsize':photo_minsize,
        'peak':peak,
        'route':route
    })

#edit peak
@login_required
def peak_edit_info(request, peak_id):
    peak = Item.objects.get(id=peak_id)
    if request.method == 'POST':
        try:
            print("peak_edit_info called", request.POST.items())
        except Exception:
            pass
        from datetime import datetime
        #process edit peak form
        new_peak_elevation = request.POST.get('peak-elevation')
        init_peak_elevation_feet = request.POST.get('peak-elevation-init-feet')
        init_peak_elevation_meters = request.POST.get('peak-elevation-init-meters')
        save_peak_elevation_correction = False
        new_peak_elevation = new_peak_elevation.replace(',','')
        if new_peak_elevation != '':
            peak_elevation_units = request.POST.get('peak-elevation-units')
            if peak_elevation_units == 'm':
                if new_peak_elevation != init_peak_elevation_meters:
                    save_peak_elevation_correction = True
                new_peak_elevation = float(new_peak_elevation) / .3048
            else:
                if new_peak_elevation != init_peak_elevation_feet:
                    save_peak_elevation_correction = True
            if save_peak_elevation_correction:
                correction = ItemCorrection(status=STATUS_NEW, user=request.user, item=peak, field=FIELDS_ELEVATION, original_value=peak.elevation, new_value=new_peak_elevation, created=datetime.now())
                correction.save()

        new_peak_name = request.POST.get('peak-name')
        if new_peak_name == '':
            new_peak_elevation_meters = float(new_peak_elevation) * .3048
            if peak.is_usa:
                new_peak_name = 'Peak %s ft' % (int(round(float(new_peak_elevation),0)))
            else:
                new_peak_name = 'Peak %s m' % (int(round(new_peak_elevation_meters,0)))
        if (new_peak_name != peak.name):
            correction = ItemCorrection(status=STATUS_NEW, user=request.user, item=peak, field=FIELDS_NAME, original_value=peak.name, new_value=new_peak_name, created=datetime.now())
            correction.save()

        new_peak_prominence = request.POST.get('peak-prominence')
        init_peak_prominence_feet = request.POST.get('peak-prominence-init-feet')
        init_peak_prominence_meters = request.POST.get('peak-prominence-init-meters')
        save_peak_prominence_correction = False
        new_peak_prominence = new_peak_prominence.replace(',','')
        if new_peak_prominence != '':
            peak_prominence_units = request.POST.get('peak-prominence-units')
            if peak_prominence_units == 'm':
                if new_peak_prominence != init_peak_prominence_meters:
                    save_peak_prominence_correction = True
                new_peak_prominence = float(new_peak_prominence) / .3048
            else:
                if new_peak_prominence != init_peak_prominence_feet:
                    save_peak_prominence_correction = True
            if save_peak_prominence_correction:
                correction = ItemCorrection(status=STATUS_NEW, user=request.user, item=peak, field=FIELDS_PROMINENCE, original_value=peak.prominence, new_value=new_peak_prominence, created=datetime.now())
                correction.save()

        new_peak_range = request.POST.get('peak-range')
        if new_peak_range != '':
            if (new_peak_range != peak.range):
                correction = ItemCorrection(status=STATUS_NEW, user=request.user, item=peak, field=FIELDS_RANGE, original_value=peak.range, new_value=new_peak_range, created=datetime.now())
                correction.save()

        new_peak_lat = request.POST.get('peak-lat')
        new_peak_lng = request.POST.get('peak-lng')
        location_updated = request.POST.get('location-updated')
        if (location_updated == 'true'):
            location = Point(float(new_peak_lng), float(new_peak_lat))
            correction = ItemCorrection(status=STATUS_NEW, user=request.user, item=peak, field=FIELDS_COORDS, original_value=str(peak.lat)+','+str(peak.long), new_value=str(new_peak_lat)+','+str(new_peak_lng), location=location, created=datetime.now())
            correction.save()

        other_changes = request.POST.get('peak-other-changes')
        if other_changes != '':
            correction = ItemCorrection(status=STATUS_NEW, user=request.user, item=peak, field=FIELDS_OTHER, original_value='', new_value=other_changes, created=datetime.now())
            correction.save()

        alternate_names = request.POST['peak_alternate_names']
        if alternate_names != '':
            existent_alternate_names = AlternateName.objects.filter(item=peak).all()
            if '|' in alternate_names:
                alternate_names = alternate_names.split('|')
                for name in alternate_names:
                    if name != '':
                        exist = existent_alternate_names.filter(name__icontains=name)
                        if not exist:
                            itemcorrection = ItemCorrection(user=request.user,item = peak, field=5)
                            itemcorrection.new_value = name
                            itemcorrection.save()
                            alternate_name = AlternateNameItemCorrection()
                            alternate_name.name = name
                            alternate_name.item = itemcorrection
                            alternate_name.save()

        peak_countries = request.POST['peak_countries']
        peak_countries_to_remove = request.POST['peak_countries_to_remove']
        if peak_countries != '':
            existent_peak_countries = ItemCountry.objects.filter(item=peak).all()
            if '|' in peak_countries:
                peak_countries = peak_countries.split('|')
                for country in peak_countries:
                    if country != '':
                        exist = existent_peak_countries.filter(country_id=country)
                        if not exist:
                            temp_country = Country.objects.filter(id=country)
                            for c in temp_country:
                                itemcorrection = ItemCorrection(user=request.user,item = peak, field=10)
                                itemcorrection.new_value = c.name
                                itemcorrection.save()
                                new_country = CountryItemCorrection()
                                new_country.country = c
                                new_country.item = itemcorrection
                                new_country.save()
        if peak_countries_to_remove != '':
            existent_peak_countries = ItemCountry.objects.filter(item=peak).all()
            if '|' in peak_countries_to_remove:
                peak_countries_to_remove = peak_countries_to_remove.split('|')
                for country in peak_countries_to_remove:
                    if country != '':
                        exist = existent_peak_countries.filter(country_id=country)
                        if exist:
                            temp_country = Country.objects.filter(id=country)
                            for c in temp_country:
                                itemcorrection = ItemCorrection(user=request.user,item = peak, field=10)
                                itemcorrection.original_value = c.name
                                itemcorrection.new_value = None
                                itemcorrection.save()
                                new_country = CountryItemCorrection()
                                new_country.country = c
                                new_country.item = itemcorrection
                                new_country.save()

        peak_regions = request.POST['peak_regions']
        peak_regions_to_remove = request.POST['peak_regions_to_remove']
        if peak_regions != '':
            existent_peak_regions = ItemRegion.objects.filter(item=peak).all()
            if '|' in peak_regions:
                peak_regions = peak_regions.split('|')
                for region in peak_regions:
                    if region != '':
                        exist = existent_peak_regions.filter(region_id=region)
                        if not exist:
                            temp_region = Region.objects.filter(id=region)
                            new_region = RegionItemCorrection()
                            for r in temp_region:
                                itemcorrection = ItemCorrection(user=request.user,item = peak, field=9)
                                itemcorrection.new_value = r.name
                                itemcorrection.save()
                                new_region.region = r
                                new_region.item = itemcorrection
                                new_region.save()
        if peak_regions_to_remove != '':
            existent_peak_regions = ItemRegion.objects.filter(item=peak).all()
            if '|' in peak_regions_to_remove:
                peak_regions_to_remove = peak_regions_to_remove.split('|')
                for region in peak_regions_to_remove:
                    if region != '':
                        exist = existent_peak_regions.filter(region_id=region)
                        if exist:
                            temp_region = Region.objects.filter(id=region)
                            new_region = RegionItemCorrection()
                            for r in temp_region:
                                itemcorrection = ItemCorrection(user=request.user,item = peak, field=9)
                                itemcorrection.original_value = r.name
                                itemcorrection.new_value = None
                                itemcorrection.save()
                                new_region.region = r
                                new_region.item = itemcorrection
                                new_region.save()

        data = {}
        data['success'] = 'true'
        #    return data
        jsondata = json.dumps(data)
        return HttpResponse(jsondata, content_type='text/html')

    else:

        alternate_names = AlternateName.objects.filter(item=peak).all()

        sql = "select a.id, a.code, a.name from cities_country a, items_item_country b " + \
            "where b.item_id = %s " + \
            "and b.country_id = a.id " + \
            "order by a.name "

        with connection.cursor() as cursor:

            cursor.execute(sql, [peak.id])

            countries = dictfetchall(cursor)

        sql = "select a.id, a.name, a.country_id from cities_region a, items_item_region b " + \
            "where b.item_id = %s " + \
            "and b.region_id = a.id " + \
            "order by a.name "

        with connection.cursor() as cursor:

            cursor.execute(sql, [peak.id])

            regions = dictfetchall(cursor)

        list = []
        for c in countries:
            sql = "select a.id, a.name " + \
                "from cities_region a " + \
                "where a.country_id = %s " + \
                "and not exists (select 1 from items_item_region b where a.id = b.region_id and b.item_id = %s) " + \
                "order by a.name asc"

            with connection.cursor() as cursor:
                cursor.execute(sql, [c['id'],peak.id])
                country_all_regions = dictfetchall(cursor)

            country = {}
            country['country'] = c
            country_regions = []
            for r in regions:
                if r['country_id'] == c['id']:
                    country_regions.append(r)
            country['country_regions'] = country_regions
            country['country_all_regions'] = country_all_regions
            list.append(country)

    return render(request, 'items/peak_edit_info.html', {
        'peak':peak,
        'country_regions':list,
        'alternate_names':alternate_names
    })


#edit peak from map edit mode
@login_required
def peak_edit_info_from_map(request, peak_id):
    peak = Item.objects.get(id=peak_id)
    if request.method == 'POST':
        from datetime import datetime
        #process edit peak form
        new_peak_elevation = request.POST.get('peak-elevation')
        init_peak_elevation_feet = request.POST.get('peak-elevation-init-feet')
        init_peak_elevation_meters = request.POST.get('peak-elevation-init-meters')
        save_peak_elevation_correction = False
        new_peak_elevation = new_peak_elevation.replace(',','')

        peak_location_verified = request.POST.get('peak-location-verified')
        peak_elevation_verified = request.POST.get('peak-elevation-verified')

        if peak_location_verified == 'on' or peak_elevation_verified == 'on':
            if peak_location_verified == 'on':
                peak.location_edited = True
            if peak_elevation_verified == 'on':
                peak.elevation_edited = True
            peak.quick_save();

        if new_peak_elevation != '':
            peak_elevation_units = request.POST.get('peak-elevation-units')
            if peak_elevation_units == 'm':
                if new_peak_elevation != init_peak_elevation_meters:
                    save_peak_elevation_correction = True
                new_peak_elevation = float(new_peak_elevation) / .3048
            else:
                if new_peak_elevation != init_peak_elevation_feet:
                    save_peak_elevation_correction = True
            if save_peak_elevation_correction:
                correction = ItemCorrection(status=STATUS_NEW, user=request.user, item=peak, field=FIELDS_ELEVATION, original_value=peak.elevation, new_value=new_peak_elevation, created=datetime.now())
                correction.save()

        new_peak_name = request.POST.get('peak-name')
        if new_peak_name == '':
            new_peak_elevation_meters = float(new_peak_elevation) * .3048
            if peak.is_usa:
                new_peak_name = 'Peak %s ft' % (int(round(float(new_peak_elevation),0)))
            else:
                new_peak_name = 'Peak %s m' % (int(round(new_peak_elevation_meters,0)))
        if (new_peak_name != peak.name):
            correction = ItemCorrection(status=STATUS_NEW, user=request.user, item=peak, field=FIELDS_NAME, original_value=peak.name, new_value=new_peak_name, created=datetime.now())
            correction.save()

        new_peak_prominence = request.POST.get('peak-prominence')
        init_peak_prominence_feet = request.POST.get('peak-prominence-init-feet')
        init_peak_prominence_meters = request.POST.get('peak-prominence-init-meters')
        save_peak_prominence_correction = False
        new_peak_prominence = new_peak_prominence.replace(',','')
        if new_peak_prominence != '':
            peak_prominence_units = request.POST.get('peak-prominence-units')
            if peak_prominence_units == 'm':
                if new_peak_prominence != init_peak_prominence_meters:
                    save_peak_prominence_correction = True
                new_peak_prominence = float(new_peak_prominence) / .3048
            else:
                if new_peak_prominence != init_peak_prominence_feet:
                    save_peak_prominence_correction = True
            if save_peak_prominence_correction:
                correction = ItemCorrection(status=STATUS_NEW, user=request.user, item=peak, field=FIELDS_PROMINENCE, original_value=peak.prominence, new_value=new_peak_prominence, created=datetime.now())
                correction.save()

        new_peak_range = request.POST.get('peak-range')
        if new_peak_range != '':
            if (new_peak_range != peak.range):
                correction = ItemCorrection(status=STATUS_NEW, user=request.user, item=peak, field=FIELDS_RANGE, original_value=peak.range, new_value=new_peak_range, created=datetime.now())
                correction.save()

        alternate_names = request.POST['peak_alternate_names']
        if alternate_names != '':
            existent_alternate_names = AlternateName.objects.filter(item=peak).all()
            if '|' in alternate_names:
                alternate_names = alternate_names.split('|')
                for name in alternate_names:
                    if name != '':
                        exist = existent_alternate_names.filter(name__icontains=name)
                        if not exist:
                            itemcorrection = ItemCorrection(user=request.user,item = peak, field=5)
                            itemcorrection.new_value = name
                            itemcorrection.save()
                            alternate_name = AlternateNameItemCorrection()
                            alternate_name.name = name
                            alternate_name.item = itemcorrection
                            alternate_name.save()

        peak_countries = request.POST['peak_countries']
        peak_countries_to_remove = request.POST['peak_countries_to_remove']
        if peak_countries != '':
            existent_peak_countries = ItemCountry.objects.filter(item=peak).all()
            if '|' in peak_countries:
                peak_countries = peak_countries.split('|')
                for country in peak_countries:
                    if country != '':
                        exist = existent_peak_countries.filter(country_id=country)
                        if not exist:
                            temp_country = Country.objects.filter(id=country)
                            for c in temp_country:
                                itemcorrection = ItemCorrection(user=request.user,item = peak, field=10)
                                itemcorrection.new_value = c.name
                                itemcorrection.save()
                                new_country = CountryItemCorrection()
                                new_country.country = c
                                new_country.item = itemcorrection
                                new_country.save()
        if peak_countries_to_remove != '':
            existent_peak_countries = ItemCountry.objects.filter(item=peak).all()
            if '|' in peak_countries_to_remove:
                peak_countries_to_remove = peak_countries_to_remove.split('|')
                for country in peak_countries_to_remove:
                    if country != '':
                        exist = existent_peak_countries.filter(country_id=country)
                        if exist:
                            temp_country = Country.objects.filter(id=country)
                            for c in temp_country:
                                itemcorrection = ItemCorrection(user=request.user,item = peak, field=10)
                                itemcorrection.original_value = c.name
                                itemcorrection.new_value = None
                                itemcorrection.save()
                                new_country = CountryItemCorrection()
                                new_country.country = c
                                new_country.item = itemcorrection
                                new_country.save()

        peak_regions = request.POST['peak_regions']
        peak_regions_to_remove = request.POST['peak_regions_to_remove']
        if peak_regions != '':
            existent_peak_regions = ItemRegion.objects.filter(item=peak).all()
            if '|' in peak_regions:
                peak_regions = peak_regions.split('|')
                for region in peak_regions:
                    if region != '':
                        exist = existent_peak_regions.filter(region_id=region)
                        if not exist:
                            temp_region = Region.objects.filter(id=region)
                            new_region = RegionItemCorrection()
                            for r in temp_region:
                                itemcorrection = ItemCorrection(user=request.user,item = peak, field=9)
                                itemcorrection.new_value = r.name
                                itemcorrection.save()
                                new_region.region = r
                                new_region.item = itemcorrection
                                new_region.save()
        if peak_regions_to_remove != '':
            existent_peak_regions = ItemRegion.objects.filter(item=peak).all()
            if '|' in peak_regions_to_remove:
                peak_regions_to_remove = peak_regions_to_remove.split('|')
                for region in peak_regions_to_remove:
                    if region != '':
                        exist = existent_peak_regions.filter(region_id=region)
                        if exist:
                            temp_region = Region.objects.filter(id=region)
                            new_region = RegionItemCorrection()
                            for r in temp_region:
                                itemcorrection = ItemCorrection(user=request.user,item = peak, field=9)
                                itemcorrection.original_value = r.name
                                itemcorrection.new_value = None
                                itemcorrection.save()
                                new_region.region = r
                                new_region.item = itemcorrection
                                new_region.save()

        data = {}
        data['success'] = 'true'
        #    return data
        jsondata = json.dumps(data)
        return HttpResponse(jsondata, content_type='text/html')

    else:

        data = {}
        data['success'] = 'false'
        #    return data
        jsondata = json.dumps(data)
        return HttpResponse(jsondata, content_type='text/html')


#add new peak
@login_required
def item_add(request):
    if request.method == 'POST':
        from datetime import datetime
        response_data = {}
        is_usa = False
        #process add peak form
        peak = TempItem(user=request.user, status=1)

        new_peak_elevation = request.POST.get('peak-elevation')
        peak_elevation_units = request.POST.get('peak-elevation-units')
        if new_peak_elevation != '':
            if peak_elevation_units == 'm':
                new_peak_elevation = float(new_peak_elevation) / .3048
            peak.elevation = new_peak_elevation
        else:
            return HttpResponseBadRequest("Elevation cant be empty")

        new_peak_name = request.POST.get('peak-name')
        if new_peak_name == '':
            new_peak_elevation_meters = float(new_peak_elevation) * .3048
            new_peak_name = 'Peak %s ft' % (int(round(float(new_peak_elevation),0)))
            peak.default_name = True
        if new_peak_name != '':
            peak.name = new_peak_name

        new_peak_prominence = request.POST.get('peak-prominence')
        peak_prominence_units = request.POST.get('peak-prominence-units')
        if new_peak_prominence != '':
            if peak_prominence_units == 'm':
                new_peak_prominence = float(new_peak_prominence) / .3048
            peak.prominence = new_peak_prominence

        new_peak_range = request.POST.get('peak-range')
        if new_peak_range != '':
            peak.range = new_peak_range

        new_peak_lat = request.POST.get('peak-lat')
        new_peak_lng = request.POST.get('peak-lng')
        if new_peak_lat != '' and new_peak_lng != '':
            peak.lat = new_peak_lat
            peak.long = new_peak_lng
            #peak.location = Point(float(new_peak_lng),float(new_peak_lat))

        peak.save()

        #save regions
        from mapbox import Geocoder
        import json

        geocoder = Geocoder(access_token=settings.MAPBOX_ACCESS_TOKEN)

        response = geocoder.reverse(new_peak_lng, lat=new_peak_lat)
        features = sorted(response.geojson()['features'], key=lambda x: x['id'])
        for f in features:
            region_name = None
            country_name = None
            region_match = False
            if f['place_type'][0] == "country":
                country_name = f['properties']['short_code']
                countries = Country.objects.filter(code__iexact=country_name)
                #delete existing countries
                sql = "delete from tempitems_tempitem_country where tempitem_id = %s "
                with connection.cursor() as cursor:
                    cursor.execute(sql, [peak.id])
                for country in countries:
                    #add new country
                    sql = "insert into tempitems_tempitem_country (tempitem_id, country_id) values (%s, %s)"
                    with connection.cursor() as cursor:
                        cursor.execute(sql, [peak.id, country.id])
                    #is usa?
                    if country.name == 'United States':
                        is_usa = True

            if f['place_type'][0] == "region":
                region_name = f['text']
                regions = Region.objects.filter(name__iexact=region_name, country_id=country.id)
                if regions:
                    region_match = True
                    #delete existing regions
                    sql = "delete from tempitems_tempitem_region where tempitem_id = %s "
                    with connection.cursor() as cursor:
                        cursor.execute(sql, [peak.id])
                    for region in regions:
                        #add new region
                        sql = "insert into tempitems_tempitem_region (tempitem_id, region_id) values (%s, %s)"
                        with connection.cursor() as cursor:
                            cursor.execute(sql, [peak.id, region.id])
                if not region_match:
                    #try matching "google region name"
                    regions = Region.objects.filter(google_region_name__iexact=region_name, country_id=country.id)
                    if regions:
                        region_match = True
                        #delete existing regions
                        sql = "delete from tempitems_tempitem_region where tempitem_id = %s "
                        with connection.cursor() as cursor:
                            cursor.execute(sql, [peak.id])
                        for region in regions:
                            #add new region
                            sql = "insert into tempitems_tempitem_region (tempitem_id, region_id) values (%s, %s)"
                            with connection.cursor() as cursor:
                                cursor.execute(sql, [peak.id, region.id])

        if peak.default_name and not is_usa:
            new_peak_name = 'Peak %s m' % (int(round(new_peak_elevation_meters, 0)))
            peak.name = new_peak_name
            peak.save()

        response_data['success'] = 'true'
        return HttpResponse(json.dumps(response_data), content_type="application/json")

    return render(request, 'items/peak_add.html', {
    })

#add new peak from map
@login_required
def item_add_from_map(request):
    if request.method == 'POST':
        from datetime import datetime
        response_data = {}
        is_usa = False
        #process add peak form
        peak = TempItem(user=request.user, status=1)

        new_peak_elevation = request.POST.get('peak-elevation')
        peak_elevation_units = request.POST.get('peak-elevation-units')
        if new_peak_elevation != '':
            if peak_elevation_units == 'm':
                new_peak_elevation = float(new_peak_elevation) / .3048
            peak.elevation = new_peak_elevation

        new_peak_name = request.POST.get('peak-name')
        if new_peak_name == '':
            new_peak_elevation_meters = float(new_peak_elevation) * .3048
            new_peak_name = 'Peak %s ft' % (int(round(float(new_peak_elevation), 0)))
            peak.default_name = True
        if new_peak_name != '':
            peak.name = new_peak_name

        new_peak_prominence = request.POST.get('peak-prominence')
        peak_prominence_units = request.POST.get('peak-prominence-units')
        if new_peak_prominence != '':
            if peak_prominence_units == 'm':
                new_peak_prominence = float(new_peak_prominence) / .3048
            peak.prominence = new_peak_prominence

        new_peak_range = request.POST.get('peak-range')
        if new_peak_range != '':
            peak.range = new_peak_range

        new_peak_lat = request.POST.get('peak-lat')
        new_peak_lng = request.POST.get('peak-lng')
        if new_peak_lat != '' and new_peak_lng != '':
            peak.lat = new_peak_lat
            peak.long = new_peak_lng

        peak.save()

        #save regions
        from mapbox import Geocoder
        import json

        geocoder = Geocoder(access_token=settings.MAPBOX_ACCESS_TOKEN)

        response = geocoder.reverse(new_peak_lng, lat=new_peak_lat)
        features = sorted(response.geojson()['features'], key=lambda x: x['id'])
        for f in features:
            region_name = None
            country_name = None
            region_match = False
            if f['place_type'][0] == "country":
                country_name = f['properties']['short_code']
                countries = Country.objects.filter(code__iexact=country_name)
                # delete existing countries
                sql = "delete from tempitems_tempitem_country where tempitem_id = %s "
                with connection.cursor() as cursor:
                    cursor.execute(sql, [peak.id])
                for country in countries:
                    # add new country
                    sql = "insert into tempitems_tempitem_country (tempitem_id, country_id) values (%s, %s)"
                    with connection.cursor() as cursor:
                        cursor.execute(sql, [peak.id, country.id])
                    # is usa?
                    if country.name == 'United States':
                        is_usa = True

            if f['place_type'][0] == "region":
                region_name = f['text']
                regions = Region.objects.filter(name__iexact=region_name, country_id=country.id)
                if regions:
                    region_match = True
                    # delete existing regions
                    sql = "delete from tempitems_tempitem_region where tempitem_id = %s "
                    with connection.cursor() as cursor:
                        cursor.execute(sql, [peak.id])
                    for region in regions:
                        # add new region
                        sql = "insert into tempitems_tempitem_region (tempitem_id, region_id) values (%s, %s)"
                        with connection.cursor() as cursor:
                            cursor.execute(sql, [peak.id, region.id])
                if not region_match:
                    # try matching "google region name"
                    regions = Region.objects.filter(google_region_name__iexact=region_name, country_id=country.id)
                    if regions:
                        region_match = True
                        # delete existing regions
                        sql = "delete from tempitems_tempitem_region where tempitem_id = %s "
                        with connection.cursor() as cursor:
                            cursor.execute(sql, [peak.id])
                        for region in regions:
                            # add new region
                            sql = "insert into tempitems_tempitem_region (tempitem_id, region_id) values (%s, %s)"
                            with connection.cursor() as cursor:
                                cursor.execute(sql, [peak.id, region.id])

        if peak.default_name and not is_usa:
            new_peak_name = 'Peak %s m' % (int(round(new_peak_elevation_meters, 0)))
            peak.name = new_peak_name
            peak.save()

        data = {}
        data['success'] = 'true'
        #    return data
        jsondata = json.dumps(data)
        return HttpResponse(jsondata, content_type='text/html')

    else:

        data = {}
        data['success'] = 'false'
        #    return data
        jsondata = json.dumps(data)
        return HttpResponse(jsondata, content_type='text/html')

@login_required
def s3_route_photo_upload(request):
    from PIL import Image
    route_id = request.POST.get('route_id', False)
    step_number = request.POST.get('step_number', False)
    thumb_file_path = ''
    if route_id:
        route = PeakRoute.objects.get(id=route_id)
        route_step = PeakRouteStep(route=route,step_number=step_number)
        if request.POST.get('name'):
            filename = request.POST.get('name')
            key = request.POST.get('key')
            basefile = key.split("/")[-1]
            extension = os.path.splitext(filename)[1][1:].lower()
            if extension == 'png':
                extension = 'PNG'
            else:
                extension = 'JPEG'
            upload_path = key
            thumb_file_path = 'images/items/routes/cache/%s' % (basefile)

            upload_images_to_s3_with_thumbnails(upload_path, thumb_file_path, extension)

    try:
        f = default_storage.open(upload_path, 'rb')
        image = Image.open(f)
        width, height = image.size
    except Exception as e:
        width,height = [-1,-1]

    if width >= settings.MIN_PEAK_IMG_WIDTH and height >= settings.MIN_PEAK_IMG_HEIGHT:
        success = 'true'
        return render(request, 'items/ajax/show_uploaded_image.html', {'success':success, 'photo_object_id':route_step.id, 'photo_object_file':upload_path, 'photo_object_url':thumb_file_path+'.745x500_q95_crop-top.jpg'})
    else:
        success = 'false'
        return render(request, 'items/ajax/show_uploaded_image.html', {'success':success, 'photo_object_id':route_step.id, 'photo_object_url':''})


@login_required
def s3_summit_photo_upload(request):
    from PIL import Image, ImageFilter, ExifTags
    from PIL.ExifTags import TAGS, GPSTAGS

    from io import BytesIO
    import os.path

    from django.core.files.storage import default_storage

    lat = None
    lon = None
    image_width = None
    image_height = None

    def get_if_exist(data, key):
        if key in data:
            return data[key]
        return None

    def convert_to_degrees(value):
        """Helper function to convert the GPS coordinates
        stored in the EXIF to degrees in float format"""
        #d0 = value[0][0]
        #d1 = value[0][1]
        d0 = value[0]._val.numerator
        d1 = value[0]._val.denominator
        d = float(d0) / float(d1)
        #m0 = value[1][0]
        #m1 = value[1][1]
        m0 = value[1]._val.numerator
        m1 = value[1]._val.denominator
        m = float(m0) / float(m1)

        #s0 = value[2][0]
        #s1 = value[2][1]
        s0 = value[2]._val.numerator
        s1 = value[2]._val.denominator
        s = float(s0) / float(s1)

        return d + (m / 60.0) + (s / 3600.0)

    def get_lat(exif_data):
        """Returns the latitude and longitude, if available, from the
        provided exif_data (obtained through get_exif_data above)"""
        # print(exif_data)
        if 'GPSInfo' in exif_data:
            gps_info = exif_data["GPSInfo"]
            gps_latitude = get_if_exist(gps_info, "GPSLatitude")
            gps_latitude_ref = get_if_exist(gps_info, 'GPSLatitudeRef')
            if gps_latitude and gps_latitude_ref:
                lat = convert_to_degrees(gps_latitude)
                if gps_latitude_ref != "N":
                    lat = 0 - lat
                return lat
            else:
                return None
        else:
            return None

    def get_lon(exif_data):
        """Returns the latitude and longitude, if available, from the
        provided exif_data (obtained through get_exif_data above)"""
        # print(exif_data)
        if 'GPSInfo' in exif_data:
            gps_info = exif_data["GPSInfo"]
            gps_longitude = get_if_exist(gps_info, 'GPSLongitude')
            gps_longitude_ref = get_if_exist(gps_info, 'GPSLongitudeRef')
            if gps_longitude and gps_longitude_ref:
                lon = convert_to_degrees(gps_longitude)
                if gps_longitude_ref != "E":
                    lon = 0 - lon
                return lon
            else:
                return None
        else:
            return None

    def get_direction(exif_data):
        if 'GPSInfo' in exif_data:
            gps_info = exif_data["GPSInfo"]
            gps_direction = get_if_exist(gps_info, 'GPSImgDirection')
            if gps_direction:
                return gps_direction
            else:
                return None
        else:
            return None

    def get_date_time(exif_data):
        if 'DateTimeOriginal' in exif_data:
            date_and_time = exif_data['DateTimeOriginal']
            return date_and_time

    def get_image_height(exif_data):
        if 'ExifImageHeight' in exif_data:
            image_height = exif_data['ExifImageHeight']
            return image_height

    def get_image_width(exif_data):
        if 'ExifImageWidth' in exif_data:
            image_width = exif_data['ExifImageWidth']
            return image_width

    def get_altitude(exif_data):
        if 'GPSInfo' in exif_data:
            gps_info = exif_data["GPSInfo"]
            gps_altitude = get_if_exist(gps_info, 'GPSAltitude')
            if gps_altitude:
                return gps_altitude
            else:
                return None
        else:
            return None

    summit_id = request.POST.get('summit_id', False)
    photo_index = request.POST.get('photo_index', 0)
    if summit_id:
        summit = SummitLog.objects.get(id=summit_id)
        category = ItemPhotoCategory.objects.get(name='summit')
        if request.POST.get('name'):
            filename = request.POST.get('name')
            key = request.POST.get('key')
            uuid = request.POST.get('uuid')
            basefile = key.split("/")[-1]
            extension = os.path.splitext(filename)[1][1:].lower()
            if extension == 'png':
                extension = 'PNG'
            else:
                extension = 'JPEG'
            upload_path = key
            thumb_file_path = 'images/items/users/cache/%s' % (basefile)

            upload_images_to_s3_with_thumbnails(upload_path, thumb_file_path, extension)

    f = default_storage.open(upload_path, 'rb')
    image = Image.open(f)
    width, height = image.size

    try:
        image = fix_image_orientation(image)
        exif_data = {}
        info = image._getexif()
        if info:
            for tag, value in info.items():
                decoded = TAGS.get(tag, tag)
                if decoded == "GPSInfo":
                    gps_data = {}
                    for t in value:
                        sub_decoded = GPSTAGS.get(t, t)
                        gps_data[sub_decoded] = value[t]

                    exif_data[decoded] = gps_data
                else:
                    exif_data[decoded] = value
        image_height = get_image_height(exif_data)
        image_width = get_image_width(exif_data)
        lat = get_lat(exif_data)
        lon = get_lon(exif_data)

    except (AttributeError, KeyError, IndexError):
        # cases: image don't have getexif
        pass

    if width >= settings.MIN_PEAK_IMG_WIDTH and height >= settings.MIN_PEAK_IMG_HEIGHT:
        photo_object = ItemPhoto(user_id=0, item_id=0, category=category, summit_log=summit, image=upload_path, uuid=uuid, photo_index=photo_index, photo_lat=lat, photo_lng=lon, image_width=image_width, image_height=image_height)
        photo_object.save()
        success = 'true'
        return render(request, 'items/ajax/show_uploaded_image.html', {'success':success, 'photo_object_id':photo_object.id, 'photo_object_file':upload_path, 'photo_object_url':thumb_file_path+'.745x500_q95_crop-top.jpg'})
    else:
        photo_object = None
        success = 'false'
        return render(request, 'items/ajax/show_uploaded_image.html', {'success':success, 'photo_object_id':0, 'photo_object_url':''})

@login_required
def s3_summit_photo_init(request):
    summit_id = request.GET.get('summit_id', False)
    if summit_id:
        summit = SummitLog.objects.get(id=summit_id)
        photos = ItemPhoto.objects.filter(summit_log=summit).order_by('id')

    return render(request, 'items/ajax/show_summit_images.html', {
        'photos': photos
    })

@login_required
def s3_route_photo_init(request):
    route_id = request.GET.get('route_id', False)
    step_number = request.GET.get('step_number', False)
    if route_id:
        route = PeakRoute.objects.get(id=route_id)
        route_step = PeakRouteStep.objects.get(route=route,step_number=step_number)

    return render(request, 'items/ajax/show_route_step_images.html', {
        'route_step': route_step
    })

@login_required
def upload_photo(request):
    from PIL import Image
    import os.path
    import time
    import re
    timestr = time.strftime("%Y%m%d-%H%M%S")
    summit_id = request.GET.get('summit_id', False)
    if summit_id:
        summit = SummitLog.objects.get(id=summit_id)
        category = ItemPhotoCategory.objects.get(name='summit')
        if request.GET.get('qqfile'):
            filename = request.GET.get('qqfile')
            filename = re.sub('[^a-zA-Z0-9-_*.]', '', filename)
            base = os.path.splitext(filename)[0]
            extension = os.path.splitext(filename)[1][1:].lower()
            filename = slugify(base) + '.' + extension
            if (extension == 'png'):
                extension = 'PNG'
            else:
                extension = 'JPEG'
            item = summit.item
            upload_path = 'items/users/%s-%s-%s' % (item.slug, timestr, filename)
            thumb_file_path = 'images/items/users/cache/%s-%s-%s' % (item.slug, timestr, filename)
            file = default_storage.open(upload_path, 'w')
            file.write(request.body)
            file.close()

            upload_images_to_s3_with_thumbnails(upload_path, thumb_file_path, extension)
    try:
        f = default_storage.open(upload_path, 'rb')
        image = Image.open(f)
        width, height = image.size
    except Exception as e:
        width,height = [-1,-1]

    if width >= settings.MIN_PEAK_IMG_WIDTH and height >= settings.MIN_PEAK_IMG_HEIGHT:
        photo_object = ItemPhoto(user_id=0, item_id=0, category=category, summit_log=summit, image=upload_path)
        photo_object.save()
        success = 'true'
        return render(request, 'items/ajax/show_uploaded_image.html', {'success':success, 'photo_object_id':photo_object.id, 'photo_object_file':upload_path, 'photo_object_url':thumb_file_path+'.745x500_q95_crop-top.jpg'})
    else:
        photo_object = None
        success = 'false'
        return render(request, 'items/ajax/show_uploaded_image.html', {'success':success, 'photo_object_id':photo_object.id, 'photo_object_url':''})

@login_required
def upload_gpx(request):
    import gpxpy
    import gpxpy.gpx
    import math as mod_math
    from geopy import distance as gpdistance
    summit_id = request.GET.get('summit_id', False)
    success = 'false'
    if summit_id:
        length_2d = 0
        uphill = 0
        downhill = 0
        moving_time = 0
        stopped_time = 0
        moving_distance = 0
        stopped_distance = 0
        max_speed = 0
        start_elevation = 0
        end_elevation = 0
        total_hours = 0
        total_minutes = 0
        distance_to_summit = 0
        distance_to_summit_temp = 0
        distance_to_summit_time = 0
        distance_to_summit_hours = 0
        distance_to_summit_minutes = 0
        summit = SummitLog.objects.get(id=summit_id)
        peak_coords = (summit.item.lat, summit.item.long)
        if request.GET.get('qqfile'):
            filename = request.GET.get('qqfile')
#            upload_path = 'items/users/summit-%s' % utils.sanetize_filename(photo_object.id, filename)
            item = summit.item
            if summit.date:
                count = item.photos.filter(category__name='summit', summit_log__date=summit.date).count()
                upload_path = 'gpx/%s-%s-%s' % (item.slug, summit.date, utils.sanetize_filename(count, filename))
            else:
                count = item.photos.filter(category__name='summit', summit_log__date_entered=False).count()
                upload_path = 'gpx/%s-%s' % (item.slug, utils.sanetize_filename(count, filename))
            #destination = open('%s/%s' % (settings.MEDIA_ROOT, upload_path), 'wb+')
            #destination.write(request.raw_post_data)
            #destination.close()
            file = default_storage.open(upload_path, 'w')
            file.write(request.body)
            file.close()
            #parse gpx
            try:
                gpx = gpxpy.parse(request.body)
                length_2d = gpx.length_2d()
                uphill, downhill = gpx.get_uphill_downhill()
                moving_time, stopped_time, moving_distance, stopped_distance, max_speed = gpx.get_moving_data()

                if length_2d is None:
                    length_2d = 0
                if uphill is None:
                    uphill = 0
                if downhill is None:
                    downhill = 0
                if moving_time is None:
                    moving_time = 0
                if stopped_time is None:
                    stopped_time = 0
                if moving_distance is None:
                    moving_distance = 0
                if stopped_distance is None:
                    stopped_distance = 0
                if max_speed is None:
                    max_speed = 0

                total_time = moving_time + stopped_time
                total_minutes = mod_math.floor(total_time / 60.)
                total_hours = mod_math.floor(total_minutes / 60.)
                start_elevation = 0
                start_lat = 0
                start_lon = 0
                end_lat = 0
                end_lon = 0
                previous_point = None
                for track in gpx.tracks:
                    for segment in track.segments:
                        for point in segment.points:
                            if (start_elevation == 0):
                                start_elevation = point.elevation
                            if (start_lat == 0):
                                start_lat = point.latitude
                            if (start_lon == 0):
                                start_lon = point.longitude
                            end_elevation = point.elevation
                            end_lat = point.latitude
                            end_lon = point.longitude
                            if previous_point is not None and distance_to_summit == 0:
                                #add to running distance to summit
                                distance_to_summit_temp = distance_to_summit_temp + point.distance_2d(previous_point)
                                if point.time and previous_point.time:
                                    temp_time = point.time - previous_point.time
                                    distance_to_summit_time = distance_to_summit_time + temp_time.total_seconds()
                                #calculate distance to summit
                                #gpdistance.VincentyDistance.ELLIPSOID = 'WGS-84'
                                current_point = (point.latitude, point.longitude)
                                #miles = gpdistance.distance(peak_coords, current_point).mi
                                miles = gpdistance.geodesic(peak_coords, current_point).mi
                                if miles < 0.113636:
                                    distance_to_summit = distance_to_summit_temp/1609
                            previous_point = point

                length_2d = length_2d/1609
                start_elevation = start_elevation/0.3048
                end_elevation = end_elevation/0.3048
                uphill = uphill/0.3048
                total_minutes = total_minutes % 60
                distance_to_summit_minutes = mod_math.floor(distance_to_summit_time / 60.)
                distance_to_summit_hours = mod_math.floor(distance_to_summit_minutes / 60.)
                distance_to_summit_minutes = distance_to_summit_minutes % 60
                success = 'true'

            except:
                print('Invalid GPX file.')
        else:
            print('No file.')

        summit.gpx_file = upload_path
        summit.quick_save()
    else:
        print('No summit id.')

    return render(request, 'items/ajax/show_uploaded_gpx.html', {'success':success, 'gpx_file':upload_path, 'start_index':0, 'end_index':500, 'length_2d':round(length_2d,2), 'start_lat':start_lat, 'start_lon':start_lon, 'start_elevation':int(start_elevation), 'end_lat':end_lat, 'end_lon':end_lon, 'end_elevation':int(end_elevation), 'uphill':int(uphill), 'total_hours':int(total_hours), 'total_minutes':int(total_minutes), 'distance_to_summit_hours':int(distance_to_summit_hours), 'distance_to_summit_minutes':int(distance_to_summit_minutes), 'distance_to_summit':round(distance_to_summit,2)})

def process_route_gpx(request, peak_id):
    import gpxpy
    import gpxpy.gpx
    import math as mod_math
    from geopy import distance as gpdistance
    gpx_file = request.POST.get('gpx_file', False)
    start_index = request.POST.get('start_index', 0)
    end_index = request.POST.get('end_index', 500)
    request_index = request.POST.get('request_index', None)
    success = 'false'
    if gpx_file:
        item = Item.objects.get(id=peak_id)
        peak_coords = (item.lat, item.long)
        start_lat = 0
        start_lon = 0
        start_elevation = 0
        end_lat = 0
        end_lon = 0
        end_elevation = 0
        uphill = 0
        distance_to_summit = 0
        distance_to_summit_temp = 0
        temp_time = 0
        distance_to_summit_time = 0
        distance_to_summit_hours = 0
        distance_to_summit_minutes = 0
        length_2d = 0
        elevations = []
        reached_summit = False

        #parse gpx
        try:
            f = default_storage.open(gpx_file, 'r')
            gpx = gpxpy.parse(f)

            previous_point = None
            for track in gpx.tracks:
                for segment in track.segments:
                    total_points = len(segment.points)
                    point_step = float(total_points) / 500
                    start_point = float(start_index) * point_step
                    end_point = float(end_index) * point_step
                    point_counter = 1
                    for point in segment.points:
                        if start_point <= point_counter <= end_point:
                            elevations.append(point.elevation)
                            if (start_lat == 0):
                                start_lat = point.latitude
                            if (start_lon == 0):
                                start_lon = point.longitude
                            if (start_elevation) == 0:
                                start_elevation = point.elevation
                            end_lat = point.latitude
                            end_lon = point.longitude
                            end_elevation = point.elevation
                            if previous_point is not None:
                                #add to running total distance
                                length_2d = length_2d + point.distance_2d(previous_point)
                                if point.time and previous_point.time:
                                    temp_time = point.time - previous_point.time
                                    distance_to_summit_time = distance_to_summit_time + temp_time.total_seconds()
                            previous_point = point
                        point_counter += 1

            uphill, downhill = calculate_uphill_downhill(elevations, distance_to_summit_time)
            distance_to_summit_minutes = mod_math.floor(distance_to_summit_time / 60.)
            distance_to_summit_hours = mod_math.floor(distance_to_summit_minutes / 60.)
            distance_to_summit_minutes = distance_to_summit_minutes % 60

            #convert to miles and feet
            length_2d = length_2d * 0.000621371
            start_elevation = start_elevation * 3.28084
            end_elevation = end_elevation * 3.28084
            uphill = uphill * 3.28084
            distance_to_summit = distance_to_summit * 0.000621371
            success = 'true'

        except:
            print('Invalid GPX file.')
    else:
        print('No GPX file.')

    return render(request, 'items/ajax/show_cropped_gpx.html', {'success':success, 'request_index':request_index, 'gpx_file':gpx_file, 'start_index':start_index, 'end_index':end_index, 'length_2d':round(length_2d,2), 'start_lat':start_lat, 'start_lon':start_lon, 'start_elevation':int(start_elevation), 'end_lat':end_lat, 'end_lon':end_lon, 'end_elevation':int(end_elevation), 'uphill':int(uphill), 'total_hours':int(distance_to_summit_hours), 'total_minutes':int(distance_to_summit_minutes), 'distance_to_summit_hours':int(distance_to_summit_hours), 'distance_to_summit_minutes':int(distance_to_summit_minutes), 'distance_to_summit':round(length_2d,2)})

def calculate_uphill_downhill(elevations, duration):
    if not elevations:
        return 0, 0

    size = len(elevations)

    #need at least twenty points to calculate elevation
    if size < 20:
        return 0, 0

    #did we get duration?
    if not duration:
        duration = 0

    #smoothing factor is 40 if we have at least one point every ten seconds and at least 40 points, otherwise 20
    if duration / size <= 10 and size >= 40:
        smoothing_factor = 40
    else:
        smoothing_factor = 20

    def __filter(n):
        current_ele = elevations[n]
        if current_ele is None:
            return False
        if smoothing_factor == 20:
            if 9 < n < size - 10:
                previous_ele1 = elevations[n-1]
                previous_ele2 = elevations[n-2]
                previous_ele3 = elevations[n-3]
                previous_ele4 = elevations[n-4]
                previous_ele5 = elevations[n-5]
                previous_ele6 = elevations[n-6]
                previous_ele7 = elevations[n-7]
                previous_ele8 = elevations[n-8]
                previous_ele9 = elevations[n-9]
                next_ele1 = elevations[n+1]
                next_ele2 = elevations[n+2]
                next_ele3 = elevations[n+3]
                next_ele4 = elevations[n+4]
                next_ele5 = elevations[n+5]
                next_ele6 = elevations[n+6]
                next_ele7 = elevations[n+7]
                next_ele8 = elevations[n+8]
                next_ele9 = elevations[n+9]
                if previous_ele1 is not None and previous_ele2 is not None and previous_ele3 is not None and previous_ele4 is not None and previous_ele5 is not None and previous_ele6 is not None and previous_ele7 is not None and previous_ele8 is not None and previous_ele9 is not None and current_ele is not None and next_ele1 is not None and next_ele2 is not None and next_ele3 is not None and next_ele4 is not None and next_ele5 is not None and next_ele6 is not None and next_ele7 is not None and next_ele8 is not None and next_ele9 is not None:
                    return previous_ele1*.05 + previous_ele2*.05 + previous_ele3*.05 + previous_ele4*.05 + previous_ele5*.05 + previous_ele6*.05 + previous_ele7*.05 + previous_ele8*.05 + previous_ele9*.05 + current_ele*.1 + next_ele1*.05 + next_ele2*.05 + next_ele3*.05 + next_ele4*.05 + next_ele5*.05 + next_ele6*.05 + next_ele7*.05 + next_ele8*.05 + next_ele9*.05
        else:
            if 19 < n < size - 20:
                previous_ele1 = elevations[n-1]
                previous_ele2 = elevations[n-2]
                previous_ele3 = elevations[n-3]
                previous_ele4 = elevations[n-4]
                previous_ele5 = elevations[n-5]
                previous_ele6 = elevations[n-6]
                previous_ele7 = elevations[n-7]
                previous_ele8 = elevations[n-8]
                previous_ele9 = elevations[n-9]
                previous_ele10 = elevations[n-10]
                previous_ele11 = elevations[n-11]
                previous_ele12 = elevations[n-12]
                previous_ele13 = elevations[n-13]
                previous_ele14 = elevations[n-14]
                previous_ele15 = elevations[n-15]
                previous_ele16 = elevations[n-16]
                previous_ele17 = elevations[n-17]
                previous_ele18 = elevations[n-18]
                previous_ele19 = elevations[n-19]
                next_ele1 = elevations[n+1]
                next_ele2 = elevations[n+2]
                next_ele3 = elevations[n+3]
                next_ele4 = elevations[n+4]
                next_ele5 = elevations[n+5]
                next_ele6 = elevations[n+6]
                next_ele7 = elevations[n+7]
                next_ele8 = elevations[n+8]
                next_ele9 = elevations[n+9]
                next_ele10 = elevations[n+10]
                next_ele11 = elevations[n+11]
                next_ele12 = elevations[n+12]
                next_ele13 = elevations[n+13]
                next_ele14 = elevations[n+14]
                next_ele15 = elevations[n+15]
                next_ele16 = elevations[n+16]
                next_ele17 = elevations[n+17]
                next_ele18 = elevations[n+18]
                next_ele19 = elevations[n+19]
                if previous_ele1 is not None and previous_ele2 is not None and previous_ele3 is not None and previous_ele4 is not None and previous_ele5 is not None and previous_ele6 is not None and previous_ele7 is not None and previous_ele8 is not None and previous_ele9 is not None and previous_ele10 is not None and previous_ele11 is not None and previous_ele12 is not None and previous_ele13 is not None and previous_ele14 is not None and previous_ele15 is not None and previous_ele16 is not None and previous_ele17 is not None and previous_ele18 is not None and previous_ele19 is not None and current_ele is not None and next_ele1 is not None and next_ele2 is not None and next_ele3 is not None and next_ele4 is not None and next_ele5 is not None and next_ele6 is not None and next_ele7 is not None and next_ele8 is not None and next_ele9 is not None and next_ele10 is not None and next_ele11 is not None and next_ele12 is not None and next_ele13 is not None and next_ele14 is not None and next_ele15 is not None and next_ele16 is not None and next_ele17 is not None and next_ele18 is not None and next_ele19 is not None:
                    return previous_ele1*.025 + previous_ele2*.025 + previous_ele3*.025 + previous_ele4*.025 + previous_ele5*.025 + previous_ele6*.025 + previous_ele7*.025 + previous_ele8*.025 + previous_ele9*.025 + previous_ele10*.025 + previous_ele11*.025 + previous_ele12*.025 + previous_ele13*.025 + previous_ele14*.025 + previous_ele15*.025 + previous_ele16*.025 + previous_ele17*.025 + previous_ele18*.025 + previous_ele19*.025 + current_ele*.05 + next_ele1*.025 + next_ele2*.025 + next_ele3*.025 + next_ele4*.025 + next_ele5*.025 + next_ele6*.025 + next_ele7*.025 + next_ele8*.025 + next_ele9*.025 + next_ele10*.025 + next_ele11*.025 + next_ele12*.025 + next_ele13*.025 + next_ele14*.025 + next_ele15*.025 + next_ele16*.025 + next_ele17*.025 + next_ele18*.025 + next_ele19*.025
        return current_ele

    smoothed_elevations = list(map(__filter, range(size)))

    uphill, downhill, uphill_trend, downhill_trend = 0., 0., 0., 0.

    for n, elevation in enumerate(smoothed_elevations):
        if n > 0 and elevation is not None and smoothed_elevations is not None:
            d = elevation - smoothed_elevations[n-1]
            if d > 0:
                downhill_trend = 0
                uphill_trend += d
                if uphill_trend > 6:
                    uphill += d
            else:
                uphill_trend = 0
                downhill_trend -= d
                if downhill_trend < 6:
                    downhill -= d

    return uphill, downhill

@login_required
def s3_summit_gpx_upload(request):
    import gpxpy
    import gpxpy.gpx
    import math as mod_math
    from geopy import distance as gpdistance
    summit_id = request.POST.get('summit_id', False)
    success = 'false'
    valid_file = 'false'
    if summit_id:
        geom = None
        length_2d = 0
        uphill = 0
        downhill = 0
        moving_time = 0
        stopped_time = 0
        moving_distance = 0
        stopped_distance = 0
        max_speed = 0
        start_elevation = 0
        end_elevation = 0
        start_lat = 0
        start_lon = 0
        end_lat = 0
        end_lon = 0
        total_hours = 0
        total_minutes = 0
        distance_to_summit = 0
        distance_to_summit_temp = 0
        distance_to_summit_time = 0
        distance_to_summit_hours = 0
        distance_to_summit_minutes = 0
        reached_summit = False
        summit = SummitLog.objects.get(id=summit_id)
        peak_coords = (summit.item.lat, summit.item.long)
        points = []
        if request.POST.get('name'):
            filename = request.POST.get('name')
            key = request.POST.get('key')
            basefile = key.split("/")[-1]
            extension = os.path.splitext(filename)[1][1:].lower()
            item = summit.item
            upload_path = key
            #parse gpx
            try:
                f = default_storage.open(upload_path, 'r')
                gpx = gpxpy.parse(f)
                length_2d = gpx.length_2d()
                uphill, downhill = gpx.get_uphill_downhill()
                moving_time, stopped_time, moving_distance, stopped_distance, max_speed = gpx.get_moving_data()

                if length_2d is None:
                    length_2d = 0
                if uphill is None:
                    uphill = 0
                if downhill is None:
                    downhill = 0
                if moving_time is None:
                    moving_time = 0
                if stopped_time is None:
                    stopped_time = 0
                if moving_distance is None:
                    moving_distance = 0
                if stopped_distance is None:
                    stopped_distance = 0
                if max_speed is None:
                    max_speed = 0

                total_time = moving_time + stopped_time
                total_minutes = mod_math.floor(total_time / 60.)
                total_hours = mod_math.floor(total_minutes / 60.)
                start_elevation = 0
                start_lat = 0
                start_lon = 0
                end_lat = 0
                end_lon = 0
                previous_point = None
                if gpx.tracks:
                    for track in gpx.tracks:
                        for segment in track.segments:
                            for point in segment.points:
                                if (start_elevation == 0):
                                    start_elevation = point.elevation
                                if (start_lat == 0):
                                    start_lat = point.latitude
                                if (start_lon == 0):
                                    start_lon = point.longitude
                                end_elevation = point.elevation
                                end_lat = point.latitude
                                end_lon = point.longitude

                                points.append((point.latitude, point.longitude))

                                if previous_point is not None and distance_to_summit == 0:
                                    #add to running distance to summit
                                    distance_to_summit_temp = distance_to_summit_temp + point.distance_2d(previous_point)
                                    if point.time and previous_point.time:
                                        temp_time = point.time - previous_point.time
                                        distance_to_summit_time = distance_to_summit_time + temp_time.total_seconds()
                                    #calculate distance to summit
                                    #gpdistance.VincentyDistance.ELLIPSOID = 'WGS-84'
                                    current_point = (point.latitude, point.longitude)
                                    #miles = gpdistance.distance(peak_coords, current_point).mi
                                    miles = gpdistance.geodesic(peak_coords, current_point).mi
                                    if miles < 0.113636:
                                        distance_to_summit = distance_to_summit_temp/1609
                                        reached_summit = True
                                previous_point = point
                elif gpx.routes:
                    for route in gpx.routes:
                        for point in route.points:
                            if (start_elevation == 0):
                                start_elevation = point.elevation
                            if (start_lat == 0):
                                start_lat = point.latitude
                            if (start_lon == 0):
                                start_lon = point.longitude
                            end_elevation = point.elevation
                            end_lat = point.latitude
                            end_lon = point.longitude

                            points.append((point.latitude, point.longitude))

                            if previous_point is not None and distance_to_summit == 0:
                                #add to running distance to summit
                                distance_to_summit_temp = distance_to_summit_temp + point.distance_2d(previous_point)
                                if point.time and previous_point.time:
                                    temp_time = point.time - previous_point.time
                                    distance_to_summit_time = distance_to_summit_time + temp_time.total_seconds()
                                #calculate distance to summit
                                #gpdistance.VincentyDistance.ELLIPSOID = 'WGS-84'
                                current_point = (point.latitude, point.longitude)
                                #miles = gpdistance.distance(peak_coords, current_point).mi
                                miles = gpdistance.geodesic(peak_coords, current_point).mi
                                if miles < 0.113636:
                                    distance_to_summit = distance_to_summit_temp/1609
                                    reached_summit = True
                            previous_point = point

                if not reached_summit:
                    distance_to_summit = 0
                    distance_to_summit_time = 0
                    distance_to_summit_hours = 0
                    distance_to_summit_minutes = 0

                length_2d = length_2d/1609
                start_elevation = start_elevation/0.3048
                end_elevation = end_elevation/0.3048
                uphill = uphill/0.3048
                total_minutes = total_minutes % 60
                distance_to_summit_minutes = mod_math.floor(distance_to_summit_time / 60.)
                distance_to_summit_hours = mod_math.floor(distance_to_summit_minutes / 60.)
                distance_to_summit_minutes = distance_to_summit_minutes % 60
                success = 'true'
                valid_file = 'true'

                geom = 'SRID=4326;LINESTRING(' + ','.join(['{0} {1}'.format(x[0], x[1]) for x in points]) + ')'

            except:
                print('Invalid GPX file.')
        else:
            print('No file.')

        #summit.gpx_file = upload_path
        #summit.quick_save()
        sql = "update items_summitlog set gpx_file = %s, gpx_geom = %s where id = %s"
        with connection.cursor() as cursor:
            cursor.execute(sql, [upload_path, geom, summit.id])
    else:
        print('No summit id.')

    return render(request, 'items/ajax/show_uploaded_gpx.html', {'success':success, 'valid_file':valid_file, 'gpx_file':upload_path, 'start_index':0, 'end_index':500, 'length_2d':round(length_2d,2), 'start_lat':start_lat, 'start_lon':start_lon, 'start_elevation':int(start_elevation), 'end_lat':end_lat, 'end_lon':end_lon, 'end_elevation':int(end_elevation), 'uphill':int(uphill), 'total_hours':int(total_hours), 'total_minutes':int(total_minutes), 'distance_to_summit_hours':int(distance_to_summit_hours), 'distance_to_summit_minutes':int(distance_to_summit_minutes), 'distance_to_summit':round(distance_to_summit,2)})

@login_required
def s3_log_climb_gpx_upload_old(request):
    import time
    start_time = time.time()
    import gpxpy
    import gpxpy.gpx
    import math as mod_math
    from geopy import distance as gpdistance
    import gpxpy.geo as geo
    #gpdistance.VincentyDistance.ELLIPSOID = 'WGS-84'
    success = 'false'
    valid_file = 'false'
    length_2d = 0
    uphill = 0
    downhill = 0
    moving_time = 0
    stopped_time = 0
    moving_distance = 0
    stopped_distance = 0
    max_speed = 0
    start_elevation = 0
    max_elevation = 0
    end_elevation = 0
    start_lat = 0
    start_lon = 0
    end_lat = 0
    end_lon = 0
    total_hours = 0
    total_minutes = 0
    distance_to_summit = 0
    distance_to_summit_temp = 0
    distance_to_summit_time = 0
    distance_to_summit_hours = 0
    distance_to_summit_minutes = 0
    geom = None
    points = []

    if request.POST.get('name'):
        filename = request.POST.get('name')
        key = request.POST.get('key')
        basefile = key.split("/")[-1]
        extension = os.path.splitext(filename)[1][1:].lower()
        upload_path = key
        reached_any_summits = False
        #parse gpx

        f = default_storage.open(upload_path, 'r')
        gpx = gpxpy.parse(f)
        length_2d = gpx.length_2d()
        uphill, downhill = gpx.get_uphill_downhill()
        moving_time, stopped_time, moving_distance, stopped_distance, max_speed = gpx.get_moving_data()

        gpx_bounds = gpx.get_bounds()
        time_bounds = gpx.get_time_bounds()

        #find matching peaks by bounds
        lat_lo = gpx_bounds.min_latitude - 0.001
        lng_lo = gpx_bounds.min_longitude - 0.001
        lat_hi = gpx_bounds.max_latitude + 0.001
        lng_hi = gpx_bounds.max_longitude + 0.001

        sql_params = []

        sql = "select " + \
            "a.id, " + \
            "a.lat, " + \
            "a.long as lng " + \
            "from items_item a " + \
            "where (a.lat between %s and %s) and (a.long between %s and %s) limit 200 "

        sql_params.append(lat_lo)
        sql_params.append(lat_hi)
        sql_params.append(lng_lo)
        sql_params.append(lng_hi)

        peaks = Item.objects.raw(sql, sql_params)

        summit_logs = []

        for p in peaks:
            locations = []
            distance_to_summit_temp = 0
            distance_to_summit_time = 0
            reached_summit = False
            distance_to_summit = 0
            peak_coords = (p.lat, p.long)
            if length_2d is None:
                length_2d = 0
            if uphill is None:
                uphill = 0
            if downhill is None:
                downhill = 0
            if moving_time is None:
                moving_time = 0
            if stopped_time is None:
                stopped_time = 0
            if moving_distance is None:
                moving_distance = 0
            if stopped_distance is None:
                stopped_distance = 0
            if max_speed is None:
                max_speed = 0

            total_time = moving_time + stopped_time
            total_minutes = mod_math.floor(total_time / 60.)
            total_hours = mod_math.floor(total_minutes / 60.)
            start_elevation = 0
            max_elevation = 0
            start_lat = 0
            start_lon = 0
            end_lat = 0
            end_lon = 0
            previous_point = None
            points = []
            if gpx.tracks:
                for track in gpx.tracks:
                    for segment in track.segments:
                        for point in segment.points:
                            if (start_elevation == 0):
                                start_elevation = point.elevation
                            if (start_lat == 0):
                                start_lat = point.latitude
                            if (start_lon == 0):
                                start_lon = point.longitude
                            if point.elevation > max_elevation:
                                max_elevation = point.elevation
                            end_elevation = point.elevation
                            end_lat = point.latitude
                            end_lon = point.longitude
                            locations.append(point)
                            points.append((point.latitude, point.longitude))
                            current_point = (point.latitude, point.longitude)
                            #miles = gpdistance.distance(peak_coords, current_point).mi
                            miles = gpdistance.geodesic(peak_coords, current_point).mi
                            if previous_point is not None and distance_to_summit == 0:
                                #add to running distance to summit
                                distance_to_summit_temp = distance_to_summit_temp + point.distance_2d(previous_point)
                                if point.time and previous_point.time:
                                    temp_time = point.time - previous_point.time
                                    distance_to_summit_time = distance_to_summit_time + temp_time.total_seconds()
                                #calculate distance to summit
                                if miles < 0.0621371:
                                    distance_to_summit = distance_to_summit_temp/1609
                                    reached_summit = True
                                    reached_any_summits = True
                            previous_point = point
            elif gpx.routes:
                for route in gpx.routes:
                    for point in route.points:
                        if (start_elevation == 0):
                            start_elevation = point.elevation
                        if (start_lat == 0):
                            start_lat = point.latitude
                        if (start_lon == 0):
                            start_lon = point.longitude
                        end_elevation = point.elevation
                        if point.elevation > max_elevation:
                            max_elevation = point.elevation
                        end_lat = point.latitude
                        end_lon = point.longitude
                        points.append((point.latitude, point.longitude))
                        current_point = (point.latitude, point.longitude)
                        #miles = gpdistance.distance(peak_coords, current_point).mi
                        miles = gpdistance.geodesic(peak_coords, current_point).mi
                        if previous_point is not None and distance_to_summit == 0:
                            #add to running distance to summit
                            distance_to_summit_temp = distance_to_summit_temp + point.distance_2d(previous_point)
                            if point.time and previous_point.time:
                                temp_time = point.time - previous_point.time
                                distance_to_summit_time = distance_to_summit_time + temp_time.total_seconds()
                            #calculate distance to summit
                            if miles < 0.0621371:
                                distance_to_summit = distance_to_summit_temp/1609
                                reached_summit = True
                                reached_any_summits = True
                        previous_point = point

            if reached_summit:
                geom = 'SRID=4326;LINESTRING(' + ','.join(['{0} {1}'.format(x[0], x[1]) for x in points]) + ')'
                gpx_polyline = polyline.encode(points, 5)
                length_2d_saved = length_2d/1609
                start_elevation_saved = start_elevation/0.3048
                max_elevation_saved = max_elevation / 0.3048
                end_elevation_saved = end_elevation/0.3048
                uphill_saved = uphill/0.3048
                downhill_saved = downhill / 0.3048
                total_minutes = total_minutes % 60
                distance_to_summit_minutes = mod_math.floor(distance_to_summit_time / 60.)
                distance_to_summit_hours = mod_math.floor(distance_to_summit_minutes / 60.)
                distance_to_summit_minutes = distance_to_summit_minutes % 60
                summit = SummitLog(user=request.user, item=p, date=time_bounds[0], date_entered=True, status=STATUS_SUMMIT_PENDING, gpx_file=upload_path, gpx_geom=geom, encoded_polyline=gpx_polyline, distance_to_summit=round(distance_to_summit,2), total_distance=round(length_2d_saved,2), start_elevation=int(start_elevation_saved), elevation_gain=int(uphill_saved), time_to_summit=distance_to_summit_time, total_trip_time=total_time, max_elevation=int(max_elevation_saved), elevation_loss=int(downhill_saved))
                summit.quick_save()
                summit_logs.append(summit.id)

                # See if one-way route
                one_way_route = False
                if miles:
                    if miles < 0.0621371:
                        # last point at summit, one-way route
                        one_way_route = True

                # check for matching routes
                # if we find a matching route, assign that route to the summit log
                # if we don't find a matching route, create one with default name
                sql = "select a.id " + \
                    "from items_peakroute a, items_summitlog b " + \
                    "where b.id = %s and a.item_id = b.item_id " + \
                    "and ST_HausdorffDistance(ST_Simplify(a.gpx_geom,.01), ST_Simplify(b.gpx_geom,.01)) < .003 " + \
                    "order by ST_HausdorffDistance(ST_Simplify(a.gpx_geom,.01), ST_Simplify(b.gpx_geom,.01)) limit 1 "

                with connection.cursor() as cursor:
                    cursor.execute(sql, [summit.id])
                    routes = dictfetchall(cursor)

                if routes:
                    for r in routes:
                        summit.peak_route_id = r['id']
                        summit.quick_save()
                else:
                    is_usa = p.is_usa()
                    encoded_mapbox_polyline = mapbox_thumbnail_service.get_encoded_polyline_for_mapbox(upload_path)
                    total_distance = mapbox_thumbnail_service.total_distance_from_encoded_polyline(encoded_mapbox_polyline, is_usa)
                    if not total_distance:
                        total_distance = round(length_2d_saved,2)
                    if is_usa:
                        new_route_name = '%s %s mi route' % (p.name, round(total_distance, 1))
                    else:
                        new_route_name = '%s %s km route' % (p.name, round(total_distance, 1))
                    new_route = PeakRoute(user=request.user, item=p, name=new_route_name, slug=slugify(new_route_name), status=1, gpx_file=upload_path, gpx_start_index=0, gpx_end_index=500, gpx_start_lat=start_lat, gpx_start_lon=start_lon, gpx_end_lat=end_lat, gpx_end_lon=end_lon, distance_to_summit=round(distance_to_summit,2), start_elevation=int(start_elevation_saved), elevation_gain=int(uphill_saved), gpx_geom=geom, default_name=True, total_distance=total_distance, elevation_loss=int(downhill_saved), max_elevation=int(max_elevation_saved), one_way=one_way_route, encoded_polyline=encoded_mapbox_polyline)
                    new_route.save()
                    summit.peak_route = new_route
                    summit.quick_save()

        if uphill > 0 and moving_time > 0:
            success = 'true'
            valid_file = 'true'

        if not reached_any_summits:
            geom = 'SRID=4326;LINESTRING(' + ','.join(['{0} {1}'.format(x[0], x[1]) for x in points]) + ')'
            gpx_polyline = polyline.encode(points, 5)
            init_summit_ids = request.GET.get('summits', None)
            summit = SummitLog(user=request.user, item_id=0, date=time_bounds[0], date_entered=True, status = STATUS_SUMMIT_PENDING, gpx_file=upload_path, gpx_geom=geom, encoded_polyline=gpx_polyline)
            summit.quick_save()
            summit_logs.append(summit.id)

    else:
        print('No file.')

    end_time = time.time() - start_time
    print('perfmon - gpx_upload - %s' % end_time)

    return render(request, 'items/ajax/show_log_climb_uploaded_gpx.html', {'success':success, 'valid_file':valid_file, 'gpx_file':upload_path, 'summit_logs':summit_logs})

@login_required
def s3_log_climb_gpx_upload(request):
    import time
    start_time = time.time()
    import gpxpy.gpx
    import math as mod_math
    from geopy import distance as gpdistance
    success = 'false'
    valid_file = 'false'

    if request.POST.get('name'):
        try:
            key = request.POST.get('key')
            upload_path = key
            reached_any_summits = False

            f = default_storage.open(upload_path, 'r')
            gpx = gpxpy.parse(f)
            length_2d = gpx.length_2d()
            uphill, downhill = gpx.get_uphill_downhill()
            moving_time, stopped_time, moving_distance, stopped_distance, max_speed = gpx.get_moving_data()

            gpx_bounds = gpx.get_bounds()
            time_bounds = gpx.get_time_bounds()

            reduce_gpx_points(gpx)

            #find matching peaks by bounds
            lat_lo = gpx_bounds.min_latitude - 0.001
            lng_lo = gpx_bounds.min_longitude - 0.001
            lat_hi = gpx_bounds.max_latitude + 0.001
            lng_hi = gpx_bounds.max_longitude + 0.001

            sql_params = []

            sql = "select " + \
                "a.id, " + \
                "a.lat, " + \
                "a.long as lng " + \
                "from items_item a " + \
                "where (a.lat between %s and %s) and (a.long between %s and %s) limit 200 "

            sql_params.append(lat_lo)
            sql_params.append(lat_hi)
            sql_params.append(lng_lo)
            sql_params.append(lng_hi)

            peaks = Item.objects.raw(sql, sql_params)

            total_peaks = []
            for p in peaks:
                peak = {}
                peak['peak_id'] = p.id
                peak['peak_lat'] = p.lat
                peak['peak_long'] = p.long
                peak['is_usa'] = p.is_usa()
                peak['peak_name'] = p.name
                total_peaks.append(peak)

            summit_logs = []
            peaks_summited = []

            locations = []
            distance_to_summit_temp = 0
            distance_to_summit_time = 0
            if length_2d is None:
                length_2d = 0
            if uphill is None:
                uphill = 0
            if downhill is None:
                downhill = 0
            if moving_time is None:
                moving_time = 0
            if stopped_time is None:
                stopped_time = 0

            total_time = moving_time + stopped_time
            total_minutes = mod_math.floor(total_time / 60.)
            start_elevation = 0
            max_elevation = 0
            start_lat = 0
            start_lon = 0
            end_lat = 0
            end_lon = 0
            previous_point = None
            points = []
            if gpx.tracks:
                for track in gpx.tracks:
                    for segment in track.segments:
                        for point in segment.points:
                            if (start_elevation == 0):
                                start_elevation = point.elevation
                            if (start_lat == 0):
                                start_lat = point.latitude
                            if (start_lon == 0):
                                start_lon = point.longitude
                            if point.elevation > max_elevation:
                                max_elevation = point.elevation
                            end_elevation = point.elevation
                            end_lat = point.latitude
                            end_lon = point.longitude
                            locations.append(point)
                            points.append((point.latitude, point.longitude))
                            current_point = (point.latitude, point.longitude)

                            if previous_point is not None:
                                #add to running distance to summit
                                distance_to_summit_temp = distance_to_summit_temp + point.distance_2d(previous_point)
                                if point.time and previous_point.time:
                                    temp_time = point.time - previous_point.time
                                    distance_to_summit_time = distance_to_summit_time + temp_time.total_seconds()
                                #calculate distance to summit
                                #check to see if we're on a summit
                                for i, p in enumerate(total_peaks):
                                    peak_coords = (p['peak_lat'], p['peak_long'])
                                    miles = gpdistance.geodesic(peak_coords, current_point).mi
                                    if miles and miles < 0.0621371:
                                        peak_summited = {}
                                        peak_summited['peak_id'] = p['peak_id']
                                        peak_summited['peak_lat'] = p['peak_lat']
                                        peak_summited['peak_long'] = p['peak_long']
                                        peak_summited['is_usa'] = p['is_usa']
                                        peak_summited['peak_name'] = p['peak_name']
                                        peak_summited['distance_to_summit'] = distance_to_summit_temp / 1609
                                        peak_summited['distance_to_summit_time'] = distance_to_summit_time
                                        peaks_summited.append(peak_summited)
                                        reached_any_summits = True
                                        del total_peaks[i]
                            previous_point = point
            elif gpx.routes:
                for route in gpx.routes:
                    for point in route.points:
                        if (start_elevation == 0):
                            start_elevation = point.elevation
                        if (start_lat == 0):
                            start_lat = point.latitude
                        if (start_lon == 0):
                            start_lon = point.longitude
                        end_elevation = point.elevation
                        if point.elevation > max_elevation:
                            max_elevation = point.elevation
                        end_lat = point.latitude
                        end_lon = point.longitude
                        points.append((point.latitude, point.longitude))
                        current_point = (point.latitude, point.longitude)

                        if previous_point is not None:
                            # add to running distance to summit
                            distance_to_summit_temp = distance_to_summit_temp + point.distance_2d(previous_point)
                            if point.time and previous_point.time:
                                temp_time = point.time - previous_point.time
                                distance_to_summit_time = distance_to_summit_time + temp_time.total_seconds()
                            # calculate distance to summit
                            # check to see if we're on a summit
                            for i, p in enumerate(total_peaks):
                                peak_coords = (p['peak_lat'], p['peak_long'])
                                #miles = gpdistance.distance(peak_coords, current_point).mi
                                miles = gpdistance.geodesic(peak_coords, current_point).mi
                                if miles and miles < 0.0621371:
                                    peak_summited = {}
                                    peak_summited['peak_id'] = p['peak_id']
                                    peak_summited['peak_lat'] = p['peak_lat']
                                    peak_summited['peak_long'] = p['peak_long']
                                    peak_summited['is_usa'] = p['is_usa']
                                    peak_summited['peak_name'] = p['peak_name']
                                    peak_summited['distance_to_summit'] = distance_to_summit_temp / 1609
                                    peak_summited['distance_to_summit_time'] = distance_to_summit_time
                                    peaks_summited.append(peak_summited)
                                    reached_any_summits = True
                                    del total_peaks[i]
                        previous_point = point

            if reached_any_summits:
                geom = 'SRID=4326;LINESTRING(' + ','.join(['{0} {1}'.format(x[0], x[1]) for x in points]) + ')'
                gpx_polyline = polyline.encode(points, 5)
                length_2d_saved = length_2d/1609
                start_elevation_saved = start_elevation/0.3048
                max_elevation_saved = max_elevation / 0.3048
                uphill_saved = uphill/0.3048
                downhill_saved = downhill / 0.3048

                #for each peak summited
                for p in peaks_summited:
                    summit = SummitLog(user=request.user, item_id=p['peak_id'], date=time_bounds[0], date_entered=True, status=STATUS_SUMMIT_PENDING, gpx_file=upload_path, gpx_geom=geom, encoded_polyline=gpx_polyline, distance_to_summit=round(p['distance_to_summit'],2), total_distance=round(length_2d_saved,2), start_elevation=int(start_elevation_saved), elevation_gain=int(uphill_saved), time_to_summit=p['distance_to_summit_time'], total_trip_time=total_time, max_elevation=int(max_elevation_saved), elevation_loss=int(downhill_saved))
                    summit.quick_save()
                    summit_logs.append(summit.id)

                    # See if one-way route
                    one_way_route = False
                    peak_coords = (p['peak_lat'], p['peak_long'])
                    miles = gpdistance.geodesic(peak_coords, current_point).mi
                    if miles:
                        if miles < 0.0621371:
                            # last point at summit, one-way route
                            one_way_route = True

                    # check for matching routes
                    # if we find a matching route, assign that route to the summit log
                    # if we don't find a matching route, create one with default name
                    sql = "select a.id " + \
                        "from items_peakroute a, items_summitlog b " + \
                        "where b.id = %s and a.item_id = b.item_id " + \
                        "and ST_HausdorffDistance(ST_Simplify(a.gpx_geom,.01), ST_Simplify(b.gpx_geom,.01)) < .003 " + \
                        "order by ST_HausdorffDistance(ST_Simplify(a.gpx_geom,.01), ST_Simplify(b.gpx_geom,.01)) limit 1 "

                    with connection.cursor() as cursor:
                        cursor.execute(sql, [summit.id])
                        routes = dictfetchall(cursor)

                    if routes:
                        for r in routes:
                            summit.peak_route_id = r['id']
                            summit.quick_save()
                    else:
                        is_usa = p['is_usa']
                        encoded_mapbox_polyline = mapbox_thumbnail_service.get_encoded_polyline_for_mapbox(upload_path)
                        total_distance = mapbox_thumbnail_service.total_distance_from_encoded_polyline(
                            encoded_mapbox_polyline, is_usa)
                        if not total_distance:
                            total_distance = round(length_2d_saved, 2)
                        if is_usa:
                            new_route_name = '%s %s mi route' % (p['peak_name'], round(total_distance, 1))
                        else:
                            new_route_name = '%s %s km route' % (p['peak_name'], round(total_distance, 1))
                        new_route = PeakRoute(user=request.user, item_id=p['peak_id'], name=new_route_name, slug=slugify(new_route_name), status=1, gpx_file=upload_path, gpx_start_index=0, gpx_end_index=500, gpx_start_lat=start_lat, gpx_start_lon=start_lon, gpx_end_lat=end_lat, gpx_end_lon=end_lon, distance_to_summit=round(p['distance_to_summit'],2), start_elevation=int(start_elevation_saved), elevation_gain=int(uphill_saved), gpx_geom=geom, default_name=True, total_distance=total_distance, elevation_loss=int(downhill_saved), max_elevation=int(max_elevation_saved), one_way=one_way_route, encoded_polyline=encoded_mapbox_polyline)
                        new_route.save()
                        summit.peak_route = new_route
                        summit.quick_save()

            if uphill > 0 and moving_time > 0:
                success = 'true'
                valid_file = 'true'

            if not reached_any_summits:
                geom = 'SRID=4326;LINESTRING(' + ','.join(['{0} {1}'.format(x[0], x[1]) for x in points]) + ')'
                gpx_polyline = polyline.encode(points, 5)
                summit = SummitLog(user=request.user, item_id=0, date=time_bounds[0], date_entered=True, status = STATUS_SUMMIT_PENDING, gpx_file=upload_path, gpx_geom=geom, encoded_polyline=gpx_polyline)
                summit.quick_save()
                summit_logs.append(summit.id)
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error("A problem occured in s3_log_climb_gpx_upload endpoint")
            logger.error(e)
            logger.error(f"Exception: {e}", exc_info=True)
    else:
        print('No file.')

    end_time = time.time() - start_time
    print('perfmon - gpx_parse - %s' % end_time)

    return render(request, 'items/ajax/show_log_climb_uploaded_gpx.html', {'success':success, 'valid_file':valid_file, 'gpx_file':upload_path, 'summit_logs':summit_logs})


@login_required
def download_gpx(request, peak_id, route_id):
    import gpxpy
    import gpxpy.gpx
    import math as mod_math

    route = PeakRoute.objects.get(id=route_id)

    #parse gpx
    if route.gpx_file:
        f = default_storage.open(route.gpx_file, 'r')
        raw_gpx = gpxpy.parse(f)
        length_2d = raw_gpx.length_2d()
        uphill, downhill = raw_gpx.get_uphill_downhill()
        moving_time, stopped_time, moving_distance, stopped_distance, max_speed = raw_gpx.get_moving_data()
        total_time = moving_time + stopped_time
        total_minutes = mod_math.floor(total_time / 60.)
        total_hours = mod_math.floor(total_minutes / 60.)
        start_elevation = 0
        gpx = gpxpy.gpx.GPX()
        # Create first track in our GPX:
        gpx_track = gpxpy.gpx.GPXTrack()
        gpx.tracks.append(gpx_track)
        # Create first segment in our GPX track:
        gpx_segment = gpxpy.gpx.GPXTrackSegment()
        gpx_track.segments.append(gpx_segment)
        for track in raw_gpx.tracks:
            for segment in track.segments:
                total_points = segment.get_points_no()
                start_step = route.gpx_start_index
                end_step = route.gpx_end_index
                point_step = total_points / 100
                i = 0
                for point in segment.points:
                    if (start_elevation == 0):
                        start_elevation = point.elevation
                    end_elevation = point.elevation
                    # Create point:
                    if i >= (point_step * start_step) and i <= (point_step * end_step):
                        gpx_segment.points.append(gpxpy.gpx.GPXTrackPoint(point.latitude, point.longitude, elevation=point.elevation, time=point.time))
                    i += 1

    r = render(request, 'items/ajax/show_download_gpx.html', {'download_gpx':gpx.to_xml()})
    r['Content-Type'] = 'application/gpx+xml; charset=utf-8'
    r['Content-Disposition'] = 'attachment; filename="%s"' % route.gpx_file
    return r

@login_required
def summit_download_gpx(request, item_slug, summitlog_id):
    import gpxpy
    import gpxpy.gpx
    import math as mod_math

    summit = SummitLog.objects.get(id=summitlog_id)

    #parse gpx
    if summit.gpx_file:
        f = default_storage.open(summit.gpx_file, 'r')
        raw_gpx = gpxpy.parse(f)
        gpx = gpxpy.gpx.GPX()
        gpx.creator = 'Peakery.com'
        # Create first track in our GPX:
        gpx_track = gpxpy.gpx.GPXTrack()
        gpx.tracks.append(gpx_track)
        # Create first segment in our GPX track:
        gpx_segment = gpxpy.gpx.GPXTrackSegment()
        gpx_track.segments.append(gpx_segment)
        for track in raw_gpx.tracks:
            for segment in track.segments:
                for point in segment.points:
                    # Create point:
                    gpx_segment.points.append(gpxpy.gpx.GPXTrackPoint(point.latitude, point.longitude, elevation=point.elevation, time=point.time))

    r = render(request, 'items/ajax/show_download_gpx.html', {'download_gpx':gpx.to_xml()})
    r['Content-Type'] = 'application/gpx+xml; charset=utf-8'
    r['Content-Disposition'] = 'attachment; filename="%s"' % summit.gpx_file
    return r

@login_required
def upload_route_photo(request):
    from PIL import Image, ImageFilter, ExifTags
    from io import BytesIO
    route_id = request.GET.get('route_id', False)
    step_number = request.GET.get('step_number', False)
    thumb_file_path = ''
    if route_id:
        route = PeakRoute.objects.get(id=route_id)
        route_step = PeakRouteStep(route=route,step_number=step_number)
        if request.POST.get('name'):
            filename = request.POST.get('name')
            key = request.POST.get('key')
            uuid = request.POST.get('uuid')
            basefile = key.split("/")[-1]
            extension = os.path.splitext(filename)[1][1:].lower()
            if extension == 'png':
                extension = 'PNG'
            else:
                extension = 'JPEG'
            item = route.item
            upload_path = key
            thumb_file_path = 'images/items/routes/cache/%s' % (basefile)

            upload_images_to_s3_with_thumbnails(upload_path, thumb_file_path, extension)

    try:
        f = default_storage.open(upload_path, 'rb')
        image = Image.open(f)
        width, height = image.size

    except Exception as e:
        width,height = [-1,-1]

    if width >= settings.MIN_PEAK_IMG_WIDTH and height >= settings.MIN_PEAK_IMG_HEIGHT:
        success = 'true'
        return render(request, 'items/ajax/show_uploaded_image.html', {'success':success, 'photo_object_id':route_step.id, 'photo_object_file':upload_path, 'photo_object_url':thumb_file_path+'.745x500_q95_crop-top.jpg'})
    else:
        default_storage.delete(upload_path)
        success = 'false'
        return render(request, 'items/ajax/show_uploaded_image.html', {'success':success, 'photo_object_id':route_step.id, 'photo_object_url':''})

@login_required
def upload_new_peak_photo(request, peak_id):
    from PIL import Image, ImageFilter, ExifTags
    #import cStringIO
    from io import BytesIO
    import time
    import re
    timestr = time.strftime("%Y%m%d-%H%M%S")
    data = {}
    error = True
    error_message = ''
    success = False
    success_message = ''
    if request.method == 'POST':
        item_id = peak_id
        item = Item.objects.get(id=item_id)
        category = ItemPhotoCategory.objects.get(name='peak')

        filename = request.FILES['file-0'].name
        filename = re.sub('[^a-zA-Z0-9-_*.]', '', filename)
        upload_path = 'items/users/%s-%s-%s' % (item.slug, timestr, filename)
        thumb_file_path = 'images/items/users/cache/%s-%s-%s' % (item.slug, timestr, filename)
        file = default_storage.open(upload_path, 'w')

        from django.core.files.base import ContentFile
        file_content = ContentFile( request.FILES['file-0'].read() )

        # Iterate through the chunks.
        for chunk in file_content.chunks():
            file.write(chunk)
        file.close()

        try:
            #width, height = get_image_dimensions(settings.MEDIA_ROOT + '/' +  upload_path)
            f = default_storage.open(upload_path, 'rb')
            image = Image.open(f)

            try:
                for orientation in ExifTags.TAGS.keys():
                    if ExifTags.TAGS[orientation]=='Orientation':
                        break
                exif=dict(image._getexif().items())

                if exif[orientation] == 3:
                    image=image.rotate(180, expand=True)
                elif exif[orientation] == 6:
                    image=image.rotate(270, expand=True)
                elif exif[orientation] == 8:
                    image=image.rotate(90, expand=True)

            except (AttributeError, KeyError, IndexError):
                # cases: image don't have getexif
                pass

            width, height = image.size

            #rectangular thumbnail
            if width/height > 1.338235:
                delta = width - (1.338235 * height)
                left = int(delta/2)
                upper = 0
                right = width - left
                lower = height
            else:
                delta = height - (width / 1.338235)
                left = 0
                upper = int(delta/2)
                right = width
                lower = height - upper

            rect_image = image.crop((left, upper, right, lower))
            if width > height:
                new_width = 910
                new_height = 680
            else:
                new_width = 680
                new_height = 910
            rect_image = image.resize((new_width, new_height), Image.LANCZOS)
            rect_image = tmp_image.filter(ImageFilter.SHARPEN)
            f_thumb = default_storage.open('%s.%s' % (thumb_file_path,'910x680_q95.jpg'), "w")
            #NOTE, we're saving the image into a cStringIO object to avoid writing to disk
            with BytesIO() as out_img:
                #You MUST specify the file type because there is no file name to discern it from
                rect_image.save(out_img, format='JPEG', subsampling=0, quality=95)
                f_thumb.write(out_img.getvalue())
                f_thumb.close()

            #square thumbnail
            if width/height > 1.333333:
                delta = width - (1.333333 * height)
                left = int(delta/2)
                upper = 0
                right = width - left
                lower = height
            else:
                delta = height - (width / 1.333333)
                left = 0
                upper = int(delta/2)
                right = width
                lower = height - upper

            sq_image = image.crop((left, upper, right, lower))
            if width > height:
                new_width = 240
                new_height = 180
            else:
                new_width = 180
                new_height = 240
            sq_image = image.resize((new_width, new_height), Image.LANCZOS)
            f_thumb = default_storage.open('%s.%s' % (thumb_file_path,'240x180_q95_crop.jpg'), "w")
            #NOTE, we're saving the image into a cStringIO object to avoid writing to disk
            with BytesIO() as out_img:
                #You MUST specify the file type because there is no file name to discern it from
                sq_image.save(out_img, 'JPEG')
                f_thumb.write(out_img.getvalue())
                f_thumb.close()

            if width > height:
                new_width = 120
                new_height = 90
            else:
                new_width = 90
                new_height = 120
            sq_image_2 = image.resize((new_width, new_height), Image.LANCZOS)
            f_thumb = default_storage.open('%s.%s' % (thumb_file_path,'120x90_q95_crop.jpg'), "w")
            #NOTE, we're saving the image into a cStringIO object to avoid writing to disk
            with BytesIO() as out_img:
                #You MUST specify the file type because there is no file name to discern it from
                sq_image_2.save(out_img, 'JPEG')
                f_thumb.write(out_img.getvalue())
                f_thumb.close()

            if width > height:
                new_width = 70
                new_height = 50
            else:
                new_width = 50
                new_height = 70
            sq_image_3 = image.resize((new_width, new_height), Image.LANCZOS)
            f_thumb = default_storage.open('%s.%s' % (thumb_file_path,'70x50_q95_crop.jpg'), "w")
            #NOTE, we're saving the image into a cStringIO object to avoid writing to disk
            with BytesIO() as out_img:
                #You MUST specify the file type because there is no file name to discern it from
                sq_image_3.save(out_img, 'JPEG')
                f_thumb.write(out_img.getvalue())
                f_thumb.close()

            #standard thumbnail
            if width/height > 1.49:
                delta = width - (1.49 * height)
                left = int(delta/2)
                upper = 0
                right = width - left
                lower = height
            else:
                delta = height - (width / 1.49)
                left = 0
                upper = int(delta/2)
                right = width
                lower = height - upper

            image = image.crop((left, upper, right, lower))
            if width > height:
                new_width = 745
                new_height = 500
            else:
                new_width = 500
                new_height = 745
            image = image.resize((new_width, new_height), Image.LANCZOS)

            f_thumb = default_storage.open('%s.%s' % (thumb_file_path,'745x500_q95_crop-top.jpg'), "w")
            #NOTE, we're saving the image into a cStringIO object to avoid writing to disk
            with BytesIO() as out_img:
                #You MUST specify the file type because there is no file name to discern it from
                image.save(out_img, 'JPEG')
                f_thumb.write(out_img.getvalue())
                f_thumb.close()
        except Exception as e:
            width,height = [-1,-1]

        if width >= settings.MIN_PEAK_IMG_WIDTH and height >= settings.MIN_PEAK_IMG_HEIGHT:
            photo_object = ItemPhoto(user=request.user, item=item, category=category, image=upload_path)
            photo_object.save()

            original_photo = item.get_main_photo()
            #When there is only thumbnail
            if original_photo:
                original_value = original_photo.image.name
            else:
                original_photo = None
                original_value = None
            new_value = photo_object.image
            itemcorrection = ItemCorrection(user=request.user, item=item, original_value=original_value, new_value=new_value,
                                            original_photo=original_photo, new_photo = photo_object, field = 6)
            itemcorrection.save()

            success = True
            success_message = 'Successfully saved peak photo'
            error = False
        elif width > 0 and height > 0:
            error_message = 'This image is too small.'
        else:
            error_message = 'Error uploading photo. Please try again.'


    data['success'] = success
    data['success_message'] = success_message
    data['error'] = error
    data['error_message'] = error_message
    data['photo_url'] = upload_path
    #    return data
    jsondata = json.dumps(data)
    return HttpResponse(jsondata, content_type='text/html')

@login_required
def s3_peak_photo_upload(request):
    from PIL import Image, ImageFilter, ExifTags
    #import cStringIO
    from io import BytesIO
    data = {}
    error = True
    error_message = ''
    success = False
    success_message = ''

    peak_id = request.POST.get('peak_id', False)
    if peak_id:
        item = Item.objects.get(id=peak_id)
        category = ItemPhotoCategory.objects.get(name='peak')
        if request.POST.get('name'):
            filename = request.POST.get('name')
            key = request.POST.get('key')
            uuid = request.POST.get('uuid')
            basefile = key.split("/")[-1]
            extension = os.path.splitext(filename)[1][1:].lower()
            if extension == 'png':
                extension = 'PNG'
            else:
                extension = 'JPEG'
            upload_path = key
            thumb_file_path = 'images/items/main/cache/%s' % (basefile)

    try:
        f = default_storage.open(upload_path, 'rb')
        image = Image.open(f)

        try:
            for orientation in ExifTags.TAGS.keys():
                if ExifTags.TAGS[orientation]=='Orientation':
                    break
            exif=dict(image._getexif().items())

            if exif[orientation] == 3:
                image=image.rotate(180, expand=True)
            elif exif[orientation] == 6:
                image=image.rotate(270, expand=True)
            elif exif[orientation] == 8:
                image=image.rotate(90, expand=True)

        except (AttributeError, KeyError, IndexError):
            # cases: image don't have getexif
            pass

        width, height = image.size

        # 1920x1440 thumbnail
        if width >= height:
            # landscape orientation photo
            if float(width) / float(height) > 1.333333:
                # possible panoramic photo, let's keep aspect ratio intact
                left = 0
                upper = 0
                right = width
                lower = height
            else:
                delta = height - (width / 1.333333)
                left = 0
                upper = int(delta / 2)
                right = width
                lower = height - upper
        else:
            # portrait orientation photo
            if float(height) / float(width) > 1.333333:
                delta = height - (1.333333 * width)
                left = 0
                upper = int(delta / 2)
                right = width
                lower = height - upper
            else:
                delta = width - (height / 1.333333)
                left = int(delta / 2)
                upper = 0
                right = width - left
                lower = height

        tmp_image = image.crop((left, upper, right, lower))
        if width >= height:
            new_width = 1920
            if float(width) / float(height) > 1.333333:
                # possible panoramic photo, let's keep aspect ratio intact
                aspect_ratio = float(width) / float(height)
                new_height = int(1920 / aspect_ratio)
            else:
                new_height = 1440
        else:
            new_width = 1440
            new_height = 1920
        tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
        tmp_image = tmp_image.filter(ImageFilter.SHARPEN)
        f_thumb = default_storage.open('%s.%s' % (thumb_file_path, '1920x1440_q95_crop.jpg'), "w")
        # NOTE, we're saving the image into a cStringIO object to avoid writing to disk
        with BytesIO() as out_img:
            # You MUST specify the file type because there is no file name to discern it from
            tmp_image.save(out_img, format='JPEG', subsampling=0, quality=95)
            f_thumb.write(out_img.getvalue())
            f_thumb.close()

        #1250x600 thumbnail
        if width >= height:
            # landscape orientation photo
            if float(width) / float(height) > 1.333333:
                # possible panoramic photo, let's keep aspect ratio intact
                left = 0
                upper = 0
                right = width
                lower = height
            else:
                delta = height - (width / 1.333333)
                left = 0
                upper = int(delta / 2)
                right = width
                lower = height - upper
        else:
            #portrait orientation photo
            if float(height)/float(width) > 1.333333:
                delta = height - (1.333333 * width)
                left = 0
                upper = int(delta/2)
                right = width
                lower = height - upper
            else:
                delta = width - (height / 1.333333)
                left = int(delta/2)
                upper = 0
                right = width - left
                lower = height

        tmp_image = image.crop((left, upper, right, lower))
        if width >= height:
            new_width = 1250
            if float(width) / float(height) > 1.333333:
                # possible panoramic photo, let's keep aspect ratio intact
                aspect_ratio = float(width) / float(height)
                new_height = int(1250 / aspect_ratio)
            else:
                new_height = 600
        else:
            new_width = 600
            new_height = 1250
        tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
        tmp_image = tmp_image.filter(ImageFilter.SHARPEN)
        f_thumb = default_storage.open('%s.%s' % (thumb_file_path,'1250x600_q95_crop--0,0_upscale.jpg'), "w")
        #NOTE, we're saving the image into a cStringIO object to avoid writing to disk
        with BytesIO() as out_img:
            #You MUST specify the file type because there is no file name to discern it from
            tmp_image.save(out_img, 'JPEG')
            f_thumb.write(out_img.getvalue())
            f_thumb.close()

        # 480x360 thumbnail
        if width >= height:
            # landscape orientation photo
            if float(width) / float(height) > 1.333333:
                delta = width - (1.333333 * height)
                left = int(delta / 2)
                upper = 0
                right = width - left
                lower = height
            else:
                delta = height - (width / 1.333333)
                left = 0
                upper = int(delta / 2)
                right = width
                lower = height - upper
        else:
            # portrait orientation photo
            if float(height) / float(width) > 1.333333:
                delta = height - (1.333333 * width)
                left = 0
                upper = int(delta / 2)
                right = width
                lower = height - upper
            else:
                delta = width - (height / 1.333333)
                left = int(delta / 2)
                upper = 0
                right = width - left
                lower = height

        tmp_image = image.crop((left, upper, right, lower))
        if width >= height:
            new_width = 480
            new_height = 360
        else:
            new_width = 360
            new_height = 480
        tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
        tmp_image = tmp_image.filter(ImageFilter.SHARPEN)
        f_thumb = default_storage.open('%s.%s' % (thumb_file_path, '480x360_q95.jpg'), "w")
        # NOTE, we're saving the image into a cStringIO object to avoid writing to disk
        with BytesIO() as out_img:
            # You MUST specify the file type because there is no file name to discern it from
            tmp_image.save(out_img, format=extension, subsampling=0, quality=95)
            f_thumb.write(out_img.getvalue())
            f_thumb.close()

        #320x240 thumbnail
        if width >= height:
            #landscape orientation photo
            if float(width)/float(height) > 1.333333:
                delta = width - (1.333333 * height)
                left = int(delta/2)
                upper = 0
                right = width - left
                lower = height
            else:
                delta = height - (width / 1.333333)
                left = 0
                upper = int(delta/2)
                right = width
                lower = height - upper
        else:
            #portrait orientation photo
            if float(height)/float(width) > 1.333333:
                delta = height - (1.333333 * width)
                left = 0
                upper = int(delta/2)
                right = width
                lower = height - upper
            else:
                delta = width - (height / 1.333333)
                left = int(delta/2)
                upper = 0
                right = width - left
                lower = height

        tmp_image = image.crop((left, upper, right, lower))
        if width >= height:
            new_width = 320
            new_height = 240
        else:
            new_width = 240
            new_height = 320
        tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
        tmp_image = tmp_image.filter(ImageFilter.SHARPEN)
        f_thumb = default_storage.open('%s.%s' % (thumb_file_path,'320x240_q95.jpg'), "w")
        #NOTE, we're saving the image into a cStringIO object to avoid writing to disk
        with BytesIO() as out_img:
            #You MUST specify the file type because there is no file name to discern it from
            tmp_image.save(out_img, format=extension, subsampling=0, quality=95)
            f_thumb.write(out_img.getvalue())
            f_thumb.close()

        #120x90 thumbnail
        if width >= height:
            #landscape orientation photo
            if float(width)/float(height) > 1.333333:
                delta = width - (1.333333 * height)
                left = int(delta/2)
                upper = 0
                right = width - left
                lower = height
            else:
                delta = height - (width / 1.333333)
                left = 0
                upper = int(delta/2)
                right = width
                lower = height - upper
        else:
            #portrait orientation photo
            if float(height)/float(width) > 1.333333:
                delta = height - (1.333333 * width)
                left = 0
                upper = int(delta/2)
                right = width
                lower = height - upper
            else:
                delta = width - (height / 1.333333)
                left = int(delta/2)
                upper = 0
                right = width - left
                lower = height

        tmp_image = image.crop((left, upper, right, lower))
        if width >= height:
            new_width = 120
            new_height = 90
        else:
            new_width = 90
            new_height = 120
        tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
        f_thumb = default_storage.open('%s.%s' % (thumb_file_path,'120x90_q95_crop.jpg'), "w")
        #NOTE, we're saving the image into a cStringIO object to avoid writing to disk
        with BytesIO() as out_img:
            #You MUST specify the file type because there is no file name to discern it from
            tmp_image.save(out_img, extension)
            f_thumb.write(out_img.getvalue())
            f_thumb.close()

        #70x50 thumbnail
        if width >= height:
            #landscape orientation photo
            if float(width)/float(height) > 1.4:
                delta = width - (1.4 * height)
                left = int(delta/2)
                upper = 0
                right = width - left
                lower = height
            else:
                delta = height - (width / 1.4)
                left = 0
                upper = int(delta/2)
                right = width
                lower = height - upper
        else:
            #portrait orientation photo
            if float(height)/float(width) > 1.4:
                delta = height - (1.4 * width)
                left = 0
                upper = int(delta/2)
                right = width
                lower = height - upper
            else:
                delta = width - (height / 1.4)
                left = int(delta/2)
                upper = 0
                right = width - left
                lower = height

        tmp_image = image.crop((left, upper, right, lower))
        if width >= height:
            new_width = 70
            new_height = 50
        else:
            new_width = 50
            new_height = 70
        tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
        f_thumb = default_storage.open('%s.%s' % (thumb_file_path,'70x50_q95_crop.jpg'), "w")
        #NOTE, we're saving the image into a cStringIO object to avoid writing to disk
        with BytesIO() as out_img:
            #You MUST specify the file type because there is no file name to discern it from
            tmp_image.save(out_img, extension)
            f_thumb.write(out_img.getvalue())
            f_thumb.close()

        #910x680 thumbnail
        if width >= height:
            # landscape orientation photo
            if float(width) / float(height) > 1.338235:
                # possible panoramic photo, let's keep aspect ratio intact
                left = 0
                upper = 0
                right = width
                lower = height
            else:
                delta = height - (width / 1.338235)
                left = 0
                upper = int(delta / 2)
                right = width
                lower = height - upper
        else:
            #portrait orientation photo
            if float(height)/float(width) > 1.338235:
                delta = height - (1.338235 * width)
                left = 0
                upper = int(delta/2)
                right = width
                lower = height - upper
            else:
                delta = width - (height / 1.338235)
                left = int(delta/2)
                upper = 0
                right = width - left
                lower = height

        tmp_image = image.crop((left, upper, right, lower))
        if width >= height:
            new_width = 910
            if float(width) / float(height) > 1.338235:
                # possible panoramic photo, let's keep aspect ratio intact
                aspect_ratio = float(width) / float(height)
                new_height = int(910 / aspect_ratio)
            else:
                new_height = 680
        else:
            new_width = 680
            new_height = 910
        tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
        tmp_image = tmp_image.filter(ImageFilter.SHARPEN)
        f_thumb = default_storage.open('%s.%s' % (thumb_file_path,'910x680_q95.jpg'), "w")
        #NOTE, we're saving the image into a cStringIO object to avoid writing to disk
        with BytesIO() as out_img:
            #You MUST specify the file type because there is no file name to discern it from
            tmp_image.save(out_img, format=extension, subsampling=0, quality=95)
            f_thumb.write(out_img.getvalue())
            f_thumb.close()

        #745x500 thumbnail
        if width >= height:
            #landscape orientation photo
            if float(width)/float(height) > 1.49:
                delta = width - (1.49 * height)
                left = int(delta/2)
                upper = 0
                right = width - left
                lower = height
            else:
                delta = height - (width / 1.49)
                left = 0
                upper = int(delta/2)
                right = width
                lower = height - upper
        else:
            #portrait orientation photo
            if float(height)/float(width) > 1.49:
                delta = height - (1.49 * width)
                left = 0
                upper = int(delta/2)
                right = width
                lower = height - upper
            else:
                delta = width - (height / 1.49)
                left = int(delta/2)
                upper = 0
                right = width - left
                lower = height

        tmp_image = image.crop((left, upper, right, lower))
        if width >= height:
            new_width = 745
            new_height = 500
        else:
            new_width = 500
            new_height = 745
        tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)

        f_thumb = default_storage.open('%s.%s' % (thumb_file_path,'745x500_q95_crop-top.jpg'), "w")
        #NOTE, we're saving the image into a cStringIO object to avoid writing to disk
        with BytesIO() as out_img:
            #You MUST specify the file type because there is no file name to discern it from
            tmp_image.save(out_img, extension)
            f_thumb.write(out_img.getvalue())
            f_thumb.close()

    except Exception as e:
        width,height = [-1,-1]

    if width >= settings.MIN_PEAK_IMG_WIDTH and height >= settings.MIN_PEAK_IMG_HEIGHT:
        photo_object = ItemPhoto(user=request.user, item=item, category=category, image=upload_path)
        photo_object.save()

        original_photo = item.get_main_photo()
        #When there is only thumbnail
        if original_photo:
            original_value = original_photo.image.name
        else:
            original_photo = None
            original_value = item.thumbnail
        new_value = photo_object.image
        itemcorrection = ItemCorrection(user=request.user, item=item, original_value=original_value, new_value=new_value,
                                        original_photo=original_photo, new_photo = photo_object, field = 6)
        itemcorrection.save()

        success = True
        success_message = 'Successfully saved peak photo'
        error = False
    elif width > 0 and height > 0:
        error_message = 'This image is too small.'
    else:
        error_message = 'Error uploading photo. Please try again.'


    data['success'] = success
    data['success_message'] = success_message
    data['error'] = error
    data['error_message'] = error_message
    data['photo_url'] = upload_path
    #    return data
    jsondata = json.dumps(data)
    return HttpResponse(jsondata, content_type='text/html')

@login_required
def s3_route_gpx_upload(request):
    import gpxpy
    import gpxpy.gpx
    import math as mod_math
    from geopy import distance as gpdistance
    route_id = request.POST.get('route_id', False)
    success = 'false'
    valid_file = 'false'
    if route_id:
        length_2d = 0
        uphill = 0
        downhill = 0
        moving_time = 0
        stopped_time = 0
        moving_distance = 0
        stopped_distance = 0
        max_speed = 0
        start_elevation = 0
        end_elevation = 0
        start_lat = 0
        start_lon = 0
        end_lat = 0
        end_lon = 0
        total_hours = 0
        total_minutes = 0
        distance_to_summit = 0
        distance_to_summit_temp = 0
        distance_to_summit_time = 0
        distance_to_summit_hours = 0
        distance_to_summit_minutes = 0
        reached_summit = False
        route = PeakRoute.objects.get(id=route_id)
        peak_coords = (route.item.lat, route.item.long)
        if request.POST.get('name'):
            filename = request.POST.get('name')
            key = request.POST.get('key')
            basefile = key.split("/")[-1]
            extension = os.path.splitext(filename)[1][1:].lower()
            item = route.item
            upload_path = key
            #parse gpx
            try:
                f = default_storage.open(upload_path, 'r')
                gpx = gpxpy.parse(f)
                length_2d = gpx.length_2d()
                uphill, downhill = gpx.get_uphill_downhill()
                moving_time, stopped_time, moving_distance, stopped_distance, max_speed = gpx.get_moving_data()

                if length_2d is None:
                    length_2d = 0
                if uphill is None:
                    uphill = 0
                if downhill is None:
                    downhill = 0
                if moving_time is None:
                    moving_time = 0
                if stopped_time is None:
                    stopped_time = 0
                if moving_distance is None:
                    moving_distance = 0
                if stopped_distance is None:
                    stopped_distance = 0
                if max_speed is None:
                    max_speed = 0

                total_time = moving_time + stopped_time
                total_minutes = mod_math.floor(total_time / 60.)
                total_hours = mod_math.floor(total_minutes / 60.)
                start_elevation = 0
                start_lat = 0
                start_lon = 0
                end_lat = 0
                end_lon = 0
                previous_point = None
                for track in gpx.tracks:
                    for segment in track.segments:
                        for point in segment.points:
                            if (start_elevation == 0):
                                start_elevation = point.elevation
                            if (start_lat == 0):
                                start_lat = point.latitude
                            if (start_lon == 0):
                                start_lon = point.longitude
                            end_elevation = point.elevation
                            end_lat = point.latitude
                            end_lon = point.longitude
                            if previous_point is not None and distance_to_summit == 0:
                                #add to running distance to summit
                                distance_to_summit_temp = distance_to_summit_temp + point.distance_2d(previous_point)
                                if point.time and previous_point.time:
                                    temp_time = point.time - previous_point.time
                                    distance_to_summit_time = distance_to_summit_time + temp_time.total_seconds()
                                #calculate distance to summit
                                #gpdistance.VincentyDistance.ELLIPSOID = 'WGS-84'
                                current_point = (point.latitude, point.longitude)
                                #miles = gpdistance.distance(peak_coords, current_point).mi
                                miles = gpdistance.geodesic(peak_coords, current_point).mi
                                if miles < 0.113636:
                                    distance_to_summit = distance_to_summit_temp/1609
                                    reached_summit = True
                            previous_point = point

                if not reached_summit:
                    distance_to_summit = 0
                    distance_to_summit_time = 0
                    distance_to_summit_hours = 0
                    distance_to_summit_minutes = 0

                length_2d = length_2d/1609
                start_elevation = start_elevation/0.3048
                end_elevation = end_elevation/0.3048
                uphill = uphill/0.3048
                total_minutes = total_minutes % 60
                distance_to_summit_minutes = mod_math.floor(distance_to_summit_time / 60.)
                distance_to_summit_hours = mod_math.floor(distance_to_summit_minutes / 60.)
                distance_to_summit_minutes = distance_to_summit_minutes % 60
                success = 'true'
                valid_file = 'true'

            except:
                print('Invalid GPX file.')
        else:
            print('No file.')
    else:
        print('No summit id.')

    return render(request, 'items/ajax/show_uploaded_gpx.html', {'success':success, 'valid_file':valid_file, 'gpx_file':upload_path, 'gpx_basefile':basefile, 'start_index':0, 'end_index':500, 'length_2d':round(length_2d,2), 'start_lat':start_lat, 'start_lon':start_lon, 'start_elevation':int(start_elevation), 'end_lat':end_lat, 'end_lon':end_lon, 'end_elevation':int(end_elevation), 'uphill':int(uphill), 'total_hours':int(total_hours), 'total_minutes':int(total_minutes), 'distance_to_summit_hours':int(distance_to_summit_hours), 'distance_to_summit_minutes':int(distance_to_summit_minutes), 'distance_to_summit':round(distance_to_summit,2)})


@login_required
def add_route_gpx(request):
    import gpxpy
    import gpxpy.gpx
    import math as mod_math
    import time
    timestr = time.strftime("%Y%m%d-%H%M%S")
    route_id = request.GET.get('route_id', False)
    success = 'false'
    if route_id:
        length_2d = 0
        uphill = 0
        start_elevation = 0
        end_elevation = 0
        total_hours = 0
        total_minutes = 0
        route = PeakRoute.objects.get(id=route_id)
        if request.GET.get('qqfile'):
            item = route.item
            count = item.peak_route.count()
            upload_path = 'gpx/%s-%s-%s.gpx' % (item.slug, route_id, timestr)
            #destination = open('%s/%s' % (settings.MEDIA_ROOT, upload_path), 'wb+')
            #destination.write(request.raw_post_data)
            #destination.close()
            file = default_storage.open(upload_path, 'w')
            file.write(request.body)
            file.close()
            #parse gpx
            try:
                gpx = gpxpy.parse(request.body)
                length_2d = gpx.length_2d()
                uphill, downhill = gpx.get_uphill_downhill()
                moving_time, stopped_time, moving_distance, stopped_distance, max_speed = gpx.get_moving_data()

                if length_2d is None:
                    length_2d = 0
                if uphill is None:
                    uphill = 0
                if moving_time is None:
                    moving_time = 0
                if stopped_time is None:
                    stopped_time = 0

                total_time = moving_time + stopped_time
                total_minutes = mod_math.floor(total_time / 60.)
                total_hours = mod_math.floor(total_minutes / 60.)
                start_elevation = 0
                start_lat = 0
                start_lon = 0
                end_lat = 0
                end_lon = 0
                for track in gpx.tracks:
                    for segment in track.segments:
                        for point in segment.points:
                            if (start_elevation == 0):
                                start_elevation = point.elevation
                            if (start_lat == 0):
                                start_lat = point.latitude
                            if (start_lon == 0):
                                start_lon = point.longitude
                            end_elevation = point.elevation
                            end_lat = point.latitude
                            end_lon = point.longitude

                length_2d = length_2d/1609
                start_elevation = start_elevation/0.3048
                end_elevation = end_elevation/0.3048
                uphill = uphill/0.3048
                total_minutes = total_minutes % 60
                success = 'true'

            except:
                print('Invalid GPX file.')

        else:
            print('No file.')

    else:
        print('No route id.')

    return render(request, 'items/ajax/show_uploaded_gpx.html', {'success':success, 'gpx_file':upload_path, 'start_index':0, 'end_index':500, 'length_2d':round(length_2d,2), 'start_lat':start_lat, 'start_lon':start_lon, 'start_elevation':int(start_elevation), 'end_lat':end_lat, 'end_lon':end_lon, 'end_elevation':int(end_elevation), 'uphill':int(uphill), 'total_hours':int(total_hours), 'total_minutes':int(total_minutes)})


@login_required
#@render_to_json()
def upload_main_peak_photo(request):
    data = {}
    error = True
    error_message = ''
    success = False
    success_message = ''
    if 'item_id' in request.GET:
        item_id = request.GET.get('item_id')
        item = Item.objects.get(id=item_id)
        category = ItemPhotoCategory.objects.get(name='peak')
        photo_object = ItemPhoto(user=request.user, item=item, category=category)
        photo_object.save()
        if request.GET.get('qqfile'):
            filename = request.GET.get('qqfile')
#            upload_path = 'items/main/peak-%s' % utils.sanetize_filename(photo_object.id, filename)
            count = item.photos.filter(category__name='peak').count()
            upload_path = 'items/main/%s-%s' % (item.slug, utils.sanetize_filename(count, filename))
            destination = open('%s/%s' % (settings.MEDIA_ROOT, upload_path), 'wb+')
            destination.write(request.raw_post_data)
            destination.close()
        else:
            #IE case
            f = request.FILES['qqfile']
#            upload_path = 'items/main/peak-%s' % utils.sanetize_filename(photo_object.id, f.name)
            count = item.photos.filter(category__name='peak').count()
            upload_path = 'items/main/%s-%s' % (item.slug, utils.sanetize_filename(count, f.name))
            destination = open('%s/%s' % (settings.MEDIA_ROOT, upload_path), 'wb+')
            for chunk in f.chunks():
                destination.write(chunk)
            destination.close()

        try:
            width, height = get_image_dimensions(settings.MEDIA_ROOT + '/' +  upload_path)
        except Exception as e:
            width, height = [-1,-1]

        if width >= settings.MIN_PEAK_IMG_WIDTH and height >= settings.MIN_PEAK_IMG_HEIGHT:
            photo_object.image = upload_path
            photo_object.save()
            item.thumbnail = upload_path
            item.thumbnail_credit = request.user.username
            item.save()
            data['image'] = settings.RETURN_URL + item.get_thumbnail_size(483, 250)
            success = True
            success_message = 'Successfully saved main peak photo'
            error = False
        else:
            import os
            os.remove(settings.MEDIA_ROOT + '/' + upload_path)
            photo_object.delete()
            photo_object = {'valid':'false', }
            error_message = 'This image is too small.'
    data['success'] = success
    data['success_message'] = success_message
    data['error'] = error
    data['error_message'] = error_message
    #    return data
    jsondata = json.dumps(data)
    return HttpResponse(jsondata, content_type='text/html')


@login_required
def summit_add_photo_caption(request):
    if request.is_ajax():
        photo_id = request.GET.get('photo_id')
        caption = request.GET.get('caption')
        photo = ItemPhoto.objects.get(id=photo_id)
        photo.caption = caption
        photo.save()
        return HttpResponse('True')
    else:
        raise Http404


@login_required
@render_to_json()
def summit_delete_photo(request):
    data = {}
    error = True
    error_message = ''
    success = False
    success_message = ''
    if request.method == 'POST':
        if 'photo_id' in request.POST:
            photo_id = request.POST['photo_id']
            photo = ItemPhoto.objects.get(id=photo_id)
            if photo.user == request.user or request.user.is_superuser:
                if photo.summit_log and photo.category.name == 'peak':
                    photo.item.thumbnail = None
                    photo.item.thumbnail_credit = None
                    photo.item.thumbnail_source = None
                    photo.item.save()
                photo.delete()
                error = False
                success = True
                success_message = 'Photo successfully removed'
            else:
                error_message = 'Unable to remove summit'
        else:
            error_message = 'Missing fields in request'
    else:
        error_message = 'Request method must be a POST request'
    data['error'] = error
    data['error_message'] = error_message
    data['success'] = success
    data['success_message'] =  success_message
    return data

@login_required
def peak_upload_photo(request, peak_id):
    import re
    photo_minsize = [settings.MIN_PEAK_IMG_WIDTH, settings.MIN_PEAK_IMG_HEIGHT]
    peak = Item.objects.get(id=peak_id)
    photo_object = None
    if request.method == 'POST':
        form = ItemUploadImageForm(request.POST,request.FILES)
        item_photo_id = request.POST.get('photo_id', '')
        if item_photo_id != '':
            photo_object = ItemPhoto.objects.get(id=item_photo_id)
        if form.is_valid():
            image = False
            if item_photo_id != '':
                image = True
            if not image and form.cleaned_data['source_url'] is not None:
                external_url = form.cleaned_data['source_url']
                external_url = re.sub('[^a-zA-Z0-9-_*.]', '', external_url)
                valid = peak.set_image(external_url)
                image = True
                if not valid:
                    messages.error(request, "This URL is not a valid image. Check the URL and try again.")
                    image = False
                else:
                    category = ItemPhotoCategory.objects.get(name='peak')
                    item_photo = ItemPhoto(item=peak, user=request.user, image=peak.thumbnail, category=category)
                    item_photo.save()
            if image and form.cleaned_data['photo_by'] is not None:
                peak.thumbnail_credit = form.cleaned_data['photo_by']
                category = ItemPhotoCategory.objects.get(name='peak')
                item_photo = ItemPhoto(item=peak, user=request.user, image=peak.thumbnail, category=category, author_photo=form.cleaned_data['photo_by'])
                item_photo.save()
                #borrar duplicado
                current_image=item_photo.image
                for photo in peak.photos.all() :
                    if photo.image == current_image and photo.author_photo is None:
                        photo.delete()

            if image and form.cleaned_data['webpage_linking_to_photo'] is not None:
                peak.thumbnail_source = form.cleaned_data['webpage_linking_to_photo']
            if image:
                messages.success(request, 'Thanks for uploading this photo.')
            peak.save()
            return HttpResponse('True')
    else:
        form = ItemUploadImageForm()
    return render(request, 'items/upload_image_form.html',{'form':form, 'photo_minsize':photo_minsize,'peak':peak, 'photo_object':photo_object})


@login_required
#@render_to_json()
def peak_upload_better_photo(request):
    from PIL import Image
    data = {}
    error = True
    error_message = ''
    success = False
    success_message = ''
    if 'item_id' in request.GET:
        item_id = request.GET.get('item_id')
        item = Item.objects.get(id=item_id)
        category = ItemPhotoCategory.objects.get(name='peak')
        photo_object = ItemPhoto(user=request.user, item=item, category=category)
        photo_object.save()
        if request.GET.get('qqfile'):
            filename = request.GET.get('qqfile')
#            upload_path = 'items/main/peak-%s' % utils.sanetize_filename(photo_object.id, filename)
            count = item.photos.filter(category__name='peak').count()
            upload_path = 'items/main/%s-%s' % (item.slug, utils.sanetize_filename(count, filename))
            #destination = open('%s/%s' % (settings.MEDIA_ROOT, upload_path), 'wb+')
            #destination.write(request.raw_post_data)
            #destination.close()
            file = default_storage.open(upload_path, 'w')
            file.write(request.body)
            file.close()
        else:
            #IE case
            f = request.FILES['qqfile']
#            upload_path = 'items/main/peak-%s' % utils.sanetize_filename(photo_object.id, f.name)
            count = item.photos.filter(category__name='peak').count()
            upload_path = 'items/main/%s-%s' % (item.slug, utils.sanetize_filename(count, f.name))
            destination = open('%s/%s' % (settings.MEDIA_ROOT, upload_path), 'wb+')
            for chunk in f.chunks():
                destination.write(chunk)
            destination.close()

        try:
            f = default_storage.open(upload_path, 'rb')
            image = Image.open(f)
            width, height = image.size
            #width, height = get_image_dimensions(settings.MEDIA_ROOT + '/' +  upload_path)
        except Exception as e:
            width, height = [-1,-1]

        # photo_object = {'valid': 'false', }
        # error_message = 'New constraint failed.'

        if width < settings.MIN_PEAK_IMG_WIDTH or height < settings.MIN_PEAK_IMG_HEIGHT:
            #os.remove(settings.MEDIA_ROOT + '/' + upload_path)
            default_storage.delete(upload_path)
            photo_object.delete()
            photo_object = {'valid':'false', }
            error_message = 'This image is too small.'
        elif height > width:
            #os.remove(settings.MEDIA_ROOT + '/' + upload_path)
            default_storage.delete(upload_path)
            photo_object.delete()
            photo_object = {'valid':'false', }
            error_message = 'This image is not in landscape orientation.'
        elif (width/height) > 2:
            #os.remove(settings.MEDIA_ROOT + '/' + upload_path)
            default_storage.delete(upload_path)
            photo_object.delete()
            photo_object = {'valid':'false', }
            error_message = 'This image appears to be panorama.'
        else:
            photo_object.image = upload_path
            photo_object.save()
            #            item.thumbnail = upload_path
            #            item.save()
            original_photo = item.get_main_photo()
            #When there is only thumbnail
            if not original_photo:
                category = ItemPhotoCategory.objects.get(name='peak')
                original_photo = ItemPhoto(item = item, category=category, user = item.user, image= item.thumbnail)
                original_photo.save()
            original_value = original_photo.image.name
            new_value = photo_object.image.name
            itemcorrection = ItemCorrection(user=request.user, item=item, original_value=original_value, new_value=new_value,
                                            original_photo=original_photo, new_photo = photo_object, field = 6)
            itemcorrection.save()
            data['image'] = settings.RETURN_URL + item.get_thumbnail_size(483, 250)
            success = True
            success_message = "Thanks for sharing. We'll review and let you know when it's approved"
            error = False
    data['success'] = success
    data['success_message'] = success_message
    data['error'] = error
    data['error_message'] = error_message
#    return data
    jsondata = json.dumps(data)
    return HttpResponse(jsondata, content_type='text/html')


@login_required
def peak_upload_better_photo_template(request, peak_id):
    peak = Item.objects.get(id=peak_id)
    return render(request, 'items/upload_better_image.html', {'peak': peak})


@login_required
@render_to_json()
def peak_delete_main_photo(request):
    data = {}
    error = True
    error_message = ''
    success = False
    success_message = ''
    if request.method == 'POST':
        if 'peak_id' in request.POST:
            peak_id = request.POST['peak_id']
            peak = Item.objects.get(id=peak_id)
            #main_photo = peak.get_main_photo()
            #main_photo_user = peak.get_main_photo_user()
            if request.user.is_staff:
                #import os
                #path = str(main_photo.image.file)
                #os.remove(path)
                #main_photo.delete()
                # assign previous main photo if exists
                #photos = peak.photos.filter(category__name='peak').order_by('-created')
                #if photos:
                    #peak.thumbnail = photos[0].image
                    #peak.thumbnail_credit = photos[0].user.username
                #else:
                peak.thumbnail = None
                peak.thumbnail_source = None
                peak.thumbnail_credit = None

                peak.quick_save()
                error = False
                success = True
                success_message = 'Successfully removed main peak photo'
    data['error'] = error
    data['error_message'] = error_message
    data['success'] = success
    data['success_message'] = success_message
    return data


def get_routes(request,peak_id):
    item = peak_id
    q = request.GET.get('q','')

    summits = SummitLog.objects.filter(item = item,route_up__isnull = False)
    item_routes = []
    for summit in summits:
        item_routes.append(summit.route_up.id)
    summits = SummitLog.objects.filter(item = item,route_down__isnull = False)
    for summit in summits:
        item_routes.append(summit.route_down.id)

    summitroutes = ""

    if q:
        summitroutes = SummitRoute.objects.filter(Q(name__istartswith = q) | Q(id__in = item_routes))
    else:
        summitroutes = SummitRoute.objects.all()

    routes_dict = {}
    cont = 0
    for summitroute in summitroutes:
        if summitroute.id in item_routes:
            if summitroute.id in routes_dict.keys():
                routes_dict[summitroute.id]['count'] +=  1
            else:
                testing = lower(summitroute.name)
                if testing.startswith(lower(q)):
                    routes_dict[summitroute.id] = {'name':summitroute.name , 'count':1}
            cont += 1
        else:
            testing = lower(summitroute.name)
            if testing.startswith(lower(q)):
                routes_dict[summitroute.id] = {'name':summitroute.name,'count':0}
        #routes = []
    routes_list = ""

    for route_key in routes_dict.keys():
        routes_list += "%s|%d|%d\n" % (routes_dict[route_key]['name'], 0 if cont ==  0 else int(ceil((float(routes_dict[route_key]['count']) / float(cont)) * 100)), route_key)

    return HttpResponse(routes_list)


def members_list(request,days = None,country = None,region = None):

    requesttimefilter = True
    ip = ''
    country = None
    region = None
    country_code = None
    #have cookie?
    if 'suggest_country_id' in request.COOKIES:
        if request.COOKIES['suggest_country_id'] != '':
            country = get_object_or_None(Country, id=request.COOKIES['suggest_country_id'])
            country_code = country.code
    if 'suggest_region_id' in request.COOKIES:
        if request.COOKIES['suggest_region_id'] != '':
            region = get_object_or_None(Region, id=request.COOKIES['suggest_region_id'])
            country = get_object_or_None(Country, id=region.country_id)
            country_code = country.code
    if country is None and region is None:
        g = GeoIP2()
        ip = get_client_ip(request)

        try:
            country_code = g.country(ip)['country_code']
            country = get_object_or_None(Country, code = country_code)
            ip_region = g.city(ip)['region']
            if country_code == 'US':
                region = get_object_or_None(Region, code='US.'+ip_region)
        except Exception:
            pass

    if days is None:
        requesttimefilter = False
        days = '30'
    if days not in ['365','30','all']:
        days = "all"

    nav_members_style = 'color: #00B1F2;'
    nav_page_name = 'Members'

    return render(request, 'items/view_members_list.html',{
        'ip':ip,
        'country_code':country_code,
        'country':country,
        'region':region,
        'timefilter':days,
        'requesttimefilter':requesttimefilter,
        'nav_members_style': nav_members_style,
        'nav_page_name': nav_page_name
    })

@cache_page(60 * 60)
def challenges_view(request):

    challenges = ItemGroup.objects.raw("select * from items_itemgroup")

    world_challenges = ItemGroup.objects.raw("select * from items_itemgroup where show_in_world = true")

    sql = "select w.* from ( " + \
        "select a.id, a.name, a.description, a.slug, 1 as sort_order, " + \
        "get_thumb(c.thumbnail, 745) as thumbnail " + \
        "from items_itemgroup a, items_itemgroupitem b, items_item c, items_item_country d, cities_country e " + \
        "where a.show_in_country = true " + \
        "and a.id = b.group_id " + \
        "and b.item_id = c.id " + \
        "and length(c.thumbnail) > 0 " + \
        "and c.id = d.item_id " + \
        "and d.country_id = e.id " + \
        "and e.continent = 'NA' " + \
        "order by abs(cast(substring(cast(a.id as text) from '..$') as integer) - cast(substring(cast(random() as text) from '..$') as integer)) " + \
        "limit 1) w " + \
        "union select x.* from ( " + \
        "select a.id, a.name, a.description, a.slug, 2 as sort_order, " + \
        "get_thumb(c.thumbnail, 745) as thumbnail " + \
        "from items_itemgroup a, items_itemgroupitem b, items_item c, items_item_country d, cities_country e " + \
        "where a.show_in_country = true " + \
        "and a.id = b.group_id " + \
        "and b.item_id = c.id " + \
        "and length(c.thumbnail) > 0 " + \
        "and c.id = d.item_id " + \
        "and d.country_id = e.id " + \
        "and e.continent = 'EU' " + \
        "order by abs(cast(substring(cast(a.id as text) from '..$') as integer) - cast(substring(cast(random() as text) from '..$') as integer)) " + \
        "limit 1) x " + \
        "union select y.* from ( " + \
        "select a.id, a.name, a.description, a.slug, 3 as sort_order, " + \
        "get_thumb(c.thumbnail, 745) as thumbnail " + \
        "from items_itemgroup a, items_itemgroupitem b, items_item c, items_item_country d, cities_country e " + \
        "where a.show_in_country = true " + \
        "and a.id = b.group_id " + \
        "and b.item_id = c.id " + \
        "and length(c.thumbnail) > 0 " + \
        "and c.id = d.item_id " + \
        "and d.country_id = e.id " + \
        "and e.continent in ('AS','OC') " + \
        "order by abs(cast(substring(cast(a.id as text) from '..$') as integer) - cast(substring(cast(random() as text) from '..$') as integer)) " + \
        "limit 1) y " + \
        "union select z.* from ( " + \
        "select a.id, a.name, a.description, a.slug, 4 as sort_order, " + \
        "get_thumb(c.thumbnail, 745) as thumbnail " + \
        "from items_itemgroup a, items_itemgroupitem b, items_item c, items_item_country d, cities_country e " + \
        "where a.show_in_country = true " + \
        "and a.id = b.group_id " + \
        "and b.item_id = c.id " + \
        "and length(c.thumbnail) > 0 " + \
        "and c.id = d.item_id " + \
        "and d.country_id = e.id " + \
        "and e.continent in ('SA','AF','AN') " + \
        "order by abs(cast(substring(cast(a.id as text) from '..$') as integer) - cast(substring(cast(random() as text) from '..$') as integer)) " + \
        "limit 1) z " + \
        "order by sort_order"
    top_challenges = ItemGroup.objects.raw(sql)

    sql = "select " + \
        "a.id, " + \
        "a.name, " + \
        "a.slug, " + \
        "count(distinct b.id) as challenge_count " + \
        "from cities_continent a, items_itemgroup b " + \
        "where a.id = b.continent " + \
        "group by " + \
        "a.id, " + \
        "a.name, " + \
        "a.slug " + \
        "order by " + \
        "case when a.id = 1 then '1' when a.id = 2 then '2' when a.id = 3 then '4' when a.id = 4 then '3' when a.id = 5 then '7' when a.id = 6 then '8' when a.id = 7 then '6' when a.id = 8 then '5' else '9' end"
    challenge_continents = ItemGroup.objects.raw(sql)

    sql = "select b.id, b.code as country_code, b.name, b.slug, c.id as continent " + \
        "from cities_country b, cities_continent c " + \
        "where b.show_in_challenges = true and b.continent = c.code " + \
        "order by b.name"

    sql = "select b.id, b.code as country_code, b.name, b.slug, c.id as continent, count(distinct d.id) as challenge_count " + \
        "from cities_country b " + \
        "join cities_continent c on c.code = b.continent " + \
        "left join items_itemgroup d on d.show_in_country = true " + \
        "where b.show_in_challenges = true " + \
        "and exists (select 1 from items_item_country x, items_itemgroupitem y where b.id = x.country_id and x.item_id = y.item_id and y.group_id = d.id " + \
        "and not exists (select 1 from items_item_country z where z.country_id != x.country_id and z.item_id = y.item_id)) " + \
        "group by b.id, b.code, b.name, b.slug, c.id " + \
        "order by b.name"
    challenge_countries = ItemGroup.objects.raw(sql)

    sql = "select b.id, b.name, b.slug, b.country_id " + \
        "from cities_region b " + \
        "where b.show_in_challenges = true " + \
        "order by b.name"

    sql = "select b.id, b.name, b.slug, b.country_id, count(distinct g.id) as challenge_count " + \
        "from cities_region b " + \
        "left join items_itemgroup g on g.show_in_region = true " + \
        "where b.show_in_challenges = true " + \
        "and exists (select 1 from items_item_region x, items_itemgroupitem y, items_itemgroup z where b.id = x.region_id and x.item_id = y.item_id and y.group_id = g.id " + \
        "and not exists (select 1 from items_item_region xx where xx.region_id != x.region_id and xx.item_id = y.item_id)) " + \
        "group by b.id, b.name, b.slug, b.country_id " + \
        "order by b.name"
    challenge_regions = ItemGroup.objects.raw(sql)

    nav_challenges_style = 'color: #00B1F2;'
    nav_page_name = 'Challenges'

    return render(request, 'items/view_challenges.html',{
        'challenges':list(challenges),
        'world_challenges': list(world_challenges),
        'top_challenges':list(top_challenges),
        'challenge_countries':list(challenge_countries),
        'challenge_regions':list(challenge_regions),
        'challenge_continents':list(challenge_continents),
        'MEDIA_URL':settings.MEDIA_URL,
        'nav_challenges_style':nav_challenges_style,
        'nav_page_name':nav_page_name
    })



def peak_list_view(request, group_slug):
    group = get_object_or_404(ItemGroup, slug=group_slug)
    order = request.GET.get('order', '-elevation')
    if order != '-elevation' and order != 'name' and order != 'summitlog_count' and order != '-summitlog_count':
        order = '-elevation'

    members_count = group.pursuers_count()
    finishers_count = group.finishers_count_new()
    pursuers_count = members_count - finishers_count

    sql = "select a.name, a.slug_new_text as slug, a.elevation, floor(a.elevation*.3048) as elevation_in_meters " + \
        "from items_item a, items_itemgroupitem b, items_itemgroup c " + \
        "where c.slug = %s " + \
        "and c.id = b.group_id " + \
        "and b.item_id = a.id " + \
        "order by a.elevation desc " + \
        "limit 1 "

    with connection.cursor() as cursor:
        cursor.execute(sql, [group_slug])
        highest_peak = dictfetchall(cursor)[0]

    sql = "select a.name, a.slug_new_text as slug, a.summitlog_count " + \
        "from items_item a, items_itemgroupitem b, items_itemgroup c " + \
        "where c.slug = %s " + \
        "and c.id = b.group_id " + \
        "and b.item_id = a.id " + \
        "order by a.summitlog_count desc " + \
        "limit 1 "

    with connection.cursor() as cursor:
        cursor.execute(sql, [group_slug])
        most_summited_peak = dictfetchall(cursor)[0]

    sql = "select a.name, a.slug_new_text as slug, a.prominence, floor(a.prominence*.3048) as prominence_in_meters " + \
        "from items_item a, items_itemgroupitem b, items_itemgroup c " + \
        "where c.slug = %s " + \
        "and c.id = b.group_id " + \
        "and b.item_id = a.id " + \
        "and a.prominence is not null " + \
        "order by a.prominence desc " + \
        "limit 1 "

    with connection.cursor() as cursor:
        cursor.execute(sql, [group_slug])

        if cursor.rowcount > 0:
            most_prominent_peak = dictfetchall(cursor)[0]
            most_prominent_peak_prominence = locale.format_string('%d', most_prominent_peak.get('prominence'), grouping=True)
            most_prominent_peak_prominence_in_meters = locale.format_string('%d', most_prominent_peak.get('prominence_in_meters'), grouping=True)
        else:
            most_prominent_peak = None
            most_prominent_peak_prominence = None
            most_prominent_peak_prominence_in_meters = None


    sql = "select to_char(a.date, 'MM')::integer as month_number, to_char(a.date, 'Month') as summitlog_month, count(a.id) as summitlog_count " + \
        "from items_summitlog a, items_itemgroupitem b, items_itemgroup c " + \
        "where c.slug = %s " + \
        "and c.id = b.group_id " + \
        "and b.item_id = a.item_id " + \
        "group by month_number, summitlog_month " + \
        "order by count(a.id) desc "

    with connection.cursor() as cursor:
        cursor.execute(sql, [group_slug])
        top_climbing_months = dictfetchall(cursor)

    sql = "select a.name, a.slug_new_text as slug, a.elevation, min(d.difficulty) as least_difficult_route " + \
        "from items_item a, items_itemgroupitem b, items_itemgroup c, items_peakroute d " + \
        "where c.slug = %s " + \
        "and c.id = b.group_id " + \
        "and b.item_id = a.id " + \
        "and a.id = d.item_id " + \
        "and length(d.difficulty) > 0 " + \
        "group by a.name, a.slug_new_text, a.elevation " + \
        "order by min(d.difficulty) asc, a.elevation desc " + \
        "limit 1"

    with connection.cursor() as cursor:
        cursor.execute(sql, [group_slug])

        if cursor.rowcount > 0:
            most_difficult_peak = dictfetchall(cursor)[0]
        else:
            most_difficult_peak = None

    #Difficulty breakdown
    sql = "select x.least_difficult_route, count(x.*) as peak_count from ( " + \
        "select a.name, a.elevation, min(d.difficulty) as least_difficult_route " + \
                "from items_item a, items_itemgroupitem b, items_itemgroup c, items_peakroute d " + \
                "where c.slug = %s " + \
                "and c.id = b.group_id " + \
                "and b.item_id = a.id " + \
                "and a.id = d.item_id " + \
                "and length(d.difficulty) > 0 " + \
                "group by a.name, a.elevation) x " + \
                "group by x.least_difficult_route " + \
                "order by x.least_difficult_route "

    with connection.cursor() as cursor:
        cursor.execute(sql, [group_slug])

        if cursor.rowcount > 0:
            difficulty_breakdown = dictfetchall(cursor)
        else:
            difficulty_breakdown = None

    class_one_two_peaks = 0
    class_three_four_peaks = 0
    class_five_peaks = 0

    if difficulty_breakdown:
        for d in difficulty_breakdown:
            if d['least_difficult_route'] == 'Class 1' or d['least_difficult_route'] == 'Class 2':
                class_one_two_peaks = class_one_two_peaks + d['peak_count']
            if d['least_difficult_route'] == 'Class 3' or d['least_difficult_route'] == 'Class 4':
                class_three_four_peaks = class_three_four_peaks + d['peak_count']
            if d['least_difficult_route'] == 'Class 5':
                class_five_peaks = class_five_peaks + d['peak_count']

    #Total distance
    sql = "select a.id, a.name, a.elevation, avg(e.total_distance) as total_distance " + \
        "from items_item a, items_itemgroupitem b, items_itemgroup c, items_summitlog e " + \
        "where c.slug = %s " + \
        "and c.id = b.group_id " + \
        "and b.item_id = a.id " + \
        "and a.id = e.item_id " + \
        "and e.total_distance > 0 " + \
        "group by a.id, a.name, a.elevation "

    with connection.cursor() as cursor:
        cursor.execute(sql, [group_slug])

        if cursor.rowcount > 0:
            total_distance_list = dictfetchall(cursor)
        else:
            total_distance_list = None

    total_distance = 0
    total_distance_peak_count = 0

    if total_distance_list:
        for t in total_distance_list:
            total_distance_peak_count = total_distance_peak_count + 1
            total_distance = total_distance + t['total_distance']

    sql = "select a.id, a.name, a.elevation, avg(e.elevation_gain) as elevation_gain " + \
        "from items_item a, items_itemgroupitem b, items_itemgroup c, items_summitlog e " + \
        "where c.slug = %s " + \
        "and c.id = b.group_id " + \
        "and b.item_id = a.id " + \
        "and a.id = e.item_id " + \
        "and e.elevation_gain > 0 " + \
        "group by a.id, a.name, a.elevation "

    with connection.cursor() as cursor:
        cursor.execute(sql, [group_slug])

        if cursor.rowcount > 0:
            elevation_gain_list = dictfetchall(cursor)
        else:
            elevation_gain_list = None

    elevation_gain = 0
    elevation_gain_peak_count = 0

    if elevation_gain_list:
        for e in elevation_gain_list:
            elevation_gain_peak_count = elevation_gain_peak_count + 1
            elevation_gain = elevation_gain + e['elevation_gain']

    top_climbing_months_total = 0
    for m in top_climbing_months:
        top_climbing_months_total = top_climbing_months_total + m.get('summitlog_count')
    top_three_months = []
    keys = ['summitlog_month', 'month_number', 'summitlog_count', 'pct_total']
    for m in top_climbing_months[:3]:
        values = [m.get('summitlog_month'), m.get('month_number'), m.get('summitlog_count'), int(100 * float(m.get('summitlog_count'))/float(top_climbing_months_total))]
        top_three_months.append(dict(zip(keys, values)))

    #Highlights
    highlights = ItemGroupHighlight.objects.filter(group_id = group.id)

    #Featured summit logs
    featured_logs = cache_manager.get_full_challenge_featured_logs(group.id)
    if not featured_logs:
        sql = "select a.id, d.name as peak_name, d.slug_new_text, case when length(a.log) > 1200 then concat(left(a.log, 1200),'...') else a.log end as log_text, a.date as summitlog_date, b.username, " + \
            "case when min(g.image) is not null then get_thumb(min(g.image), 480) else coalesce(replace(replace(f.avatar, '/', '/resized/100/'), 'avatars/resized/100/', 'avatars/'),'img/default-user.png') end as thumbnail_url " + \
            "from items_summitlog a " + \
            "join auth_user b on b.id = a.user_id " + \
            "join items_itemgroupitem c on c.item_id = a.item_id " + \
            "join items_item d on d.id = a.item_id " + \
            "left join avatar_avatar f on f.user_id = a.user_id " + \
            "left join items_itemphoto g on g.summit_log_id = a.id " + \
            "where c.group_id = %s and a.status = 1 and a.date_entered = true and length(a.log) >= 175 " + \
            "group by a.id, log_text, a.date, b.username, d.name, d.slug_new_text, f.avatar " + \
            "order by a.date desc limit 5 "

        with connection.cursor() as cursor:
            cursor.execute(sql, [group.id])
            featured_logs = dictfetchall(cursor)

        cache_manager.set_full_challenge_featured_logs(group.id, featured_logs)

    items = group.items.filter(active=1).order_by(order)
    top_peaks = group.items.filter(active=1).order_by('-elevation')

    nav_page_name = group.name

    group_items_count = group.items.count()

    return render(request, 'items/view_challenge_details.html',{
        'group':group,
        'group_items_count': group_items_count,
        'items':items,
        'top_peaks':top_peaks,
        'pursuers_count': pursuers_count,
        'finishers_count': finishers_count,
        'highest_peak': highest_peak,
        'highest_peak_elevation': locale.format_string('%d', highest_peak.get('elevation'), grouping=True),
        'highest_peak_elevation_in_meters': locale.format_string('%d', highest_peak.get('elevation_in_meters'), grouping=True),
        'most_summited_peak': most_summited_peak,
        'most_summited_peak_summits': locale.format_string('%d', most_summited_peak.get('summitlog_count'), grouping=True),
        'most_prominent_peak': most_prominent_peak,
        'most_prominent_peak_prominence': most_prominent_peak_prominence,
        'most_prominent_peak_prominence_in_meters': most_prominent_peak_prominence_in_meters,
        'most_difficult_peak': most_difficult_peak,
        'class_one_two_peaks': class_one_two_peaks,
        'class_three_four_peaks': class_three_four_peaks,
        'class_five_peaks': class_five_peaks,
        'total_distance': locale.format_string('%d', total_distance, grouping=True),
        'total_distance_in_km': locale.format_string('%d', total_distance * 1.60934, grouping=True),
        'total_distance_peak_count': total_distance_peak_count,
        'elevation_gain': locale.format_string('%d', elevation_gain, grouping=True),
        'elevation_gain_in_meters': locale.format_string('%d', elevation_gain * 0.3048, grouping=True),
        'elevation_gain_peak_count': total_distance_peak_count,
        'highlights': highlights,
        'featured_logs':featured_logs,
        'top_three_months': top_three_months,
        'subnav_info_style': 'color: #F24100;',
        'nav_challenges_style': 'color: #00B1F2;',
        'nav_page_name':nav_page_name,
        'fixed_subnav_class': ''
    })

@login_required
def challenge_edit_highlights(request, group_id):
    group = get_object_or_404(ItemGroup, id=group_id)
    if request.method == 'POST' and request.is_ajax():
        formdata = {}
        for p in request.POST:
            if (request.POST[p] != ''):
                keys = p.split('-')
                key = keys[2]
                val = request.POST[p]
                formdata[key] = val
        #create new log entry
        log_group = ItemGroupHighlightLogGroup(user=request.user, group=group, log_date=datetime.datetime.now())
        log_group.save()
        #delete existing highlights
        sql = "delete from items_itemgrouphighlight where group_id = %s"
        with connection.cursor() as cursor:
            cursor.execute(sql, [group_id])
        #add new highlights
        new_highlights = []
        for k,v in sorted(formdata.items()):
            highlight = ItemGroupHighlight(user=request.user, group=group, highlight=v.replace("\n", " ").replace("\r", " "))
            highlight.save()
            new_highlights.append(v.replace("\n", " ").replace("\r", " "))
        #copy highlights to log
        sql = "insert into items_itemgrouphighlightlog (log_group_id, highlight) select %s, highlight from items_itemgrouphighlight where group_id = %s order by id"
        with connection.cursor() as cursor:
            cursor.execute(sql, [log_group.id, group_id])
        data = {}
        data['error'] = False
        data['error_message'] = ''
        data['success'] = True
        data['success_message'] =  'Highlights updated'
        data['highlights'] = new_highlights
        return HttpResponse(json.dumps(data), content_type='application/json')
    else:
        return render(request, 'edit_challenge_highlights.html', {'highlights':highlights, 'group':group})

@login_required
def edit_route_highlights(request, peak_id, route_id):
    route = get_object_or_404(PeakRoute, id=route_id)
    if request.method == 'POST' and request.is_ajax():
        formdata = {}
        for p in request.POST:
            if (request.POST[p] != ''):
                keys = p.split('-')
                key = keys[2]
                val = request.POST[p]
                formdata[key] = val
        #create new log entry
        log_group = PeakRouteHighlightLogGroup(user=request.user, peak_route=route, log_date=datetime.datetime.now())
        log_group.save()
        #delete existing highlights
        sql = "delete from items_peakroutehighlight where peak_route_id = %s"
        with connection.cursor() as cursor:
            cursor.execute(sql, [route_id])
        #add new highlights
        new_highlights = []
        for k,v in sorted(formdata.items()):
            highlight = PeakRouteHighlight(user=request.user, peak_route=route, highlight=v.replace("\n", " ").replace("\r", " "))
            highlight.save()
            new_highlights.append(v.replace("\n", " ").replace("\r", " "))
        #copy highlights to log
        sql = "insert into items_peakroutehighlightlog (log_group_id, highlight) select %s, highlight from items_peakroutehighlight where peak_route_id = %s order by id"
        with connection.cursor() as cursor:
            cursor.execute(sql, [log_group.id, route_id])
        data = {}
        data['error'] = False
        data['error_message'] = ''
        data['success'] = True
        data['success_message'] =  'Highlights updated'
        data['highlights'] = new_highlights
        return HttpResponse(json.dumps(data), content_type='application/json')
    else:
        return render(request, 'edit_route_highlights.html', {'highlights':highlights, 'route':route})

def peak_list_view_peaks(request, group_slug):
    group = get_object_or_404(ItemGroup, slug=group_slug)
    items = group.items.all()
    peak_count = len(items)

    subnav_peaks_style = 'color: #F24100;'
    nav_challenges_style = 'color: #00B1F2;'

    nav_page_name = group.name

    return render(request, 'items/view_challenge_peaks.html',{
        'group':group,
        'peak_count':peak_count,
        'subnav_peaks_style': subnav_peaks_style,
        'nav_challenges_style': nav_challenges_style,
        'nav_page_name':nav_page_name
    })

def peak_list_view_map(request, group_slug):
    group = get_object_or_404(ItemGroup, slug=group_slug)
    items = group.items.select_related('region','region__country', 'country').all().order_by('-summitlog_count')

    subnav_map_style = 'color: #F24100;'
    nav_challenges_style = 'color: #00B1F2;'

    nav_page_name = group.name

    return render(request, 'items/view_challenge_map.html',{
        'group':group,
        'items':items,
        'subnav_map_style': subnav_map_style,
        'nav_challenges_style': nav_challenges_style,
        'nav_page_name':nav_page_name
    })


def peak_list_view_members(request, group_slug):
    group = get_object_or_404(ItemGroup, slug=group_slug)
    members_count = group.pursuers_count()
    finishers_count = group.finishers_count()
    pursuers_count = members_count - finishers_count

    return render(request, 'items/view_challenge_members.html',{
        'group':group,
        'members_count':members_count,
        'finishers_count':finishers_count,
        'pursuers_count':pursuers_count,
        'subnav_members_style': 'color: #F24100;',
        'nav_challenges_style': 'color: #00B1F2;',
        'nav_page_name': group.name,
        'fixed_subnav_class': ""
    })

def peak_list_view_summits(request, group_slug):
    group = get_object_or_404(ItemGroup, slug=group_slug)
    group_items = group.items.select_related('region','region__country', 'country').all().order_by('-summitlog_count')
    summitlogs = SummitLog.objects.filter(item__id__in=group_items)
    summit_months = group.get_summitlog_count_by_month()

    subnav_summits_style = 'color: #F24100;'
    nav_challenges_style = 'color: #00B1F2;'

    nav_page_name = group.name

    return render(request, 'items/view_challenge_summits.html',{
        'group': group,
        'items': group_items,
        'summitlogs': summitlogs,
        'summit_months': summit_months,
        'subnav_summits_style': subnav_summits_style,
        'nav_challenges_style': nav_challenges_style,
        'nav_page_name':nav_page_name
    })

def peak_list_view_recent_summits(request, group_slug):
    group = get_object_or_404(ItemGroup, slug=group_slug)
    order = request.GET.get('order', '-elevation')
    user_progress = None

    #user queries
    group_items = group.items.all().values_list('id', flat=True)
    group_count = len(group_items)

    """ manual pagination """
    try:
        page = int(request.GET.get('page', '1'))
        end = page * 40
        ini = end - 40
        previous = page - 1
        next = page + 1
    except ValueError:
        page = 1
        ini = 0
        end = 40
    getvars_context = request.GET.copy()
    if 'page' in getvars_context:
        del getvars_context['page']
    if len(getvars_context.keys()) > 0:
        getvars = "&%s" % getvars_context.urlencode()
    else:
        getvars = ''

    all_summits = group.all_summits()
    all_summits_count = len(all_summits)

    all_summits = all_summits[ini:end]

    return render(request, 'items/view_list_details_recent_summits.html',{'group':group,
                                                                             'all_summits':all_summits,
                                                                             'all_summits_count': all_summits_count,
                                                                             'order':order,
                                                                             'group_count': group_count,
                                                                             'ini':ini+1,
                                                                             'end':end,
                                                                             'previous':previous,
                                                                             'next':next,
                                                                             'getvars':getvars,
                                                                             })

def peak_list_view_top_pursuers(request, group_slug):
    group = get_object_or_404(ItemGroup, slug=group_slug)
    order = request.GET.get('order', '-elevation')

    #user queries
    group_items = group.items.all().values_list('id', flat=True)
    group_count = len(group_items)

    users_ids = SummitLog.objects.filter(item__id__in=group_items).values_list('user', flat=True).order_by('user').distinct()

    #finishers
    temp_leaders_ids = []
    for usuario_id in users_ids:
        count_qs = SummitLog.objects.filter(user__id=usuario_id, item__in=group_items).values('user','item').distinct()
        user_summited_count = count_qs.count()
        temp_leaders_ids.append( (usuario_id, user_summited_count) )

    #in progress leaders
    leaders = []
    temp_leaders_ids = sorted(temp_leaders_ids, key=itemgetter(1), reverse=True)
    for leader_id in temp_leaders_ids:
        leader = User.objects.get(id=leader_id[0])
        profile = leader.person
        lastest_summit = profile.lastest_summit_from_list(group)
        completed = profile.get_list_stats(group)
        completed_percentage = [None,0,0,0]
        if not completed:
            completed = completed_percentage
        dict = {"user":leader, "lastest_summit":lastest_summit[0], "completed":completed}
        leaders.append(dict)

    items = group.items.select_related('region','region__country').all().order_by(order)

    """ manual pagination """
    try:
        page = int(request.GET.get('page', '1'))
        end = page * 40
        ini = end - 40
        previous = page - 1
        next = page + 1
    except ValueError:
        page = 1
        ini = 0
        end = 40
    getvars_context = request.GET.copy()
    if 'page' in getvars_context:
        del getvars_context['page']
    if len(getvars_context.keys()) > 0:
        getvars = "&%s" % getvars_context.urlencode()
    else:
        getvars = ''

    leaders_count = len(leaders)
    leaders = leaders[ini:end]
    return render(request, 'items/view_list_details_top_pursuers.html',{'group':group,
                                                                           'items':items,
                                                                           'order':order,
                                                                           'leaders':leaders,
                                                                           'group_count': group_count,
                                                                           'leaders_count': leaders_count,
                                                                           'ini':ini+1,
                                                                           'end':end,
                                                                           'previous':previous,
                                                                           'next':next,
                                                                           'getvars':getvars,
                                                                           })

def peak_list_view_finishers(request, group_slug):
    group = get_object_or_404(ItemGroup, slug=group_slug)
    order = request.GET.get('order', '-elevation')

    #user queries
    group_items = group.items.all().values_list('id', flat=True)
    group_count = len(group_items)

    users_ids = SummitLog.objects.filter(item__id__in=group_items).values_list('user', flat=True).order_by('user').distinct()

    #finishers
    finishers = []

    for usuario_id in users_ids:
        count_qs = SummitLog.objects.filter(user__id=usuario_id, item__in=group_items).values('user','item').distinct()
        user_summited_count = count_qs.count()
        if user_summited_count >= group_count:
            finisher = User.objects.get(id=usuario_id)
            lastest_summit = finisher.person.lastest_summit_from_list(group)
            dict = {"user":finisher, "lastest_summit":lastest_summit[0]}
            finishers.append(dict)

    items = group.items.select_related('region','region__country').all().order_by(order)

    """ manual pagination """
    try:
        page = int(request.GET.get('page', '1'))
        end = page * 40
        ini = end - 40
        previous = page - 1
        next = page + 1
    except ValueError:
        page = 1
        ini = 0
        end = 40
    getvars_context = request.GET.copy()
    if 'page' in getvars_context:
        del getvars_context['page']
    if len(getvars_context.keys()) > 0:
        getvars = "&%s" % getvars_context.urlencode()
    else:
        getvars = ''

    finishers_count = len(finishers)
    finishers = finishers[ini:end]
    return render(request, 'items/view_list_details_finishers.html',{'group':group,
                                                                        'items':items,
                                                                        'order':order,
                                                                        'group_count': group_count,
                                                                        'finishers':finishers,
                                                                        'finishers_count': finishers_count,
                                                                        'ini':ini+1,
                                                                        'end':end,
                                                                        'previous':previous,
                                                                        'next':next,
                                                                        'getvars':getvars,
                                                                        })


def summit_badges_by_user(request, username):
    try:
        items_list = set()
        summits_logs = list()
        order = request.GET.get('order', '-date')
        special_order = None
        ini = int(request.GET.get('ini', 0))
        fin = int(request.GET.get('fin', 18))
        fin += ini

        if order == 'most-claimed':
            order = '-date'
            special_order = True

        user = User.objects.get(username=username)

        summits = user.summit_log.all().order_by(order)

        for s in summits:
            if not s.item in items_list:
                items_list.add(s.item)
                summits_logs.append(s)
        if special_order:
            summits_logs_so = summits_logs
            summits_logs = list()
            for summit in summits_logs_so:
                summits_logs.append([summit.summit_times(), summit])
            summits_logs.sort(key=itemgetter(0),reverse=True)
            summits_logs = [x[1] for x in summits_logs]
            print(summits_logs)
        first_time = False
        if ini == 0: first_time = True

        total_summits = len(summits_logs)

        summits_logs = summits_logs[ini:fin]
        total_page = len(summits_logs)
        available = False

        if user.person.new_peak_badges_count() > (ini + total_page):
            print("more bagdes!")
            available = True
        else:
            print("no more bagdes!")
            available = False

        next_page = total_page + ini

        order_dict = dict()

        if order == '-date':
            if not special_order:
                order_dict['order_by'] = '-date'
                order_dict['order_date'] = 'current'
            else:
                order_dict['order_most_claimed'] = 'current'
        elif order == '-item__elevation':
            order_dict['order_by'] = '-item__elevation'
            order_dict['order_badge'] = 'current'
        else:
            order_dict['order_by'] = '-date'
            order_dict['order_date'] = 'current'

        return render(request, 'items/ajax/summit_badges_by_user.html',{'summits':summits_logs,'first_time':first_time, 'total_summits':total_summits, 'total_pages':total_page, 'available':available, 'user':user, 'next_page':next_page, 'order':order_dict, })
    except:
        raise Http404


def summit_comments_by_user(request, username):
    try:
        ini = request.GET.get('ini', 0)
        fin = request.GET.get('fin', 5)
        user = User.objects.get(username=username)
        comments = user.summit_log_comments.select_related('summit_log').all().order_by('-created')[ini:fin]

        first_time = False
        if ini == 0: first_time = True

        return render(request, 'summits/ajax/comments_by_user.html',{'comments':comments, 'profile':user.person, 'first_time':first_time})
    except:
        raise Http404


def peaklists_by_user(request, username):

    try:
        ini = int(request.GET.get('ini',0))
        fin = int(request.GET.get('fin',5))
        user = User.objects.get(username=username)
        lists = user.person.get_lists()
        lists_count = len(lists)
        lists = lists[ini:fin]
        first_time = False
        if ini == 0: first_time = True
    except:
        raise Http404
    return render(request, 'items/ajax/peaklists_by_user.html',{
        'lists_count':lists_count,
        'username':username,'lists':lists, 'first_time': first_time,'profile':user.person})


@login_required
@render_to_json()
def summit_delete(request):
    data = {}
    error = True
    error_message = ''
    success = False
    success_message = ''
    if request.method == 'POST':
        if 'summit_id' in request.POST:
            summit_id = request.POST['summit_id']
            summit = SummitLog.objects.get(id=summit_id, user=request.user)
            peak = Item.objects.get(id=summit.item_id)
            if summit and peak:
                summit.delete()
                error = False
                success = True
                success_message = 'Summit successfully removed'
                #update summitlog count for item
                summitlog_count = peak.summits.all().count()
                peak.summitlog_count = summitlog_count
                peak.quick_save()
            else:
                error_message = 'Unable to remove summit'
        else:
            error_message = 'Missing fields in request'
    else:
        error_message = 'Request method must be a POST request'
    data['error'] = error
    data['error_message'] = error_message
    data['success'] = success
    data['success_message'] =  success_message
    return data




@login_required
def summit_comment_edit(request, comment_id):
    comment = SummitLogComment.objects.select_related('summit_log').get(id=comment_id, user=request.user)
    if request.method == 'POST' and request.is_ajax():
        form = SummitLogCommentForm(request.POST, instance=comment)
        data = {}
        error = True
        error_message = ''
        success = False
        success_message = ''
        if form.is_valid():
            form.save()
            error = False
            success = True
            success_message = 'Comment was successfully modified'
            data['comment'] = request.POST.get('comment')
        else:
            data['errors'] = form.errors
        data['error'] = error
        data['error_message'] = error_message
        data['success'] = success
        data['success_message'] =  success_message
        return HttpResponse(json.dumps(data), content_type='application/json')
    else:
        form = SummitLogCommentForm(instance=comment)
        return render(request, 'summits/ajax/comment_edit.html', {'comment':comment, 'form':form})


@login_required
def item_edit_highlights(request, peak_id):
    peak = get_object_or_404(Item, id=peak_id)
    if request.method == 'POST' and request.is_ajax():
        formdata = {}
        for p in request.POST:
            if (request.POST[p] != ''):
                keys = p.split('-')
                key = keys[2]
                val = request.POST[p]
                formdata[key] = val
        #create new log entry
        log_group = ItemHighlightLogGroup(user=request.user, item=peak, log_date=datetime.datetime.now())
        log_group.save()
        #delete existing highlights
        sql = "delete from items_itemhighlight where item_id = %s"
        with connection.cursor() as cursor:
            cursor.execute(sql, [peak_id])
        #add new highlights
        new_highlights = []
        for k,v in sorted(formdata.items()):
            highlight = ItemHighlight(user=request.user, item=peak, highlight=v.replace("\n", " ").replace("\r", " "))
            highlight.save()
            new_highlights.append(v.replace("\n", " ").replace("\r", " "))
        #copy highlights to log
        sql = "insert into items_itemhighlightlog (log_group_id, highlight) select %s, highlight from items_itemhighlight where item_id = %s order by id"
        with connection.cursor() as cursor:
            cursor.execute(sql, [log_group.id, peak_id])
        data = {}
        data['error'] = False
        data['error_message'] = ''
        data['success'] = True
        data['success_message'] =  'Highlights updated'
        data['highlights'] = new_highlights
        return HttpResponse(json.dumps(data), content_type='application/json')
    else:
        return render(request, 'items/peak_edit_highlights.html', {'highlights':[], 'peak':peak})

@login_required
def summit_comment_edit_delete(request):
    data = {}
    error = True
    error_message = ''
    success = False
    success_message = ''
    if request.method == 'POST':
        if 'comment_id' in request.POST:
            comment_id = request.POST['comment_id']
            comment = SummitLogComment.objects.select_related('summit_log').get(id=comment_id, user=request.user)
            comment.delete()
            error = False
            success = True
            success_message = 'Comment successfully removed'
        else:
            error_message = 'Missing fields in request'
    else:
        error_message = 'Request method must be a POST request'
    data['error'] = error
    data['error_message'] = error_message
    data['success'] = success
    data['success_message'] =  success_message
    return HttpResponse(json.dumps(data), content_type='application/json')


def autocomplete_item_name(request):
    term = request.GET.get('term')
    items = Item.objects.only('id','name').filter(name__istartswith=term)
    res = []
    for i in items:
        dict = {'id':i.id, 'label':i.name, 'value':i.name}
        res.append(dict)
    return HttpResponse(json.dumps(res))


@login_required
def item_add_old(request):
    form = ItemAddForm()
    return render(request, 'items/add_peak_form.html',{'form':form})


@login_required
def fix_item_location(request):
    data = {}
    if request.user.has_perm('items.change_peak_loc_in_map'):
        data['status'] = "Failed"
        data['message'] = "Failed"
        try:
            id  = int(request.POST.get('id', None))
            lat = float(request.POST.get('lat', None))
            long= float(request.POST.get('long', None))
        except:
            data['message'] = "Error getting Parameters"
        try:
            #get the item
            item = Item.objects.get(pk=int(id))
            #save an approved item correction
            location = Point(float(long), float(lat))
            correction = ItemCorrection(status=STATUS_APPROVED, user=request.user, item=item, field=FIELDS_COORDS, original_value=str(item.lat) + ',' + str(item.long), new_value=str(lat) + ',' + str(long), location=location, created=datetime.datetime.now(), decision_date=datetime.datetime.now())
            correction.save()
            data['original_lat'] = item.lat
            data['original_lng'] = item.long
            #update the item
            item.lat = lat
            item.long = long

            item.save_location(audit=request.user)
            data['status'] = "Success"
            data['message'] = "Updated location for %s" % (item.name)
        except Exception as e:
            print(e)
            data['message'] = "Item doesn't exist"
    else:
        data['message'] = "Not allowed"
    jsondata = json.dumps(data)
    return HttpResponse(jsondata, content_type='text/html')


@login_required
def undo_fix_item_location(request):
    data = {}
    if request.user.has_perm('items.change_peak_loc_in_map'):
        data['status'] = "Failed"
        data['message'] = "Failed"
        try:
            id  = int(request.POST.get('id', None))
            lat = float(request.POST.get('lat', None))
            long= float(request.POST.get('long', None))
        except:
            data['message'] = "Error getting Parameters"
        try:
            #get the item
            item = Item.objects.get(pk=int(id))
            #save an approved item correction
            location = Point(float(long), float(lat))
            correction = ItemCorrection(status=STATUS_APPROVED, user=request.user, item=item, field=FIELDS_COORDS, original_value=str(item.lat) + ',' + str(item.long), new_value=str(lat) + ',' + str(long), location=location, created=datetime.datetime.now(), decision_date=datetime.datetime.now())
            correction.save()
            #update the item
            item.lat = lat
            item.long = long
            item.save_location(audit=request.user)
            data['status'] = "Success"
            data['message'] = "Location reverted for %s" % (item.name)
            data['original_lat'] = lat
            data['original_lng'] = long
        except Exception as e:
            print(e)
            data['message'] = "Item doesn't exist"
    else:
        data['message'] = "Not allowed"
    jsondata = json.dumps(data)
    return HttpResponse(jsondata, content_type='text/html')


@login_required
def delete_peak_from_map(request):
    data = {}
    if request.user.has_perm('items.change_peak_loc_in_map'):
        data['status'] = "Failed"
        data['message'] = "Failed"
        try:
            id = int(request.POST.get('id', None))
        except:
            data['message'] = "Error getting Parameters"
        try:
            # get the item
            item = Item.objects.get(pk=int(id))
            item.active = False
            item.save()
            # save an item correction
            correction = ItemCorrection(status=STATUS_NEW, user=request.user, item=item, field=FIELDS_DELETE, created=datetime.datetime.now())
            correction.save()
            #item.delete()
            data['status'] = "Success"
            data['message'] = "Deleted %s" % (item.name)
        except Exception as e:
            print(e)
            data['message'] = "Item doesn't exist"
    else:
        data['message'] = "Not allowed"
    jsondata = json.dumps(data)
    return HttpResponse(jsondata, content_type='text/html')

@login_required
def undo_delete_peak_from_map(request):
    data = {}
    if request.user.has_perm('items.change_peak_loc_in_map'):
        data['status'] = "Failed"
        data['message'] = "Failed"
        try:
            id  = int(request.POST.get('id', None))
        except:
            data['message'] = "Error getting Parameters"
        try:
            #get the item
            item = Item.objects.get(pk=int(id))
            item.active = True
            item.save()
            #delete the item correction
            correction = ItemCorrection.objects.get(item=item, field=11, status=1)
            correction.delete()
            data['status'] = "Success"
            data['message'] = "Delete reverted for %s" % (item.name)
        except Exception as e:
            print(e)
            data['message'] = "Item doesn't exist"
    else:
        data['message'] = "Not allowed"
    jsondata = json.dumps(data)
    return HttpResponse(jsondata, content_type='text/html')

@staff_member_required
def admin_update_peak_elevation(request):
    message = "Failed"
    try:
        id = int(request.POST.get('id', None))
        new_peak_elevation = request.POST.get('elevation', None)
        peak_elevation_units = request.POST.get('units', None)
    except:
        message = "Error getting parameters"
    try:
        item = Item.objects.get(pk=int(id))
        if new_peak_elevation != '':
            if peak_elevation_units == 'm':
                new_peak_elevation = float(new_peak_elevation) / .3048
            else:
                new_peak_elevation = float(new_peak_elevation)
            correction = ItemCorrection(status=STATUS_APPROVED, user=request.user, item=item, field=FIELDS_ELEVATION, original_value=item.elevation, new_value=new_peak_elevation, created=datetime.datetime.now(), decision_date=datetime.datetime.now())
            correction.save()
            item.elevation = new_peak_elevation
            item.quick_save()
        message = "Updated elevation for %s" % item.name
        print(message)
    except Exception as e:
        print(e)
        message = "Item doesn't exist"
    return HttpResponse(message)

@staff_member_required
def admin_update_peak_prominence(request):
    message = "Failed"
    try:
        id = int(request.POST.get('id', None))
        new_peak_prominence = request.POST.get('prominence', None)
        peak_prominence_units = request.POST.get('units', None)
    except:
        message = "Error getting parameters"
    try:
        item = Item.objects.get(pk=int(id))
        if new_peak_prominence != '':
            if peak_prominence_units == 'm':
                new_peak_prominence = float(new_peak_prominence) / .3048
            else:
                new_peak_prominence = float(new_peak_prominence)
        else:
            new_peak_prominence = None
        correction = ItemCorrection(status=STATUS_APPROVED, user=request.user, item=item, field=FIELDS_PROMINENCE, original_value=item.prominence, new_value=new_peak_prominence, created=datetime.datetime.now(), decision_date=datetime.datetime.now())
        correction.save()
        item.prominence = new_peak_prominence
        item.quick_save()
        message = "Updated prominence for %s" % item.name
        print(message)
    except Exception as e:
        print(e)
        message = "Item doesn't exist"
    return HttpResponse(message)

@staff_member_required
def admin_update_peak_range(request):
    message = "Failed"
    try:
        id = int(request.POST.get('id', None))
        new_peak_range = request.POST.get('range', None)
    except:
        message = "Error getting parameters"
    try:
        item = Item.objects.get(pk=int(id))
        correction = ItemCorrection(status=STATUS_APPROVED, user=request.user, item=item, field=FIELDS_RANGE, original_value=item.range, new_value=new_peak_range, created=datetime.datetime.now(), decision_date=datetime.datetime.now())
        correction.save()
        item.range = new_peak_range
        item.quick_save()
        message = "Updated range for %s" % item.name
        print(message)
    except Exception as e:
        print(e)
        message = "Item doesn't exist"
    return HttpResponse(message)

@staff_member_required
def admin_update_peak_to_challenge(request):
    message = "Failed"
    try:
        id = int(request.POST.get('id', None))
        challengeid = request.POST.get('challengeid', None)
        action = request.POST.get('action', None)
    except:
        message = "Error getting parameters"
    try:
        item = Item.objects.get(pk=int(id))
        challenge = ItemGroup.objects.get(pk=int(challengeid))
        if action == 'add':
            sql = "insert into items_itemgroupitem (item_id, group_id, \"order\") " + \
                "select %s, %s, 0 where not exists (select 1 from items_itemgroupitem where item_id = %s and group_id = %s) "
            with connection.cursor() as cursor:
                cursor.execute(sql, [id, challengeid, id, challengeid])
            message = "Added %s to challenge %s" % (item.name, challenge.name)
            print(message)
        elif action == 'remove':
            sql = "delete from items_itemgroupitem where item_id = %s and group_id = %s "
            with connection.cursor() as cursor:
                cursor.execute(sql, [id, challengeid])
            message = "Removed %s from challenge %s" % (item.name, challenge.name)
            print(message)
    except Exception as e:
        print(e)
        message = "Item or challenge doesn't exist"
    return HttpResponse(message)

@login_required
def set_as_peak_thumbnail(request):
    if request.is_ajax:
        get = request.GET
        peak_id = get.get('peak_id')
        photo_id = get.get('photo_id')
        checked = get.get('checked')
        if checked == 'true':
            checked = True
        else:
            checked = False

        item = Item.objects.get(id=peak_id)
        photo = ItemPhoto.objects.get(id=photo_id)

        if checked:
            item.thumbnail = photo.image
            try:
                category = ItemPhotoCategory.objects.get(name='peak')
                photo.category = category
                photo.save()
            except:
                pass
            item.save()
            return HttpResponse("1")
        else:
            item.thumbnail = ''
            item.save()
            return HttpResponse("2")

    return Http404


from peakery.tempitems.models import STATUS_PENDING
@login_required
def item_add_1(request):
    response = {'status':False , 'id':-1, 'step2': False}
    if request.POST:
        form = ItemAddForm(request.POST)
        if form.is_valid():
            action = form.cleaned_data['duplicate']
            form.cleaned_data['elevation'] = form.elevation_ft.ft
            data = form.cleaned_data
            itemstatus = STATUS_PENDING
            data['user'] = request.user
            data['status'] = itemstatus
            del data['elevationMeasure']

            if action not in ['add','correct']:
                name_list = data['name'].split(" ")

                filter = Q(name__icontains = name_list[0])
                for name in name_list[1:]:
                    filter = filter | Q(name__icontains = name)
                query = Item.objects.filter(filter)

                location = Point(data['long'],data['lat'])
                distance_limit = D(mi=2)
                query = query.filter(location__distance_lte = (location,distance_limit))

                if query and query.count() == 1:
                    return render(request, 'items/add_peak_form.html',{'form':form,'duplicate':query[0]})
                else:
                    action = "add"
            if action == "add":
                del data['duplicate']
                del data['prominenceMeasure']

                item = TempItem(**data)
                item.save()
                response['status'] = True
                response['id'] = item.pk
                if show_step2:
                    response['step2'] = True
                if not show_step2:
                    return render(request, 'items/thanks_add_peak.html',{})
                else:
                    newrequest = request
                    newrequestGET = request.GET.copy()
                    newrequest.GET = newrequestGET
                    newrequest.GET['id'] = item.id
                    return item_add_resolve_region(newrequest)
            if action == "correct":

                name_list = data['name'].split(" ")
                filter = Q(name__icontains = name_list[0])
                for name in name_list[1:]:
                    filter = filter | Q(name__icontains = name)
                query = Item.objects.filter(filter)
                location = Point(data['long'],data['lat'])
                distance_limit = D(mi=2)
                query = query.filter(location__distance_lte = (location,distance_limit))
                if query and len(query) == 1:
                    item = query[0]
                else:
                    item = None
                fields = {'coords':['lat','long'],'range':['range'],'elevation':['elevation']}
                if item:
                    for field in fields.keys():
                        if field in ['coords']:
                            original_value = '%s,%s' % (item.lat,item.long)
                            new_value = '%s,%s' % (location.y,location.x)
                            itemcorrection = ItemCorrection(user=request.user,item = item,field = field,
                                                            original_value = original_value,new_value = new_value,
                                                            location = location
                            )
                            itemcorrection.save()
                        else:
                            if field in ['range']:
                                itemcorrection = ItemCorrection(user=request.user,item = item,field = field,
                                                                original_value = item.range,new_value = data['range'],location=None)
                            if field in ['elevation']:
                                itemcorrection = ItemCorrection(user=request.user,item = item,field = field,
                                                                original_value = item.range,new_value = data['elevation'],location=None)

                            itemcorrection.save()
                return render(request, 'items/thanks_add_peak.html',{})

    else:
        form = ItemAddForm()
    return render(request, 'items/add_peak_form.html',{'form':form,'duplicate':None})


@login_required
def item_add_resolve_region(request):
    id = request.GET.get("id",False)
    name = ""
    form = ItemResolveRegionForm()
    if id:
        id = int(id)
        itemtemp = TempItem.objects.get(pk=id)
        name = itemtemp.name
    return render(request, 'items/resolve_region.html',{'name':name,'form':form,'id':id})


@login_required
def item_add_resolve_region_with_id(request,peak_id):
    response = {'status':True}
    if request.POST:
        form = ItemResolveRegionForm1(request.POST)
        if form.is_valid():
            tempitem = TempItem.objects.get(id = peak_id)
            tempitem.country.add(form.cleaned_data['countries'])
            tempitem.region.add(form.cleaned_data['regions'])
    else:
        pass
    return HttpResponse(json.dumps(response))

@login_required
@render_to_json()
def item_edit_correction_alternate_names(request):
    """
    Service to add alternate names in peak edit lightbox before save the others
    """
    if request.method == 'POST':
        data = {}
        success = False
        success_message = ""
        error = True
        error_message = ""
        if "peak_id" in request.POST and "alternate_names" in request.POST:
            peak_id = int(request.POST['peak_id'])
            alternate_names = request.POST['alternate_names']
            if not alternate_names == '0':
                item = Item.objects.get(pk=peak_id)
                existent_alternate_names = AlternateName.objects.filter(item=item).all()
                if '|' in alternate_names:
                    alternate_names = alternate_names.split('|')
                    for name in alternate_names:
                        exist = existent_alternate_names.filter(name__icontains=name)
                        if not exist:
                            itemcorrection = ItemCorrection(user=request.user,item = item, field=5)
                            itemcorrection.new_value = name
                            itemcorrection.save()
                            alternate_name = AlternateNameItemCorrection()
                            alternate_name.name = name
                            alternate_name.item = itemcorrection
                            alternate_name.save()
                            success_message = 'Successfully added alternate names'
                        else:
                            error_message = "Some names you wrote are already exist"
                else:
                    exist = existent_alternate_names.filter(name__icontains=alternate_names)
                    if not exist:
                        itemcorrection = ItemCorrection(user=request.user,item = item, field=5)
                        itemcorrection.new_value = alternate_names
                        itemcorrection.save()

                        alternate_name = AlternateNameItemCorrection()
                        alternate_name.name = alternate_names
                        alternate_name.item = itemcorrection
                        alternate_name.save()

                        success_message = 'Successfully added alternate name'
                    else:
                        error_message = "The name you wrote already exist"
                success = True
                error = False
            else:
            # Success true but not alternate names added, It's because we need to continue the process and alternate names aren't required fields
                success = True
                success_message = 'Not alternate names added'
                error = False
        data['success'] = success
        data['success_message'] = success_message
        data['error'] = error
        data['error_message'] = error_message
        return data
    if request.method == 'GET':
        data = {}
        success = False
        success_message = ""
        error = True
        error_message = ""
        if 'counter' in request.GET:
            counter = request.GET.get('counter')
            data['element_id'] = 'alternate-name-'+counter
            success = True
            error = False
        data['success'] = success
        data['success_message'] = success_message
        data['error'] = error
        data['error_message'] = error_message
        return data


def item_add_thanks(request):

    return render(request, 'items/thanks_add_peak.html',{})
"""""""""""""""""""""""""""
"
"   HERE STARTS ADMIN ITEM STUFF
"
"""""""""""""""""""""""""""

from peakery.items.models import STATUS_NEW,STATUS_APPROVED,STATUS_REJECTED,FIELDS_ELEVATION,FIELDS_COORDS,FIELDS_RANGE,FIELDS_PROMINENCE,FIELDS_ALTERNATE_NAMES,FIELDS_NAME,FIELDS_DELETE,FIELDS_OTHER

@staff_member_required
def approve_item_correction(request,correction_id = None, return_http_response=True):
    redirect = reverse('admin:items_itemcorrection_changelist') + "?status__exact=1"
    # We must apply the field
    correction = ItemCorrection.objects.get(pk=correction_id)
    if  correction.status != STATUS_APPROVED:
        item = correction.item
        field = correction.get_field_display().strip().lower()
        if field in ['coords']:
            new_value = correction.location
            item.fix_fields_with(field=field,value = new_value)
        elif field in ['alternate names']:
            alternate_name_correction = AlternateNameItemCorrection.objects.filter(item = correction)
            if alternate_name_correction:
                alternate_name_correction = alternate_name_correction[0]
                alternate_name = AlternateName()
                alternate_name.name = alternate_name_correction.name
                alternate_name.item = item
                alternate_name.save()
                #item.save()
        elif field in ['country']:
            country_correction = CountryItemCorrection.objects.filter(item = correction)
            if country_correction:
                country_correction = country_correction[0]
                item_country = ItemCountry()
                item_country.country = country_correction.country
                item_country.item = item
                item_country.save()
        elif field in ['region']:
            region_correction = RegionItemCorrection.objects.filter(item = correction)
            if region_correction:
                region_correction = region_correction[0]
                item_region = ItemRegion()
                item_region.region = region_correction.region
                item_region.item = item
                item_region.save()
        elif field in ['photo']:
            item.thumbnail = correction.new_photo.image
            item.thumbnail_credit = correction.user.username
            item.quick_save()
        else:
            new_value = correction.new_value
            item.fix_fields_with(field=field,value = new_value)
        correction.status = STATUS_APPROVED
        correction.decision_date = datetime.datetime.now()
        correction.save()
        #set peak slug
        item.set_peak_slug()
        # Removed by Ticket 824: admin: when an admin user approves Item Corrections, dont send the user emails
        correction.send_approved_email()
    #messages.add_message(request, messages.ERROR, 'Hello world.')
    if return_http_response:
        return HttpResponseRedirect(redirect)
    else:
        return True


@staff_member_required
def item_correction_add_photo_only(request,correction_id = None, return_http_response=True):
    redirect = reverse('admin:items_itemcorrection_changelist') + "?status__exact=1"
    # We must apply the field
    correction = ItemCorrection.objects.get(pk=correction_id)
    if correction.status != STATUS_APPROVED:
        item = correction.item
        field = correction.get_field_display().strip().lower()
        correction.status = STATUS_APPROVED
        correction.decision_date = datetime.datetime.now()
        correction.save()
        # Removed by Ticket 824: admin: when an admin user approves Item Corrections, dont send the user emails
        correction.send_approved_email()
    #messages.add_message(request, messages.ERROR, 'Hello world.')
    if return_http_response:
        return HttpResponseRedirect(redirect)
    else:
        return True


@staff_member_required
def reject_item_correction(request,correction_id = None, return_http_response=True):
    redirect = reverse('admin:items_itemcorrection_changelist') + "?status__exact=1"
    correction = ItemCorrection.objects.get(pk=correction_id)
    field = correction.get_field_display().strip().lower()
    correction.status = STATUS_REJECTED
    correction.decision_date = datetime.datetime.now()
    correction.save()
    if correction.reject_text.all().count() > 0:
        correction.send_rejected_email()

    #item = correction.item
    #item.save()

    if field in ['delete']:
        item = correction.item
        item.active = True
        item.quick_save()

    if return_http_response:
        return HttpResponseRedirect(redirect)
    else:
        return True


@staff_member_required
def admin_item_correction(request,correction_id = None):
    itemcorrection = ItemCorrection.objects.get(pk=correction_id)
    redirect = reverse('admin:items_item_change',args = (itemcorrection.item.id,))
    return HttpResponseRedirect(redirect)


@staff_member_required
def view_item_correction(request,correction_id = None):
    itemcorrection = ItemCorrection.objects.get(pk=correction_id)
    redirect = itemcorrection.item.get_absolute_url()
    return HttpResponseRedirect(redirect)


@staff_member_required
def approve_item_duplicate(request,duplicate_id = None):
    itemduplicate = ItemDuplicate.objects.get(pk=duplicate_id)
    itemduplicate.resolve_duplicate(user=request.user)
    redirect = reverse("admin:items_itemduplicate_changelist")
    return HttpResponseRedirect(redirect)


@staff_member_required
def admin_set_item_lists(request, item_id):
    item = Item.objects.get(id=item_id)
    if request.method == 'POST':
        #item.save()
        peak_lists_ids = request.POST.getlist('peak_lists')
        lists = ItemGroup.objects.filter(id__in=peak_lists_ids)

        #only delete unchecked groups, not deleting all preserve the order
        item_lists = item.groups.all()
        for group in item_lists:
            if group not in lists:
                item_group_item = ItemGroupItem.objects.get(item=item, group=group)
                item_group_item.delete()

        #only insert non existing item groups items
        for g in lists:
            if g not in item_lists:
                ig = ItemGroupItem(item=item, group=g)
                ig.save()
        messages.success(request, "Saved %s lists to this item" % lists.count())
        return HttpResponseRedirect(item.get_absolute_url())
    raise Http404


@login_required
def fetch_auto_suggest_users(request):
    exclude_list = request.GET.get('users', None)

    if exclude_list:
        exclude_list = exclude_list.replace('|', '')
        exclude_list =  exclude_list.split(',')

    if request.user.id:
        query = request.GET.get('q', None)

        if query:
            if exclude_list:
                urs = UserRelation.objects.filter(Q(from_user=request.user, to_user__username__icontains=query) | Q(from_user=request.user, first_name__icontains=query) | Q(from_user=request.user, last_name__icontains=query)).order_by('to_user__username', 'first_name', 'last_name').exclude(id__in=exclude_list[:-1])
            else:
                urs = UserRelation.objects.filter(Q(from_user=request.user, to_user__username__icontains=query) | Q(from_user=request.user, first_name__icontains=query) | Q(from_user=request.user, last_name__icontains=query)).order_by('to_user__username', 'first_name', 'last_name')

            if urs:
                user_list = ""

                for ur in urs:
                    try:
                        if ur.source == PEAKERY_SOURCE:
                            user_list += "%s|%s|%s|%d\n" % (ur.to_user.username, str(ur.to_user.first_name) + " " + str(ur.to_user.last_name), ur.to_user.person.avatar_url(), ur.id)
                        elif ur.source == GMAIL_SOURCE:
                            user_list += "%s|%s|%s|%d\n" % (ur.email, ur.first_name, settings.GMAIL_AVATAR, ur.id)
                        elif ur.source == INPUT_SOURCE:
                            user_list += "%s|%s|%s|%d\n" % (ur.email, ur.first_name, settings.INPUT_AVATAR, ur.id)
                    except:
                        raise

                return HttpResponse(user_list)

    return HttpResponse()

@login_required
def fetch_user_info(request):
    if request.user.id:
        rid = request.GET.get('rid', None)
        if rid:
            ur = UserRelation.objects.get(id=rid)
            return HttpResponse(ur.get_relation_box())
    return HttpResponse()


def create_user_relation_by_input(request):
    if request.method == 'POST':
        if request.user.id:
            name = request.GET.get('name', None)
            email = request.GET.get('email', None)

            if name and email:
                ur = UserRelation.check_relation(request.user, None, email, None)

                if not ur:
                    UserRelation(from_user=request.user, to_user=None, first_name=name, email_db=email, source=INPUT_SOURCE).save()
                    ur = UserRelation.check_relation(request.user, None, email, None)

                    response = {}
                    response['id'] = ur.id
                    response['template'] = ur.get_relation_box()

                    return HttpResponse(json.dumps(response))
                else:
                    return HttpResponse()

    return HttpResponse()

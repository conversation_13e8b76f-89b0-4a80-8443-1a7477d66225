{% extends "base.html" %}
{% load static %}
{% block extrajs %}
<script src="{% static 'js/jquery.cycle.lite.js' %}"></script>
{% endblock %}
{% load item_tags directory_tags %}
{% block title %}{{ group.name }}{% endblock %}
{% block titlemeta_overwrite %}{{ group.name }}{% endblock %}
{% block description %}{{ group.description }}{% endblock %}

{% block directory_active %}active{% endblock %}

{% block fixed_page_header %}
    <div class="row main-header-row hidden-xs">
        <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 65px; border-radius: 12px 12px 0 0;"><span style="font-size: 24px; font-weight: 600;">
            <div id="breadcrumbs">
                <ul>
                    <li><h1 class="regions-title">{{ group.name }}{% if request.user.is_superuser %}&nbsp;&nbsp;<a target="_blank" href="/admin/items/itemgroup/{{ group.id }}/" class="admin-edit-elevation desktop-admin-link hidden-xs hidden-sm hidden-md" style="color: #f24100; font-size: 10px; font-weight: 500;">admin</a>{% endif %}</h1></li>
                </ul>
                <div class="pull-right hidden-xs" style="margin-top: -5px;">
                    <a style="{{ subnav_info_style }}" class="region-header-sub-links" href="/challenges/{{ group.slug }}/">Info</a><a class="region-header-sub-links" href="/challenges/{{ group.slug }}/map/">Map</a><a class="region-header-sub-links" href="/challenges/{{ group.slug }}/peaks/">Peaks</a><a class="region-header-sub-links" href="/challenges/{{ group.slug }}/summits/">Climbs</a><a class="region-header-sub-links" href="/challenges/{{ group.slug }}/members/">Members</a>
                </div>
            </div>
        </span></div>
    </div>
{% endblock %}

{% block content %}

<style>

    .challenge-description > a {
        display: inline-flex;
        width: 100%;
        overflow: auto;
    }

   @media screen and (max-width: 767px) and (min-width: 1px) {
       #content-body {
           margin-top: 20px;
       }
       .content-pane {
           margin-top: 50px;
           background-color:#ffffff;
       }
       .stats-data-bottom, .stats-data-highlight, .stats-giant-red, .stats-giant-blue {
            margin-left: -6px;
        }
        .stats-data-highlight {
            margin-bottom: 5px;
            line-height: 20px;
        }
        .stats-data {
            margin-bottom: 5px;
        }
        .stats-header {
           font-size: 14px;
           margin-bottom: 10px;
       }
   }
   @media screen and (max-width: 1023px) and (min-width: 768px) {
        .stats-data {
            margin-bottom: 10px;
        }
        .stats-giant-red, .stats-giant-blue {
            margin-left: -6px;
        }
    }
    @media screen and (min-width: 1024px) {
        .stats-data {
            margin-bottom: 10px;
        }
    }
    @media screen and (min-width: 768px) {
        .content-pane {
           margin-top: 0px;
           background-color:#ffffff;
       }
    }

    .hover-cell:hover, .table-hover-cell:hover {
        background-image: linear-gradient(to bottom,#fde1d6,#fde1d6) !important;
    }

    @media screen and (max-width: 767px) and (min-width: 1px) {
        .featured-logs-thumbnail {
            display: none;
        }
    }

    @media screen and (min-width: 768px) {
        .featured-logs-thumbnail {
            float: left;
            width: 100px;
        }
        .featured-logs-description {
            margin-left: 120px;
        }
    }

</style>

<div class="container">
    <!-- Mobile header -->
    <div class="row sub-header-row hidden-lg hidden-md hidden-sm" style="height: 50px; position: fixed; width: 100%; z-index: 102;">
        <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999; display: flex; justify-content: space-between;">
            <a style="{{ subnav_info_style }}" class="mobile-header-sub-links" href="/challenges/{{ group.slug }}/">Info</a><a class="mobile-header-sub-links" href="/challenges/{{ group.slug }}/map/">Map</a><a class="mobile-header-sub-links" href="/challenges/{{ group.slug }}/peaks/">Peaks</a><a class="mobile-header-sub-links" href="/challenges/{{ group.slug }}/summits/">Summits</a><a class="mobile-header-sub-links" href="/challenges/{{ group.slug }}/members/">Members</a>
        </div>
    </div>
    <!-- End mobile header -->

    <div class="row content-pane">

        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12" style="cursor: pointer; padding-right: 0px; padding-left: 0px; border: solid 1px #e0e0e0; border-left: none; border-top: none; border-bottom: solid 1px #e0e0e0;">
        <div class="top-photos peakimglrg-responsive">
          <div class="hover-photos">
              <div class="hero-slideshow challenge-slideshow" id="slideshow1">
                  {% if group.thumbnail %}
                      <div style="width: 100%; height: 100%; cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ group.get_thumbnail_910 }}'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;"></div>
                      {% for p in top_peaks|slice:"4:8" %}
                        <div onclick="openUrl('/{{ p.slug_new_text }}/');" style="width: 100%; height: 100%; cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ p.get_thumbnail_910 }}'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;">
                            <div id="hero-user-photo-info{{ forloop.counter }}" class="hero-user-photo-info" style="z-index: 99;">
                            <span class="data hero-photo-caption-peakname" style="position: absolute; bottom: 0px; left: 10px; color: #fff;">
                            <p class="bagger ellipsis" style="font-size: 14px;">
                            <a href="/{{ p.slug_new_text }}" style="color: #fff;">{{ p.name }}</a>
                            </p>
                            </span>
                            </div>
                        </div>
                      {% endfor %}
                  {% else %}
                      {% for p in top_peaks|slice:"0:1" %}
                        <div onclick="openUrl('/{{ p.slug_new_text }}/');" style="width: 100%; height: 100%; cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ p.get_thumbnail_910 }}'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;">
                            <div id="hero-user-photo-info{{ forloop.counter0 }}" class="hero-user-photo-info" style="z-index: 99;">
                            <span class="data hero-photo-caption-peakname" style="position: absolute; bottom: 0px; left: 10px; color: #fff;">
                            <p class="bagger ellipsis" style="font-size: 14px;">
                            <a href="/{{ p.slug_new_text }}" style="color: #fff;">{{ p.name }}</a>
                            </p>
                            </span>
                            </div>
                        </div>
                      {% endfor %}
                      {% for p in top_peaks|slice:"5:9" %}
                        <div onclick="openUrl('/{{ p.slug_new_text }}/');" style="width: 100%; height: 100%; cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ p.get_thumbnail_910 }}'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;">
                            <div id="hero-user-photo-info{{ forloop.counter }}" class="hero-user-photo-info" style="z-index: 99;">
                            <span class="data hero-photo-caption-peakname" style="position: absolute; bottom: 0px; left: 10px; color: #fff;">
                            <p class="bagger ellipsis" style="font-size: 14px;">
                            <a href="/{{ p.slug_new_text }}" style="color: #fff;">{{ p.name }}</a>
                            </p>
                            </span>
                            </div>
                        </div>
                      {% endfor %}
                  {% endif %}
              </div>
          </div>
        </div>
        </div>

        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12" style="padding-right: 0px; padding-left: 0px; background-size: cover;">
            <div class="top-photos peakimglrg-responsive" style="background-color: #fff;">
                <div onclick="openUrl('/challenges/{{ group.slug }}/peaks/');" class="col-lg-4 col-md-4 col-sm-4 col-xs-4 hover-cell" style="cursor: pointer; border-right: solid 1px #e0e0e0; border-bottom: solid 1px #e0e0e0;">
                    <p>&nbsp;</p>
                    <p><span class="stats-header">Peaks</span></p>
                    <p>&nbsp;</p>
                    <p><a href="/challenges/{{ group.slug }}/peaks/"><span class="stats-giant-red" style="color: #F24100;">{{ group_items_count }}</span></a></p>
                    <p>&nbsp;</p>
                </div>
                <div onclick="openUrl('/challenges/{{ group.slug }}/members/#type=pursuers');" class="col-lg-4 col-md-4 col-sm-4 col-xs-4 hover-cell" style="cursor: pointer; border-right: solid 1px #e0e0e0; border-bottom: solid 1px #e0e0e0;">
                    <p>&nbsp;</p>
                    <p><span class="stats-header">Pursuers</span></p>
                    <p>&nbsp;</p>
                    <p><a href="/challenges/{{ group.slug }}/members/#type=pursuers"><span class="stats-giant-blue">{{ pursuers_count }}</span></a></p>
                    <p>&nbsp;</p>
                </div>
                <div onclick="openUrl('/challenges/{{ group.slug }}/members/#type=finishers');" class="col-lg-4 col-md-4 col-sm-4 col-xs-4 hover-cell" style="cursor: pointer; border-bottom: solid 1px #e0e0e0;">
                    <p>&nbsp;</p>
                    <p><span class="stats-header">Finishers</span></p>
                    <p>&nbsp;</p>
                    <p><a href="/challenges/{{ group.slug }}/members/#type=finishers"><span class="stats-giant-blue">{{ finishers_count }}</span></a></p>
                    <p>&nbsp;</p>
                </div>
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="background-color: #fff;">
                    <p>&nbsp;</p>
                    <p class="challenge-description" style="line-height: 26px;">{{ group.description|default:""|urlize|url_target_blank|linebreaksbr }}</p>
                    <p>&nbsp;</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        {% if group.thumbnail %}
            {% for p in top_peaks|slice:"0:1" %}
                <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 hover-photos" onclick="openUrl('/{{ p.slug_new_text }}/');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ p.get_thumbnail_745 }}'); overflow: hidden; background-size: cover; background-position: center top; background-repeat: no-repeat; border-right: solid 1px #fff;">
                    <div>
                        <a href="/{{ p.slug_new_text }}/"><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></a>
                        <div class="user-photo-info">
                            <span class="photo-caption-peakname-only" style="position: absolute; bottom: 5px; left: 10px; color: #fff;">
                                <div class="bagger ellipsis"><a href="/{{ p.slug_new_text }}/" style="color: #fff;">{{ p.name }}</a></div>
                            </span>
                        </div>
                    </div>
                </div>
            {% endfor %}
        {% endif %}
        {% for p in top_peaks|slice:"1:2" %}
            <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 hover-photos" onclick="openUrl('/{{ p.slug_new_text }}/');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ p.get_thumbnail_745 }}'); overflow: hidden; background-size: cover; background-position: center top; background-repeat: no-repeat; border-right: solid 1px #fff;">
                <div>
                    <a href="/{{ p.slug_new_text }}/"><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></a>
                    <div class="user-photo-info">
                        <span class="photo-caption-peakname-only" style="position: absolute; bottom: 5px; left: 10px; color: #fff;">
                            <div class="bagger ellipsis"><a href="/{{ p.slug_new_text }}/" style="color: #fff;">{{ p.name }}</a></div>
                        </span>
                    </div>
                </div>
            </div>
        {% endfor %}
        {% for p in top_peaks|slice:"2:3" %}
            <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 hover-photos" onclick="openUrl('/{{ p.slug_new_text }}/');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ p.get_thumbnail_745 }}'); overflow: hidden; background-size: cover; background-position: center top; background-repeat: no-repeat; ">
                <div>
                    <a href="/{{ p.slug_new_text }}"><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></a>
                    <div class="user-photo-info">
                        <span class="photo-caption-peakname-only" style="position: absolute; bottom: 5px; left: 10px; color: #fff;">
                            <div class="bagger ellipsis"><a href="/{{ p.slug_new_text }}/" style="color: #fff;">{{ p.name }}</a></div>
                        </span>
                    </div>
                </div>
            </div>
        {% endfor %}
        {% for p in top_peaks|slice:"3:4" %}
            <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 hidden-sm hover-photos" onclick="openUrl('/{{ p.slug_new_text }}/');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ p.get_thumbnail_745 }}'); overflow: hidden; background-size: cover; background-position: center top; background-repeat: no-repeat; border-left: solid 1px #fff;">
                <div>
                    <a href="/{{ p.slug_new_text }}"><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></a>
                    <div class="user-photo-info">
                        <span class="photo-caption-peakname-only" style="position: absolute; bottom: 5px; left: 10px; color: #fff;">
                            <div class="bagger ellipsis"><a href="/{{ p.slug_new_text }}/" style="color: #fff;">{{ p.name }}</a></div>
                        </span>
                    </div>
                </div>
            </div>
        {% endfor %}
        {% if not group.thumbnail %}
            {% for p in top_peaks|slice:"4:5" %}
                <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 hidden-sm hover-photos" onclick="openUrl('/{{ p.slug_new_text }}/');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ p.get_thumbnail_745 }}'); overflow: hidden; background-size: cover; background-position: center top; background-repeat: no-repeat; border-left: solid 1px #fff;">
                    <div>
                        <a href="/{{ p.slug_new_text }}"><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></a>
                        <div class="user-photo-info">
                            <span class="photo-caption-peakname-only" style="position: absolute; bottom: 5px; left: 10px; color: #fff;">
                                <div class="bagger ellipsis"><a href="/{{ p.slug_new_text }}/" style="color: #fff;">{{ p.name }}</a></div>
                            </span>
                        </div>
                    </div>
                </div>
            {% endfor %}
        {% endif %}
    </div>

    <div class="row">
        {% if highest_peak %}
        <div onclick="openUrl('/{{ highest_peak.slug }}/');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive stat-box hover-cell" style="max-height: 200px; cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="stats-header">Highest peak</div>
                <p class="stats-data-highlight">{{ highest_peak.name }}</p>
                <p class="stats-data">{{ highest_peak_elevation }} ft / {{ highest_peak_elevation_in_meters }} m</p>
            </div>
        </div>
        {% else %}
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive stat-box hover-cell" style="max-height: 200px; cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="stats-header">Highest peak</div>
                <p class="stats-data-highlight">{{ highest_peak.name }}</p>
                <p class="stats-data">{{ highest_peak_elevation }} ft / {{ highest_peak_elevation_in_meters }} m</p>
            </div>
        </div>
        {% endif %}
        {% if most_prominent_peak %}
        <div onclick="openUrl('/{{ most_prominent_peak.slug }}/');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive stat-box hover-cell" style="max-height: 200px; cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="stats-header">Most prominent peak</div>
                <p class="stats-data-highlight">{{ most_prominent_peak.name }}</p>
                <p class="stats-data">{{ most_prominent_peak_prominence }} ft / {{ most_prominent_peak_prominence_in_meters }} m <span class="hidden-xs">prom</span></p>
            </div>
        </div>
        {% else %}
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive stat-box hover-cell" style="max-height: 200px; cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="stats-header">Most prominent peak</div>
                <p class="stats-data-highlight">{{ most_prominent_peak.name }}</p>
                <p class="stats-data">{{ most_prominent_peak_prominence }} ft / {{ most_prominent_peak_prominence_in_meters }} m <span class="hidden-xs">prom</span></p>
            </div>
        </div>
        {% endif %}
        {% if most_summited_peak %}
        <div onclick="openUrl('/{{ most_summited_peak.slug }}/');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive stat-box hover-cell" style="max-height: 200px; cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="stats-header">Most climbed peak</div>
                <p class="stats-data-highlight">{{ most_summited_peak.name }}</p>
                <p class="stats-data">{{ most_summited_peak_summits }} climbs</p>
            </div>
        </div>
        {% else %}
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive stat-box hover-cell" style="max-height: 200px; cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="stats-header">Most climbed peak</div>
                <p class="stats-data-highlight">{{ most_summited_peak.name }}</p>
                <p class="stats-data">{{ most_summited_peak_summits }} climbs</p>
            </div>
        </div>
        {% endif %}
        {% if most_difficult_peak %}
        <div onclick="openUrl('/{{ most_difficult_peak.slug }}/');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive stat-box hover-cell" style="max-height: 200px; cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="stats-header">Most difficult peak</div>
                {% if most_difficult_peak %}
                    <p class="stats-data-highlight">{{ most_difficult_peak.name }}</p>
                    <p class="stats-data">{{ most_difficult_peak.least_difficult_route }}</p>
                {% else %}
                    <p class="stats-data-missing">no info yet</p>
                {% endif %}
            </div>
        </div>
        {% else %}
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive stat-box hover-cell" style="max-height: 200px; cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="stats-header">Most difficult peak</div>
                {% if most_difficult_peak %}
                    <p class="stats-data-highlight">{{ most_difficult_peak.name }}</p>
                    <p class="stats-data">{{ most_difficult_peak.least_difficult_route }}</p>
                {% else %}
                    <p class="stats-data-missing">no info yet</p>
                {% endif %}
            </div>
        </div>
        {% endif %}
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive stat-box" style="max-height: 200px; background-color: #fff; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="stats-header">Difficulty breakdown</div>
                {% if class_one_two_peaks or class_three_four_peaks or class_five_peaks %}
                    {% if class_one_two_peaks %}
                        <p class="stats-data">Class 1/2 <span style="margin-left: 20px; font-size: 14px; font-weight: 300; color: #666;">{{ class_one_two_peaks }} peak{{ class_one_two_peaks|pluralize:"s" }}</span></p>
                    {% endif %}
                    {% if class_three_four_peaks %}
                        <p class="stats-data">Class 3/4 <span style="margin-left: 20px; font-size: 14px; font-weight: 300; color: #666;">{{ class_three_four_peaks }} peak{{ class_three_four_peaks|pluralize:"s" }}</span></p>
                    {% endif %}
                    {% if class_five_peaks %}
                        <p class="stats-data">Class 5+ <span style="margin-left: 20px; font-size: 14px; font-weight: 300; color: #666;">{{ class_five_peaks }} peak{{ class_five_peaks|pluralize:"s" }}</span></p>
                    {% endif %}
                {% else %}
                    <p class="stats-data-missing">no info yet</p>
                {% endif %}
            </div>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive stat-box" style="max-height: 200px; background-color: #fff; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="stats-header">Top climbing months</div>
                {% for m in top_three_months %}
                    <p class="stats-data"><a href="/challenges/{{ group.slug }}/summits/#month={{ m.month_number }}&member=all&route=&order=most_recent&page=1">{{ m.summitlog_month }}</a><span style="font-size: 16px; color: #999;">&nbsp;&nbsp;{{ m.pct_total }}%</span></p>
                {% endfor %}
            </div>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hidden-sm stat-box" style="max-height: 200px; background-color: #fff; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="stats-header">Total distance{% if total_distance != '0' %}*{% endif %}</div>
                {% if total_distance != '0' %}
                    <p class="stats-data">{{ total_distance }} mi / {{ total_distance_in_km }} km</p>
                    <p class="stats-data"><span style="font-size: 10px; line-height: 20px; font-weight: 300; color: #666;">* includes {{ total_distance_peak_count }} of {{ group_items_count }} peak{{ group_items_count|pluralize:"s" }} with summits</span></p>
                {% else %}
                    <p class="stats-data-missing">no info yet</p>
                {% endif %}
            </div>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hidden-sm stat-box" style="max-height: 200px; background-color: #fff; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="stats-header">Total vertical gain{% if elevation_gain != '0' %}*{% endif %}</div>
                {% if elevation_gain != '0' %}
                    <p class="stats-data">{{ elevation_gain }} ft / {{ elevation_gain_in_meters }} m</p>
                    <p class="stats-data"><span style="font-size: 10px; line-height: 20px; font-weight: 300; color: #666;">* includes {{ elevation_gain_peak_count }} of {{ group_items_count }} peak{{ items|pluralize:"s" }} with summits</span></p>
                {% else %}
                    <p class="stats-data-missing">no info yet</p>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="row dark-background-row">
        <div class="sp-60"></div>
    </div>

    <div class="row" id="highlights-header">
        <div class="col-md-12 region-info-section-header" style="height: 70px; background-color: #fff; line-height: 70px; font-weight: 500;">
            <div style="float: left;">
                Highlights
            </div>
            <div id="edit-highlights-link-div" class="pull-right">
                {% if user.is_authenticated %}
                    {% if highlights %}
                        <a id="edit-highlights-link" style="cursor: pointer; color: #00B1F2; font-weight: 500; font-size: 14px;">edit highlights</a>
                    {% else %}
                        <a id="edit-highlights-link" style="cursor: pointer; color: #00b1f2; font-weight: 500; font-size: 14px;">add a highlight!</a>
                    {% endif %}
                {% else %}
                    {% if highlights %}
                        <a data-toggle="modal" data-target="#accounts-login" style="cursor: pointer; color: #00B1F2; font-weight: 500; font-size: 14px;">edit highlights</a>
                    {% else %}
                        <a data-toggle="modal" data-target="#accounts-login" style="cursor: pointer; color: #00b1f2; font-weight: 500; font-size: 14px;">add a highlight!</a>
                    {% endif %}
                {% endif %}
            </div>
        </div>
    </div>
    <div class="row" id="highlights-content" style="{% if not highlights %}display: none; {% endif %}background-color: #fff; border-top: 1px solid #c0c0c0; padding-top: 20px; padding-left: 8px;">
        <div class="col-md-12">
            <div class="description highlights-info-content">
                <ul id="highlights-list">
                {% if highlights %}
                    {% for h in highlights %}
                        <li style="list-style: initial; margin-bottom: 20px; font-size: 16px; line-height: 28px;">{{ h.highlight }}</li>
                    {% endfor %}
                {% endif %}
                </ul>
            </div>
        </div>
    </div>
    <div class="row" id="edit-highlights-header" style="display: none;">
        <div class="col-md-12 region-info-section-header" style="height: 70px; background-color: #feece5; line-height: 70px; font-weight: 500;">
            <div style="float: left;">
                <span style="color: #f24100;">{{ group.name }} highlights</span>
            </div>
        </div>
    </div>
    <div class="row" id="edit-highlights-form" style="display: none; background-color: #feece5; border-top: 1px solid #c0c0c0; padding-top: 20px; padding-left: 10px;">
        <div class="col-md-12">
            <div class="description highlights-info-content" style="margin-left: -10px;">
                <form id="edithighlights_form" method="POST" action="/challenges/edit_highlights/{{ group.id }}/">
                <fieldset id="peak-highlights-fieldset">
                {% if highlights %}
                    {% for h in highlights %}
                        <div>
                            <textarea class="peak-highlight-input" name="peak-highlight-{{ forloop.counter }}"  data-index="{{ forloop.counter }}" id="peak-highlight-{{ forloop.counter }}" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ group.name }}...">{{ h.highlight }}</textarea>
                        </div>
                        {% if forloop.last %}
                        <div>
                            <textarea class="peak-highlight-input" name="peak-highlight-{{ forloop.counter|add:1 }}" data-index="{{ forloop.counter|add:1 }}" id="peak-highlight-new" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ group.name }}..."></textarea>
                        </div>
                        {% endif %}
                    {% endfor %}
                {% else %}
                    <div>
                        <textarea class="peak-highlight-input" name="peak-highlight-1" data-index="1" id="peak-highlight-1" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ group.name }}..."></textarea>
                    </div>
                {% endif %}
                </fieldset>
                <button style="float: left; width: 170px; height: 50px; font-size: 16px; padding: 0 20px;" class="btn set2 input" id="edit-highlights-save" type="submit">Save highlights</button>&nbsp;<a style="float: left; margin-left: 30px; margin-top: 17px; cursor: pointer; color: #999;" id="edit-highlights-cancel">Cancel</a>
                </form>
            </div>
        </div>
    </div>

    {% if featured_logs %}

    <div class="row dark-background-row">
        <div class="sp-60"></div>
    </div>

    <div class="row" id="featured-logs-header">
        <div class="col-md-12 region-info-section-header" style="height: 70px; background-color: #fff; line-height: 70px; font-weight: 500;">
            <div style="float: left;">
                <span>Latest climbs</span>
            </div>
        </div>
    </div>

    <div class="row sub-header-row" style="background-color: #fff; border-top: 1px solid #c0c0c0;">
        <div class="col-md-12" style="padding-left: 0px; padding-right: 0px;">
            <div class="peak_desc" style="padding: 0px;">
                <div class="description" style="margin-bottom: 0px; padding-top: 0px;">
                    {% for l in featured_logs %}
                        <div class="hover-cell" onclick="openUrl('/{{ l.slug_new_text }}/summits/{{ l.id }}/');" style="display: inline-block; padding: 15px; cursor: pointer; background-image: linear-gradient(to bottom,#fff,#f6f6f6);">
                            <div class="featured-logs-thumbnail">
                                <img class="hover-photos" src="{{ MEDIA_URL }}{{ l.thumbnail_url }}" style="width: 100px;">
                            </div>
                            <div class="featured-logs-description">"{{ l.log_text }}" &mdash; <i><span style="color: #00b1f2; font-weight: 500;">{{ l.username }} &bull; {{ l.summitlog_date|date:"M j, Y" }}</span></i></div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    {% endif %}

    <div class="row dark-background-row">
        <div class="sp-60"></div>
    </div>

    <div class="row hidden-lg hidden-md hidden-sm">
        <div style="height: 179px;"></div>
    </div>
    <div class="row hidden-xs">
        <div style="height: 55px;"></div>
    </div>

</div>

<script type="text/javascript">

    var initial_highlights = [];
    {% for h in highlights %}
        initial_highlights.push('{{ h.highlight }}');
    {% endfor %}

    function openUrl(url) {
        window.location.href = url;
    }

    $(document).ready(function() {

        $('#slideshow1').cycle({
            fx: 'fade',
            timeout: 3000
        });

        //highlights stuff
        {% if user.is_authenticated %}
        $('#edit-highlights-link-div').on('click', 'a', function() {
            $('#highlights-header').hide();
            $('#highlights-content').hide();
            $('#edit-highlights-header').fadeIn(300);
            $('#edit-highlights-form').fadeIn(300, function () {
                autosize($('.peak-highlight-input'));
                autosize.update($('.peak-highlight-input'));
            });
            return false;
        });
        {% else %}
        $('#edit-highlights-link-div').on('click', 'a', function() {
            if ($('#navbar-login-link').is(':visible')) {
                $('#navbar-login-link').click();
            } else {
                //window.location.href = '/accounts/login/';
            }
            return false;
        });
        {% endif %}

        $('#edit-highlights-cancel').click(function(){
            $('#peak-highlights-fieldset').empty();
            var new_index = 1;
            var haveHighlights = false;
            for (var i = 0; i < initial_highlights.length; i++) {
                $('#peak-highlights-fieldset').append('<div><textarea class="peak-highlight-input" name="peak-highlight-' + new_index + '"  data-index="' + new_index + '" id="peak-highlight-' + new_index + '" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ group.name }}...">'+initial_highlights[i]+'</textarea></div>');
                haveHighlights = true;
                new_index++;
            }
            $('#peak-highlights-fieldset').append('<div><textarea class="peak-highlight-input" name="peak-highlight-' + new_index + '" data-index="' + new_index + '" id="peak-highlight-new" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ group.name }}..."></textarea></div>');
            $('#edit-highlights-header').hide();
            $('#edit-highlights-form').hide();
            $('#highlights-header').fadeIn(300);
            if (haveHighlights) {
                $('#highlights-content').fadeIn(300);
                $('#edit-highlights-link').html('edit highlights');
            } else {
                $('#highlights-content').fadeOut(300);
                $('#edit-highlights-link').html('add a highlight!');
            }
            return false;
        });

        $('#peak-highlights-fieldset').on('keyup', 'textarea', function() {
            var index = $(this).data('index');
            var num_fields = $('.peak-highlight-input').length;
            if (index == num_fields && $(this).val().length > 0) {
                var new_index = index + 1;
                if ($('#peak-highlight-' + new_index).length == 0) {
                    $('#peak-highlights-fieldset').append('<div><textarea class="peak-highlight-input" name="peak-highlight-' + new_index + '"  data-index="' + new_index + '" id="peak-highlight-' + new_index + '" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ group.name }}..."></textarea></div>');
                }
            }
            autosize($('.peak-highlight-input'));
            autosize.update($('.peak-highlight-input'));
            return false;
        });

        // process the form
        $('#edit-highlights-save').click(function(event) {

            var url = "/challenges/edit_highlights/{{ group.id }}/";
            $('#edit-highlights-save').html('<i class="fa fa-spinner fa-spin fa-fw"></i>');
            $('#edit-highlights-save').prop("disabled",true);

            $.ajax({
                type: "POST",
                url: url,
                data: $("#edithighlights_form").serialize(),
                success: function(data)
                {
                    //console.log(data);
                    //update page with new highlights
                    $('#peak-highlights-fieldset').empty();
                    $('#highlights-list').empty();
                    var haveHighlights = false;
                    $.each( data, function( key, val ) {
                        if (key=='highlights') {
                            new_index = 1;
                            initial_highlights = [];
                            $.each( val, function( highlightkey, highlightval ) {
                                //console.log(highlightval);
                                haveHighlights = true;
                                $('#peak-highlights-fieldset').append('<div><textarea class="peak-highlight-input" name="peak-highlight-' + new_index + '"  data-index="' + new_index + '" id="peak-highlight-' + new_index + '" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ group.name }}...">'+highlightval+'</textarea></div>');
                                $('#highlights-list').append('<li style="list-style: initial; margin-bottom: 20px;">'+highlightval+'</li>');
                                initial_highlights.push(highlightval);
                                new_index++;
                            });
                        }
                    });
                    $('#peak-highlights-fieldset').append('<div><textarea class="peak-highlight-input" name="peak-highlight-' + new_index + '" data-index="' + new_index + '" id="peak-highlight-new" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ group.name }}..."></textarea></div>');
                    $('#edit-highlights-link-div').html('<a id="edit-highlights-link" style="cursor: pointer; color: #00B1F2; font-weight: 500;">edit highlights</a>');
                    $('#edit-highlights-save').html('Save highlights');
                    $('#edit-highlights-save').prop("disabled",false);
                    $('#edit-highlights-header').hide();
                    $('#edit-highlights-form').hide();
                    $('#highlights-header').fadeIn(300);
                    if (haveHighlights) {
                        $('#highlights-content').fadeIn(300);
                        $('#edit-highlights-link').html('edit highlights');
                    } else {
                        $('#highlights-content').fadeOut(300);
                        $('#edit-highlights-link').html('add a highlight!');
                    }
                }
            });
            event.preventDefault();
        });

    });

</script>

{% endblock %}

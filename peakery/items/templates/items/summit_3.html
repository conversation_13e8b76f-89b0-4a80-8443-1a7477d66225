{% load thumbnail %}
<style>
    .ui-autocomplete {
        max-height: 100px;
        overflow-y: auto;
        /* prevent horizontal scrollbar */
        overflow-x: hidden;
        /* add padding to account for vertical scrollbar */
        padding-right: 20px;
    }
        /* IE 6 doesn't support max-height
       * we use height instead, but this forces the menu to always be this tall
       */
    * html .ui-autocomplete {
        height: 100px;
    }
</style>
<script type="text/javascript">
$(document).ready(function(){
    $(document).bind('close.facebox', function() {location.reload(true) });
    $('input[title!=""]').hint();
    $('a#step3').facebox();
    $('a#skip3').click(function(){ $("a#step3").trigger('click') });

//    $('select#route_up').click(function(){
//
//        $('select#route_up :first-child').toggle();
//    });

    $('select#route_up').change(function() {
        if($('select#route_up').val() == 'Or add a new route...') {
            $('div#uniform-route_up').remove();

            var input = '<input type="text" name="route_up" id="route_up" />';
            $('div.clearfix.routeUp span.a.arrowUp.onFocus').append(input);
            $('<span class="holder">Route you took up...</span>').insertAfter('input#route_up');
            $('#route_up').focus();
        }


    });

    $('select#route_down').change(function() {
        if($('select#route_down').val() == 'Same as route up') {
            var route_up_temp = $('#route_up').val();
            if (route_up_temp == undefined){
                var route_up_temp =$('select#route_up').val();
            }
            $('div#uniform-route_down').remove();
            var input = '<input type="text" name="route_down" id="route_down" val="' + route_up_temp + '" />';
            $('div.clearfix.routeDown span.a.arrowDown.onFocus').append(input);
            $('input#route_down').val(route_up_temp);
        }
        else if($('select#route_down').val() == 'Or add a new route...') {
            $('div#uniform-route_down').remove();

            var input = '<input type="text" name="route_down" id="route_down" />';
            $('div.clearfix.routeDown span.a.arrowDown.onFocus').append(input);
            $('<span class="holder">Route you took down...</span>').insertAfter('input#route_down');
            $('#route_down').focus();
        }
    });

    $('#form_peak3').submit(function() {

        if(!validate_mails()){return false;}

        $(this).ajaxSubmit({
            target: '#output',
            success:    function(e) {
                //fileuploader.js is causing the conflict:
                if(e=='True'){
                    $("a#step3").trigger('click');
                }else{
                    //error from django
                }
            }
        });
        return false;
    });

    $("input[id^='email-']").keydown(function(){
        var email_id = $(this).attr('id').split("-")[1];
        var email = $(this).val();
        if( isEmail(email) || email==""){
            $(this).next("span").hide();
        }
    });

    $("#add_peak3").click(function(){
        $("#form_peak3").submit();
        $("div#upload-ajax-spinner").show();
    });

    function resultsUsers(event, data, formatted) {
        $("input#auto_suggest_users").flushCache();

        $.get('{% url "fetch_user_info" %}?rid=' + data[3], function(response) {
            if(response != '') {
                $('div#selected_users ul#users').append(response);
                $("input#auto_suggest_users").val('');

                if($('input#fellow_selected_users').val() != '') {
                    $('input#fellow_selected_users').val($('input#fellow_selected_users').val() + '|' + data[3] + '|,');
                }
                else {
                    $('input#fellow_selected_users').val('|' + data[3] + '|,');
                }
            }
        });
    }

    $('a.remove-user').live('click', function() {
        $('li#' + $(this).attr('id')).remove();

        if($('input#fellow_selected_users').val() != '') {
            var hidden_input_value = $('input#fellow_selected_users').val();

            hidden_input_value = hidden_input_value.replace('|' + $(this).attr('id') + '|,', '');

            $('input#fellow_selected_users').val(hidden_input_value);
        }
    });

    //For users that doesn't have information stored on peakery
    $('a.remove-user-email').live('click', function() {
        $('li#' + $(this).attr('id')).remove();

        if($('input#fellow_selected_users').val() != '') {
            var hidden_input_value = $('input#fellow_selected_users').val();
            hidden_input_value = hidden_input_value.replace('|' + $(this).attr('id') + '|,', '');
            $('input#fellow_selected_users').val(hidden_input_value);
        }
    });

    $('li p.fullname input:text').live('keydown', function(event){
        if(event.keyCode == 13) {
            if(isEmail($(this).val())) {
                var id = $(this).attr('id');
                var name = $('div#' + id + ' p.username').html();
                var email = $('input#' + id).val();
                var vars = "?name=" + name + "&email=" + email;

                $.post('{% url "create_user_relation_by_input" %}' + vars, function(response) {
                    if(response != '') {
                        response = $.parseJSON(response);

                        $('li#' + id).remove();
                        $('div#selected_users ul#users').append(response.template);

                        if($('input#fellow_selected_users').val() != '') {
                            $('input#fellow_selected_users').val($('input#fellow_selected_users').val() + '|' + response.id + '|,');
                        }
                        else {
                            $('input#fellow_selected_users').val('|' + response.id + '|,');
                        }
                    }
                    else {
                        alert('The email is already used');
                    }
                });
            }
            else {
                alert('Invalid email: ' + $(this).val());
            }
        }
    });

    $("input#auto_suggest_users").keydown(function(event) {
        if(event.keyCode == 13) {
            if($('div.ac_results.ac_suggests_users').css('display') == 'none' ||  $('div.ac_results.ac_suggests_users').length == 0) {
                createEmailBox($("input#auto_suggest_users").val());
            }
        }
    });

    $("input#auto_suggest_users").autocomplete('{% url "fetch_auto_suggest_users" %}', {
        width: $("input#auto_suggest_users").width,
        highlight: false,
        scrollHeight: 300,
        cacheLength:0,
        max:5,
        scroll: true,
        resultsId: 'ac_suggests_users',
        formatItem: function(data, i, n, value) {
            return "<img src='" + data[2] + "' width='45' height='45' /> " + data[0] + "<br /> " + data[1];
        },
        formatResult: function(data, value) {
            return data[0];
        },
        extraParams: {
            users: function() { return $('input#fellow_selected_users').val(); }
        }
    });

    {% if not all_routes_up and not all_routes_down %}
        $('select#route_up').remove();
        $('select#route_down').remove();

        $('div#uniform-route_up').remove();

        var input = '<input type="text" name="route_up" id="route_up" />';
        $('div.clearfix.routeUp span.a.arrowUp.onFocus').append(input);
        $('<span class="holder">Route you took up...</span>').insertAfter('input#route_up');

        $('div#uniform-route_down').remove();

        var input = '<input type="text" name="route_down" id="route_down" />';
        $('div.clearfix.routeDown span.a.arrowDown.onFocus').append(input);
        $('<span class="holder">Route you took down...</span>').insertAfter('input#route_down');
    {% endif %}

    $("input#auto_suggest_users").result(resultsUsers);
});

{% if current_route_down or current_route_up %}
    $('select#route_up').val('{{ current_route_up|default:"" }}');
    $('select#route_down').val('{{ current_route_down|default:"" }}');
{% endif %}

function validate_mails(){
    var valid = true;
    $("input[id^='email-']").each(function(k,v){
        var input = $(v);
        var email = input.val();
        if( !isEmail(email) ){
            if(email!=""){
                $(v).next("span").show();
                valid = false;
            }
        }
    });
    return valid;
}

function createEmailBox(name) {
    var slug = slugify(name);

    var html = '<li id="' + slug + '" class="red">';
    html += "<div id='" + slug + "' class='user-lozenge'>";
    html += "<span>";
    html += "<p class='username'>" + name + "</p>";
    html += "<p class='fullname fullnameInputContainer'><input type='text' placeholder='Enter email address...' id='" + slug + "' /></p>";
    html += "</span>";
    html += "</div>";
    html += "<a class='remove-user-email remove-user' id='" + slug + "'>x</a>";
    html += "</li>";

    $('div#selected_users ul#users').append(html);
    $("input#auto_suggest_users").val('');
}

$('.fullnameInputContainer input').placeholder();

function slugify(text) {
    text = text.replace(/[^-a-zA-Z0-9,&\s]+/ig, '');
    text = text.replace(/-/gi, "_");
    text = text.replace(/\s/gi, "-");
    return text;
}
</script>


<div id="output" style="display: none"></div>
<div id="bagview3" class="summitBoxStep2 facebox">
    <div class="new_bag add_peak">
        <h1 class="peakname"><img src="{% thumbnail peak.get_thumbnail 70x50 crop %}" alt="map" />
            <span class="text">
                <span style="color:#00B1F2">{{ peak.name }} Summit Log</span>
            </span>
        </h1>
        <form method="POST" action="{% url "summit_3" peak.id summit.id %}{{ edit_enabled }}" id="form_peak3">
            <fieldset id="youSummitWith">
                <h2 class="title" style="display: inline-block; float: left;">4. Your trip companions</h2>
                <span class="helpText" style="display: inline-block; color: #333; margin: 5px 0 10px 20px;">Add people one at a time and hit enter</span>
                <span class="a userData onFocus">
                    <input type="text" name="auto_suggest_users" id="auto_suggest_users" />
                    <span class="holder">Name or Username ... </span>
                </span>
                <input type="hidden" value="{{ hidden_field_value }}" name="fellow_selected_users" id="fellow_selected_users" />
                <div id="selected_users">
                    <ul id="users" class="clearfix">
                        {% if urs %}
                            {% for ur in urs %}
                                {{ ur.get_relation_box }}
                            {% endfor %}
                        {% endif %}
                    </ul>
                </div>
            </fieldset>
            <fieldset id="routes">
                <h2 class="title">5. Your route</h2>
                <div class="clearfix routeUp">
                    <span class="a arrowUp onFocus">
                        {% comment %}
                        <input type="text" name="route_up" id="route_up" />
                        {% endcomment %}
{#                        <span class="holder">{% if current_route_up %}{{ current_route_up|default:'' }}{% else %}Select route you took <strong>up</strong>{% endif %}</span>#}
                        <div id="route_up_container">
                            <select name="route_up" id="route_up">
                                <option value="" selected="selected" style="display:none;">Select route you took up</option>
                                {% for rt in all_routes_up %}
                                    {% if not current_route_up %}
                                        {% if forloop.counter == 1 %}
                                            <option value="{{ rt.name }}">{{ rt.name }} &bull; used {{ rt.percentage }}% of the time</option>
                                        {% else %}
                                            <option value="{{ rt.name }}">{{ rt.name }} &bull; used {{ rt.percentage }}% of the time</option>
                                        {% endif %}
                                    {% else %}
                                        {% if current_route_up == rt.name %}
                                            <option value="{{ rt.name }}">{{ rt.name }} &bull; used {{ rt.percentage }}% of the time</option>
                                        {% else %}
                                            <option value="{{ rt.name }}">{{ rt.name }} &bull; used {{ rt.percentage }}% of the time</option>
                                        {% endif %}
                                    {% endif %}
                                {% endfor %}
                                <optgroup label="=============================================================================" disabled="disabled"></optgroup>
                                <option value="Or add a new route...">Or add a new route...</option>
                            </select>
                        </div>
                    </span>
                    {#                    <input type="button" value=">" id="btn_route_up" class="arrowButton" />#}
                </div>
                <div class="clearfix routeDown">
                    <span class="a arrowDown onFocus">
                        {% comment %}
                        <input type="text" name="route_down" id="route_down" />
{% endcomment %}
{#                        <span class="holder">{% if current_route_down %}{{ current_route_down|default:'' }}{% else %}Select route you took <strong>down</strong>{% endif %}</span>#}
                        <div id="route_down_container">
                            <select name="route_down" id="route_down">
                                <option value="" selected="selected" style="display:none;">Select route you took down</option>
                                <option value="Same as route up">Same as route up</option>
                                {% for rt in all_routes_down %}
                                    {% if not current_route_down %}
                                        {% if forloop.counter == 1 %}
                                            <option value="{{ rt.name }}">{{ rt.name }} &bull; used {{ rt.percentage }}% of the time</option>
                                        {% else %}
                                            <option value="{{ rt.name }}">{{ rt.name }} &bull; used {{ rt.percentage }}% of the time</option>
                                        {% endif %}
                                        {% else %}
                                        {% if current_route_down == rt.name %}
                                            <option value="{{ rt.name }}" selected="selected">{{ rt.name }} &bull; used {{ rt.percentage }}% of the time</option>
                                        {% else %}
                                            <option value="{{ rt.name }}">{{ rt.name }} &bull; used {{ rt.percentage }}% of the time</option>
                                        {% endif %}
                                    {% endif %}
                                {% endfor %}
                                <optgroup label="=============================================================================" disabled="disabled"></optgroup>
                                <option value="Or add a new route...">Or add a new route...</option>
                            </select>
                        </div>
                    </span>
                    {#                    <input type="button" value=">" id="btn_route_down" class="arrowButton" />#}
                </div>
            </fieldset>
            <script type="text/javascript">
                $(function(){
                    if($('span.a input').val() != ""){
                        $('span.a span.holder').hide();
                    }
                    $('span.a span.holder').click(function(){
                        $(this).parent().find('input').focus();
                    });
                    $('span.a input').keydown(function(){
                        $(this).parent().find('span.holder').hide(100);
                    });
                    $('span.a input').keyup(function(){
                        if($(this).val() == ""){
                            $(this).parent().find('span.holder').show(100);
                        }
                    });
                    $('span.a input').blur(function(){
                        if($(this).val() == ""){
                            $(this).parent().find('span.holder').show(100);
                        }
                    });
                    $('span.a.onFocus input').focus(function(){
                        $(this).parent().find('span.holder').hide();
                    });

                    $('input#route_up').live('focus', function(){
                        $(this).parent().find('span.holder').hide();
                    });
                    $('input#route_down').live('focus', function(){
                        $(this).parent().find('span.holder').hide();
                    });

                    $('input#route_up').live('blur', function(){
                        if($(this).val() == "") {
                            $(this).parent().find('span.holder').show(100);
                        }
                    });
                    $('input#route_down').live('blur', function(){
                        if($(this).val() == "") {
                            $(this).parent().find('span.holder').show(100);
                        }
                    });
                });
            </script>
            <input type="hidden" name="total_fellows" value="2">
            <div id="upload-ajax-spinner" style="display: none;"><img src="{{ MEDIA_URL }}img/misc/loading.gif" /></div>
            <ul class="horz righted">
                <li><a href="javascript:void(0)" class="linealign" id="skip3">SKIP</a></li>
                <li><input class="btn set2 input" id="add_peak3" type="button" value="NEXT &raquo;" /></li>
            </ul>
        </form>
    </div><!-- ENd new_bag -->
</div><!-- ENd bagview3 -->

<a id="step3" href="{% url "summit_4" peak.id summit.id %}" style="display: none"></a>

<script type="text/javascript">(function(a){a.uniform={options:{selectClass:"selector",radioClass:"radio",checkboxClass:"checker",fileClass:"uploader",filenameClass:"filename",fileBtnClass:"action",fileDefaultText:"No file selected",fileBtnText:"Choose File",checkedClass:"checked",focusClass:"focus",disabledClass:"disabled",buttonClass:"button",activeClass:"active",hoverClass:"hover",useID:true,idPrefix:"uniform",resetSelector:false,autoHide:true},elements:[]};if(a.browser.msie&&a.browser.version<7){a.support.selectOpacity=false}else{a.support.selectOpacity=true}a.fn.uniform=function(k){k=a.extend(a.uniform.options,k);var d=this;if(k.resetSelector!=false){a(k.resetSelector).mouseup(function(){function l(){a.uniform.update(d)}setTimeout(l,10)})}function j(l){$el=a(l);$el.addClass($el.attr("type"));b(l)}function g(l){a(l).addClass("uniform");b(l)}function i(o){var m=a(o);var p=a("<div>"),l=a("<span>");p.addClass(k.buttonClass);if(k.useID&&m.attr("id")!=""){p.attr("id",k.idPrefix+"-"+m.attr("id"))}var n;if(m.is("a")||m.is("button")){n=m.text()}else{if(m.is(":submit")||m.is(":reset")||m.is("input[type=button]")){n=m.attr("value")}}n=n==""?m.is(":reset")?"Reset":"Submit":n;l.html(n);m.css("opacity",0);m.wrap(p);m.wrap(l);p=m.closest("div");l=m.closest("span");if(m.is(":disabled")){p.addClass(k.disabledClass)}p.bind({"mouseenter.uniform":function(){p.addClass(k.hoverClass)},"mouseleave.uniform":function(){p.removeClass(k.hoverClass);p.removeClass(k.activeClass)},"mousedown.uniform touchbegin.uniform":function(){p.addClass(k.activeClass)},"mouseup.uniform touchend.uniform":function(){p.removeClass(k.activeClass)},"click.uniform touchend.uniform":function(r){if(a(r.target).is("span")||a(r.target).is("div")){if(o[0].dispatchEvent){var q=document.createEvent("MouseEvents");q.initEvent("click",true,true);o[0].dispatchEvent(q)}else{o[0].click()}}}});o.bind({"focus.uniform":function(){p.addClass(k.focusClass)},"blur.uniform":function(){p.removeClass(k.focusClass)}});a.uniform.noSelect(p);b(o)}function e(o){var m=a(o);var p=a("<div />"),l=a("<span />");if(!m.css("display")=="none"&&k.autoHide){p.hide()}p.addClass(k.selectClass);if(k.useID&&o.attr("id")!=""){p.attr("id",k.idPrefix+"-"+o.attr("id"))}var n=o.find(":selected:first");if(n.length==0){n=o.find("option:first")}l.html(n.html());o.css("opacity",0);o.wrap(p);o.before(l);p=o.parent("div");l=o.siblings("span");o.bind({"change.uniform":function(){l.text(o.find(":selected").html());p.removeClass(k.activeClass)},"focus.uniform":function(){p.addClass(k.focusClass)},"blur.uniform":function(){p.removeClass(k.focusClass);p.removeClass(k.activeClass)},"mousedown.uniform touchbegin.uniform":function(){p.addClass(k.activeClass)},"mouseup.uniform touchend.uniform":function(){p.removeClass(k.activeClass)},"click.uniform touchend.uniform":function(){p.removeClass(k.activeClass)},"mouseenter.uniform":function(){p.addClass(k.hoverClass)},"mouseleave.uniform":function(){p.removeClass(k.hoverClass);p.removeClass(k.activeClass)},"keyup.uniform":function(){l.text(o.find(":selected").html())}});if(a(o).attr("disabled")){p.addClass(k.disabledClass)}a.uniform.noSelect(l);b(o)}function f(n){var m=a(n);var o=a("<div />"),l=a("<span />");if(!m.css("display")=="none"&&k.autoHide){o.hide()}o.addClass(k.checkboxClass);if(k.useID&&n.attr("id")!=""){o.attr("id",k.idPrefix+"-"+n.attr("id"))}a(n).wrap(o);a(n).wrap(l);l=n.parent();o=l.parent();a(n).css("opacity",0).bind({"focus.uniform":function(){o.addClass(k.focusClass)},"blur.uniform":function(){o.removeClass(k.focusClass)},"click.uniform touchend.uniform":function(){if(!a(n).attr("checked")){l.removeClass(k.checkedClass)}else{l.addClass(k.checkedClass)}},"mousedown.uniform touchbegin.uniform":function(){o.addClass(k.activeClass)},"mouseup.uniform touchend.uniform":function(){o.removeClass(k.activeClass)},"mouseenter.uniform":function(){o.addClass(k.hoverClass)},"mouseleave.uniform":function(){o.removeClass(k.hoverClass);o.removeClass(k.activeClass)}});if(a(n).attr("checked")){l.addClass(k.checkedClass)}if(a(n).attr("disabled")){o.addClass(k.disabledClass)}b(n)}function c(n){var m=a(n);var o=a("<div />"),l=a("<span />");if(!m.css("display")=="none"&&k.autoHide){o.hide()}o.addClass(k.radioClass);if(k.useID&&n.attr("id")!=""){o.attr("id",k.idPrefix+"-"+n.attr("id"))}a(n).wrap(o);a(n).wrap(l);l=n.parent();o=l.parent();a(n).css("opacity",0).bind({"focus.uniform":function(){o.addClass(k.focusClass)},"blur.uniform":function(){o.removeClass(k.focusClass)},"click.uniform touchend.uniform":function(){if(!a(n).attr("checked")){l.removeClass(k.checkedClass)}else{var p=k.radioClass.split(" ")[0];a("."+p+" span."+k.checkedClass+":has([name='"+a(n).attr("name")+"'])").removeClass(k.checkedClass);l.addClass(k.checkedClass)}},"mousedown.uniform touchend.uniform":function(){if(!a(n).is(":disabled")){o.addClass(k.activeClass)}},"mouseup.uniform touchbegin.uniform":function(){o.removeClass(k.activeClass)},"mouseenter.uniform touchend.uniform":function(){o.addClass(k.hoverClass)},"mouseleave.uniform":function(){o.removeClass(k.hoverClass);o.removeClass(k.activeClass)}});if(a(n).attr("checked")){l.addClass(k.checkedClass)}if(a(n).attr("disabled")){o.addClass(k.disabledClass)}b(n)}function h(q){var o=a(q);var r=a("<div />"),p=a("<span>"+k.fileDefaultText+"</span>"),m=a("<span>"+k.fileBtnText+"</span>");if(!o.css("display")=="none"&&k.autoHide){r.hide()}r.addClass(k.fileClass);p.addClass(k.filenameClass);m.addClass(k.fileBtnClass);if(k.useID&&o.attr("id")!=""){r.attr("id",k.idPrefix+"-"+o.attr("id"))}o.wrap(r);o.after(m);o.after(p);r=o.closest("div");p=o.siblings("."+k.filenameClass);m=o.siblings("."+k.fileBtnClass);if(!o.attr("size")){var l=r.width();o.attr("size",l/10)}var n=function(){var s=o.val();if(s===""){s=k.fileDefaultText}else{s=s.split(/[\/\\]+/);s=s[(s.length-1)]}p.text(s)};n();o.css("opacity",0).bind({"focus.uniform":function(){r.addClass(k.focusClass)},"blur.uniform":function(){r.removeClass(k.focusClass)},"mousedown.uniform":function(){if(!a(q).is(":disabled")){r.addClass(k.activeClass)}},"mouseup.uniform":function(){r.removeClass(k.activeClass)},"mouseenter.uniform":function(){r.addClass(k.hoverClass)},"mouseleave.uniform":function(){r.removeClass(k.hoverClass);r.removeClass(k.activeClass)}});if(a.browser.msie){o.bind("click.uniform.ie7",function(){setTimeout(n,0)})}else{o.bind("change.uniform",n)}if(o.attr("disabled")){r.addClass(k.disabledClass)}a.uniform.noSelect(p);a.uniform.noSelect(m);b(q)}a.uniform.restore=function(l){if(l==undefined){l=a(a.uniform.elements)}a(l).each(function(){if(a(this).is(":checkbox")){a(this).unwrap().unwrap()}else{if(a(this).is("select")){a(this).siblings("span").remove();a(this).unwrap()}else{if(a(this).is(":radio")){a(this).unwrap().unwrap()}else{if(a(this).is(":file")){a(this).siblings("span").remove();a(this).unwrap()}else{if(a(this).is("button, :submit, :reset, a, input[type='button']")){a(this).unwrap().unwrap()}}}}}a(this).unbind(".uniform");a(this).css("opacity","1");var m=a.inArray(a(l),a.uniform.elements);a.uniform.elements.splice(m,1)})};function b(l){l=a(l).get();if(l.length>1){a.each(l,function(m,n){a.uniform.elements.push(n)})}else{a.uniform.elements.push(l)}}a.uniform.noSelect=function(l){function m(){return false}a(l).each(function(){this.onselectstart=this.ondragstart=m;a(this).mousedown(m).css({MozUserSelect:"none"})})};a.uniform.update=function(l){if(l==undefined){l=a(a.uniform.elements)}l=a(l);l.each(function(){var n=a(this);if(n.is("select")){var m=n.siblings("span");var p=n.parent("div");p.removeClass(k.hoverClass+" "+k.focusClass+" "+k.activeClass);m.html(n.find(":selected").html());if(n.is(":disabled")){p.addClass(k.disabledClass)}else{p.removeClass(k.disabledClass)}}else{if(n.is(":checkbox")){var m=n.closest("span");var p=n.closest("div");p.removeClass(k.hoverClass+" "+k.focusClass+" "+k.activeClass);m.removeClass(k.checkedClass);if(n.is(":checked")){m.addClass(k.checkedClass)}if(n.is(":disabled")){p.addClass(k.disabledClass)}else{p.removeClass(k.disabledClass)}}else{if(n.is(":radio")){var m=n.closest("span");var p=n.closest("div");p.removeClass(k.hoverClass+" "+k.focusClass+" "+k.activeClass);m.removeClass(k.checkedClass);if(n.is(":checked")){m.addClass(k.checkedClass)}if(n.is(":disabled")){p.addClass(k.disabledClass)}else{p.removeClass(k.disabledClass)}}else{if(n.is(":file")){var p=n.parent("div");var o=n.siblings(k.filenameClass);btnTag=n.siblings(k.fileBtnClass);p.removeClass(k.hoverClass+" "+k.focusClass+" "+k.activeClass);o.text(n.val());if(n.is(":disabled")){p.addClass(k.disabledClass)}else{p.removeClass(k.disabledClass)}}else{if(n.is(":submit")||n.is(":reset")||n.is("button")||n.is("a")||l.is("input[type=button]")){var p=n.closest("div");p.removeClass(k.hoverClass+" "+k.focusClass+" "+k.activeClass);if(n.is(":disabled")){p.addClass(k.disabledClass)}else{p.removeClass(k.disabledClass)}}}}}}})};return this.each(function(){if(a.support.selectOpacity){var l=a(this);if(l.is("select")){if(l.attr("multiple")!=true){if(l.attr("size")==undefined||l.attr("size")<=1){e(l)}}}else{if(l.is(":checkbox")){f(l)}else{if(l.is(":radio")){c(l)}else{if(l.is(":file")){h(l)}else{if(l.is(":text, :password, input[type='email']")){j(l)}else{if(l.is("textarea")){g(l)}else{if(l.is("a")||l.is(":submit")||l.is(":reset")||l.is("button")||l.is("input[type=button]")){i(l)}}}}}}}}})}})(jQuery);</script>
<style type="text/css">
    /* Global Declaration */

div.selector,
div.selector span,
div.checker span,
div.radio span,
div.uploader,
div.uploader span.action,
div.button,
div.button span {
    background-image: url("{{ MEDIA_URL }}img/uniform/sprite.png");
    background-repeat: no-repeat;
    -webkit-font-smoothing: antialiased;
}

.selector,
.radio,
.checker,
.uploader,
.button,
.selector *,
.radio *,
.checker *,
.uploader *,
.button *{
    margin: 0;
    padding: 0;
}

    /* INPUT & TEXTAREA */

input.text,
input.email,
input.password,
textarea.uniform {
    font-size: 12px;
    font-family: Avenir,Trebuchet MS,sans-serif;
    font-weight: normal;
    padding: 3px;
    color: #777;
    background: url('{{ MEDIA_URL }}img/uniform/bg-input-focus.png') repeat-x 0px 0px;
    background: url('{{ MEDIA_URL }}img/uniform/bg-input.png') repeat-x 0px 0px;
    border-top: solid 1px #aaa;
    border-left: solid 1px #aaa;
    border-bottom: solid 1px #ccc;
    border-right: solid 1px #ccc;
    outline: 0;
}

input.text:focus,
input.email:focus,
input.password:focus,
textarea.uniform:focus {
    -webkit-box-shadow: 0px 0px 4px rgba(0,0,0,0.3);
    -moz-box-shadow: 0px 0px 4px rgba(0,0,0,0.3);
    box-shadow: 0px 0px 4px rgba(0,0,0,0.3);
    border-color: #999;
    background: url('{{ MEDIA_URL }}img/uniform/bg-input-focus.png') repeat-x 0px 0px;
}

    /* SPRITES */

    /* Select */

div.selector {
    background-position: -483px -130px;
    line-height: 26px;
    height: 26px;
}

div.selector span {
    background-position: right 0px;
    height: 26px;
    line-height: 26px;
}

div.selector select {
    /* change these to adjust positioning of select element */
    top: 0px;
    left: 0px;
}

div.selector:active,
div.selector.active {
    background-position: -483px -156px;
}

div.selector:active span,
div.selector.active span {
    background-position: right -26px;
}

div.selector.focus, div.selector.hover, div.selector:hover {
    background-position: -483px -182px;
}

div.selector.focus span, div.selector.hover span, div.selector:hover span {
    background-position: right -52px;
}

div.selector.focus:active,
div.selector.focus.active,
div.selector:hover:active,
div.selector.active:hover {
    background-position: -483px -208px;
}

div.selector.focus:active span,
div.selector:hover:active span,
div.selector.active:hover span,
div.selector.focus.active span {
    background-position: right -78px;
}

div.selector.disabled {
    background-position: -483px -234px;
}

div.selector.disabled span {
    background-position: right -104px;
}

    /* Checkbox */

div.checker {
    width: 19px;
    height: 19px;
}

div.checker input {
    width: 19px;
    height: 19px;
}

div.checker span {
    background-position: 0px -260px;
    height: 19px;
    width: 19px;
}

div.checker:active span,
div.checker.active span {
    background-position: -19px -260px;
}

div.checker.focus span,
div.checker:hover span {
    background-position: -38px -260px;
}

div.checker.focus:active span,
div.checker:active:hover span,
div.checker.active:hover span,
div.checker.focus.active span {
    background-position: -57px -260px;
}

div.checker span.checked {
    background-position: -76px -260px;
}

div.checker:active span.checked,
div.checker.active span.checked {
    background-position: -95px -260px;
}

div.checker.focus span.checked,
div.checker:hover span.checked {
    background-position: -114px -260px;
}

div.checker.focus:active span.checked,
div.checker:hover:active span.checked,
div.checker.active:hover span.checked,
div.checker.active.focus span.checked {
    background-position: -133px -260px;
}

div.checker.disabled span,
div.checker.disabled:active span,
div.checker.disabled.active span {
    background-position: -152px -260px;
}

div.checker.disabled span.checked,
div.checker.disabled:active span.checked,
div.checker.disabled.active span.checked {
    background-position: -171px -260px;
}

    /* Radio */

div.radio {
    width: 18px;
    height: 18px;
}

div.radio input {
    width: 18px;
    height: 18px;
}

div.radio span {
    height: 18px;
    width: 18px;
    background-position: 0px -279px;
}

div.radio:active span,
div.radio.active span {
    background-position: -18px -279px;
}

div.radio.focus span,
div.radio:hover span {
    background-position: -36px -279px;
}

div.radio.focus:active span,
div.radio:active:hover span,
div.radio.active:hover span,
div.radio.active.focus span {
    background-position: -54px -279px;
}

div.radio span.checked {
    background-position: -72px -279px;
}

div.radio:active span.checked,
div.radio.active span.checked {
    background-position: -90px -279px;
}

div.radio.focus span.checked, div.radio:hover span.checked {
    background-position: -108px -279px;
}

div.radio.focus:active span.checked,
div.radio:hover:active span.checked,
div.radio.focus.active span.checked,
div.radio.active:hover span.checked {
    background-position: -126px -279px;
}

div.radio.disabled span,
div.radio.disabled:active span,
div.radio.disabled.active span {
    background-position: -144px -279px;
}

div.radio.disabled span.checked,
div.radio.disabled:active span.checked,
div.radio.disabled.active span.checked {
    background-position: -162px -279px;
}

    /* Uploader */

div.uploader {
    background-position: 0px -297px;
    height: 28px;
}

div.uploader span.action {
    background-position: right -409px;
    height: 24px;
    line-height: 24px;
}

div.uploader span.filename {
    height: 24px;
    /* change this line to adjust positioning of filename area */
    margin: 2px 0px 2px 2px;
    line-height: 24px;
}

div.uploader.focus,
div.uploader.hover,
div.uploader:hover {
    background-position: 0px -353px;
}

div.uploader.focus span.action,
div.uploader.hover span.action,
div.uploader:hover span.action {
    background-position: right -437px;
}

div.uploader.active span.action,
div.uploader:active span.action {
    background-position: right -465px;
}

div.uploader.focus.active span.action,
div.uploader:focus.active span.action,
div.uploader.focus:active span.action,
div.uploader:focus:active span.action {
    background-position: right -493px;
}

div.uploader.disabled {
    background-position: 0px -325px;
}

div.uploader.disabled span.action {
    background-position: right -381px;
}

div.button {
    background-position: 0px -523px;
}

div.button span {
    background-position: right -643px;
}

div.button.focus,
div.button:focus,
div.button:hover,
div.button.hover {
    background-position: 0px -553px;
}

div.button.focus span,
div.button:focus span,
div.button:hover span,
div.button.hover span {
    background-position: right -673px;
}

div.button.active,
div.button:active {
    background-position: 0px -583px;
}

div.button.active span,
div.button:active span {
    background-position: right -703px;
    color: #555;
}

div.button.disabled,
div.button:disabled {
    background-position: 0px -613px;
}

div.button.disabled span,
div.button:disabled span {
    background-position: right -733px;
    color: #bbb;
    cursor: default;
}

    /* PRESENTATION */

    /* Button */

div.button {
    height: 30px;
}

div.button span {
    margin-left: 13px;
    height: 22px;
    padding-top: 8px;
    font-weight: bold;
    font-family: Avenir,Trebuchet MS,sans-serif;
    font-size: 12px;
    letter-spacing: 1px;
    text-transform: uppercase;
    padding-left: 2px;
    padding-right: 15px;
}

    /* Select */
div.selector {
    width: 434px;
    font-size: 12px;
}

div.selector select {
    min-width: 190px;
    width: 100%;
    font-family: Avenir,Trebuchet MS,sans-serif;
    font-size: 12px;
    border: solid 1px #fff;
}

div.selector span {
    padding: 0px 25px 0px 2px;
    cursor: pointer;
}

div.selector span {
    color: #333;
    width: 407px;
    background: none;
}

div.selector.disabled span {
    color: #bbb;
}

    /* Checker */
div.checker {
    margin-right: 5px;
}

    /* Radio */
div.radio {
    margin-right: 3px;
}

    /* Uploader */
div.uploader {
    width: 190px;
    cursor: pointer;
}

div.uploader span.action {
    width: 85px;
    text-align: center;
    text-shadow: #fff 0px 1px 0px;
    background-color: #fff;
    font-size: 11px;
    font-weight: bold;
}

div.uploader span.filename {
    color: #777;
    width: 82px;
    border-right: solid 1px #bbb;
    font-size: 11px;
}

div.uploader input {
    width: 190px;
}

div.uploader.disabled span.action {
    color: #aaa;
}

div.uploader.disabled span.filename {
    border-color: #ddd;
    color: #aaa;
}
    /*

    CORE FUNCTIONALITY

    Not advised to edit stuff below this line
    -----------------------------------------------------
    */

.selector,
.checker,
.button,
.radio,
.uploader {
    display: -moz-inline-box;
    display: inline-block;
    vertical-align: middle;
    zoom: 1;
    *display: inline;
}

.selector select:focus, .radio input:focus, .checker input:focus, .uploader input:focus {
    outline: 0;
}

    /* Button */

div.button a,
div.button button,
div.button input {
    position: absolute;
}

div.button {
    cursor: pointer;
    position: relative;
}

div.button span {
    display: -moz-inline-box;
    display: inline-block;
    line-height: 1;
    text-align: center;
}

    /* Select */

div.selector {
    position: relative;
    padding-left: 0;
    overflow: hidden;
    background: none;
    height: 30px;
    width: 444px;
    background: #fff;
    border: 2px solid #999;
}

div.selector span {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 30px;
    width: 444px;
    height: 30px;
    padding: 0;
    max-width: 374px;
    float: left;
}

div.selector select {
    position: absolute;
    opacity: 0;
    filter: alpha(opacity:0);
    height: 25px;
    border: none;
    background: none;
}

    /* Checker */

div.checker {
    position: relative;
}

div.checker span {
    display: -moz-inline-box;
    display: inline-block;
    text-align: center;
}

div.checker input {
    opacity: 0;
    filter: alpha(opacity:0);
    display: inline-block;
    background: none;
}

    /* Radio */

div.radio {
    position: relative;
}

div.radio span {
    display: -moz-inline-box;
    display: inline-block;
    text-align: center;
}

div.radio input {
    opacity: 0;
    filter: alpha(opacity:0);
    text-align: center;
    display: inline-block;
    background: none;
}

    /* Uploader */

div.uploader {
    position: relative;
    overflow: hidden;
    cursor: default;
}

div.uploader span.action {
    float: left;
    display: inline;
    padding: 2px 0px;
    overflow: hidden;
    cursor: pointer;
}

div.uploader span.filename {
    padding: 0px 10px;
    float: left;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: default;
}

div.uploader input {
    opacity: 0;
    filter: alpha(opacity:0);
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    float: right;
    height: 25px;
    border: none;
    cursor: default;
}
</style>

<script type="text/javascript" charset="utf-8">
    $(function(){
        $("select#route_up, select#route_down").uniform();
        $('div#route_down_container div.selector span:first-child').before('<span class="leftArrow"></span>');
        $('div#route_down_container div.selector').append('<span class="dropDown"></span>');
        $('div#route_up_container div.selector span:first-child').before('<span class="leftArrow"></span>');
        $('div#route_up_container div.selector').append('<span class="dropDown"></span>');
    });
</script>
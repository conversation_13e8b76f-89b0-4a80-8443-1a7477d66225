from items.models import Item
from django.core.management.base import BaseCommand
import unicodedata
from django.utils.encoding import smart_unicode

def unaccent_name(name):
    return ''.join((c for c in unicodedata.normalize('NFD', smart_unicode(name)) if unicodedata.category(c) != 'Mn'))

class Command(BaseCommand):
    args = ''
    help = ''

    def handle(self, *args, **options):
        items = Item.objects.all().order_by('id')

        for i in items:
            print 'ID: %s' % i.id

            i.name_unaccented = unaccent_name(i.name)
            i.quick_save()
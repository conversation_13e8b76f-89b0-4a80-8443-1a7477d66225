from concurrent.futures import ThreadPoolExecutor
from django.core.management.base import BaseCommand
from peakery.items.models import Item


def process_items(items):
    for item in items:
        item.db_meta_description = item.create_meta_description()
    Item.objects.bulk_update(items, ['db_meta_description'])
    print("Chunk of", len(items), "processed")


class Command(BaseCommand):
    args = ''
    help = 'Rebuild db_meta_description field for all items.'

    def handle(self, *args, **options):
        print('Rebuilding db_meta_description...')
        batch_size = 1000
        items = list(Item.objects.select_related("user").all())
        total_items = len(items)
        print(f"Will fix {total_items} items")

        num_workers = 8
        chunks = [items[i:i + batch_size] for i in range(0, total_items, batch_size)]

        with ThreadPoolExecutor(max_workers=num_workers) as executor:
            executor.map(process_items, chunks)

        print('Rebuild Done.')
from peakery.items.models import Item
from peakery.cities.models import Country
from django.core.management.base import BaseCommand

class Command(BaseCommand):
    args = ''
    help = ''

    def handle(self, *args, **options):

        country = Country.objects.get(id=190)
        print('COUNTRY: %s' % country.name)

        items = Item.objects.filter(country=country).order_by('id')

        for i in items:
            print('ID: %s %s' %(i.id, i.name))
            i.delete_full_cache()

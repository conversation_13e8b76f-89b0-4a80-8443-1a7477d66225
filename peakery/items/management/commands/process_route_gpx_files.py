from items.models import Item
from django.core.management.base import BaseCommand
from django.db import connection
import sys
from utils.utils import dictfetchall

class Command(BaseCommand):
    help = 'Processes GPX files for peak routes.'
    args = ''

    def handle(self, *args, **options):
        reload(sys)
        sys.setdefaultencoding('utf8')

        import gpxpy
        import gpxpy.gpx
        import math as mod_math
        from geopy import distance as gpdistance
        import cStringIO
        import os.path
        from django.core.files.storage import default_storage

        self.stdout.write('Finding routes to process...\n')

        sql = "select a.id, a.item_id, a.gpx_file " + \
            "from items_peakroute a " + \
            "where length(a.gpx_file) > 0 order by a.id "

        cursor = connection.cursor()
        cursor.execute(sql)
        routes = dictfetchall(cursor)

        for r in routes:

            gpx_path = r['gpx_file']

            self.stdout.write('Processing route: %s' % r['id'])

            try:

                length_2d = 0
                uphill = 0
                downhill = 0
                moving_time = 0
                stopped_time = 0
                moving_distance = 0
                stopped_distance = 0
                max_speed = 0
                start_elevation = 0
                max_elevation = 0
                end_elevation = 0
                start_lat = 0
                start_lon = 0
                end_lat = 0
                end_lon = 0
                total_hours = 0
                total_minutes = 0
                distance_to_summit = 0
                distance_to_summit_temp = 0
                distance_to_summit_time = 0
                distance_to_summit_hours = 0
                distance_to_summit_minutes = 0
                reached_summit = False
                peak = Item.objects.get(id=r['item_id'])
                peak_coords = (peak.lat, peak.long)

                points = []

                f = default_storage.open(gpx_path, 'r')
                gpx = gpxpy.parse(f)
                length_2d = gpx.length_2d()
                uphill, downhill = gpx.get_uphill_downhill()
                moving_time, stopped_time, moving_distance, stopped_distance, max_speed = gpx.get_moving_data()

                if length_2d is None:
                    length_2d = 0
                if uphill is None:
                    uphill = 0
                if downhill is None:
                    downhill = 0
                if moving_time is None:
                    moving_time = 0
                if stopped_time is None:
                    stopped_time = 0
                if moving_distance is None:
                    moving_distance = 0
                if stopped_distance is None:
                    stopped_distance = 0
                if max_speed is None:
                    max_speed = 0

                total_time = moving_time + stopped_time
                total_minutes = mod_math.floor(total_time / 60.)
                total_hours = mod_math.floor(total_minutes / 60.)
                start_elevation = 0
                max_elevation = 0
                start_lat = 0
                start_lon = 0
                end_lat = 0
                end_lon = 0
                previous_point = None
                if gpx.tracks:
                    for track in gpx.tracks:
                        for segment in track.segments:
                            for point in segment.points:
                                if (start_elevation == 0):
                                    start_elevation = point.elevation
                                if (start_lat == 0):
                                    start_lat = point.latitude
                                if (start_lon == 0):
                                    start_lon = point.longitude
                                end_elevation = point.elevation
                                if point.elevation > max_elevation:
                                    max_elevation = point.elevation
                                end_lat = point.latitude
                                end_lon = point.longitude

                                points.append((point.longitude, point.latitude))

                                if previous_point is not None and distance_to_summit == 0:
                                    #add to running distance to summit
                                    distance_to_summit_temp = distance_to_summit_temp + point.distance_2d(previous_point)
                                    if point.time and previous_point.time:
                                        temp_time = point.time - previous_point.time
                                        distance_to_summit_time = distance_to_summit_time + temp_time.total_seconds()
                                    #calculate distance to summit
                                    gpdistance.VincentyDistance.ELLIPSOID = 'WGS-84'
                                    current_point = (point.latitude, point.longitude)
                                    miles = gpdistance.distance(peak_coords, current_point).mi
                                    if miles < 0.0621371:
                                        distance_to_summit = distance_to_summit_temp/1609
                                        reached_summit = True
                                previous_point = point
                elif gpx.routes:
                    for route in gpx.routes:
                        for point in route.points:
                            if (start_elevation == 0):
                                start_elevation = point.elevation
                            if (start_lat == 0):
                                start_lat = point.latitude
                            if (start_lon == 0):
                                start_lon = point.longitude
                            end_elevation = point.elevation
                            if point.elevation > max_elevation:
                                max_elevation = point.elevation
                            end_lat = point.latitude
                            end_lon = point.longitude

                            points.append((point.longitude, point.latitude))

                            if previous_point != None and distance_to_summit == 0:
                                #add to running distance to summit
                                distance_to_summit_temp = distance_to_summit_temp + point.distance_2d(previous_point)
                                if point.time and previous_point.time:
                                    temp_time = point.time - previous_point.time
                                    distance_to_summit_time = distance_to_summit_time + temp_time.total_seconds()
                                #calculate distance to summit
                                gpdistance.VincentyDistance.ELLIPSOID = 'WGS-84'
                                current_point = (point.latitude, point.longitude)
                                miles = gpdistance.distance(peak_coords, current_point).mi
                                if miles < 0.0621371:
                                    distance_to_summit = distance_to_summit_temp/1609
                                    reached_summit = True
                            previous_point = point

                if not reached_summit:
                    distance_to_summit = 0
                    distance_to_summit_time = 0
                    distance_to_summit_hours = 0
                    distance_to_summit_minutes = 0

                #See if one-way route
                one_way_route = False
                if miles:
                    if miles < 0.113636:
                        #last point at summit, one-way route
                        one_way_route = True

                if length_2d:
                    length_2d = length_2d/1609
                else:
                    length_2d = 0
                if start_elevation:
                    start_elevation = start_elevation/0.3048
                else:
                    start_elevation = 0
                if end_elevation:
                    end_elevation = end_elevation/0.3048
                else:
                    end_elevation = 0
                if max_elevation:
                    max_elevation = max_elevation/0.3048
                else:
                    max_elevation = 0
                if uphill:
                    uphill = uphill/0.3048
                else:
                    uphill = 0
                if downhill:
                    downhill = downhill/0.3048
                else:
                    downhill = 0
                if total_minutes:
                    total_minutes = total_minutes % 60
                else:
                    total_minutes = 0
                if distance_to_summit_time:
                    distance_to_summit_minutes = mod_math.floor(distance_to_summit_time / 60.)
                    distance_to_summit_hours = mod_math.floor(distance_to_summit_minutes / 60.)
                    distance_to_summit_minutes = distance_to_summit_minutes % 60
                else:
                    distance_to_summit_minutes = 0
                    distance_to_summit_hours = 0
                success = 'true'
                valid_file = 'true'

                geom = 'SRID=4326;LINESTRING(' + ','.join(['{0} {1}'.format(x[0], x[1]) for x in points]) + ')'

                sql = "update items_peakroute " + \
                      "set gpx_start_lat = %s, " + \
                      "gpx_start_lon = %s, " + \
                      "gpx_end_lat = %s, " + \
                      "gpx_end_lon = %s, " + \
                      "distance_to_summit = %s, " + \
                      "start_elevation = %s, " + \
                      "elevation_gain = %s, " + \
                      "total_distance = %s, " + \
                      "elevation_loss = %s, " + \
                      "max_elevation = %s, " + \
                      "one_way = %s, " + \
                      "gpx_geom = %s " + \
                      "where id = %s "

                cursor = connection.cursor()
                cursor.execute(sql, [start_lat, start_lon, end_lat, end_lon, round(distance_to_summit,2), int(start_elevation), int(uphill), round(length_2d,2), int(downhill), int(max_elevation), one_way_route, geom, r['id']])

            except:
                self.stdout.write('Error processing file.')

        self.stdout.write('END\n')

from items.models import Item
from cities.models import Country, Region
from django.core.management.base import BaseCommand
from django.db import connection
import sys
from utils.utils import dictfetchall

class Command(BaseCommand):
    help = 'Rebuilds peak photos. Pass digit 0-9 to work on IDs ending with that digit.'
    args = '<item_photo_id_digit ...>'

    def handle(self, *args, **options):
        reload(sys)
        sys.setdefaultencoding('utf8')

        def get_if_exist(data, key):
            if key in data:
                return data[key]
            return None

        def convert_to_degrees(value):
            """Helper function to convert the GPS coordinates
            stored in the EXIF to degrees in float format"""
            d0 = value[0][0]
            d1 = value[0][1]
            d = float(d0) / float(d1)
            m0 = value[1][0]
            m1 = value[1][1]
            m = float(m0) / float(m1)

            s0 = value[2][0]
            s1 = value[2][1]
            s = float(s0) / float(s1)

            return d + (m / 60.0) + (s / 3600.0)

        def get_lat(exif_data):
            """Returns the latitude and longitude, if available, from the
            provided exif_data (obtained through get_exif_data above)"""
            # print(exif_data)
            try:
                if 'GPSInfo' in exif_data:
                    gps_info = exif_data["GPSInfo"]
                    gps_latitude = get_if_exist(gps_info, "GPSLatitude")
                    gps_latitude_ref = get_if_exist(gps_info, 'GPSLatitudeRef')
                    if gps_latitude and gps_latitude_ref:
                        lat = convert_to_degrees(gps_latitude)
                        if gps_latitude_ref != "N":
                            lat = 0 - lat
                        return lat
                    else:
                        return None
                else:
                    return None
            except:
                return None

        def get_lon(exif_data):
            """Returns the latitude and longitude, if available, from the
            provided exif_data (obtained through get_exif_data above)"""
            # print(exif_data)
            try:
                if 'GPSInfo' in exif_data:
                    gps_info = exif_data["GPSInfo"]
                    gps_longitude = get_if_exist(gps_info, 'GPSLongitude')
                    gps_longitude_ref = get_if_exist(gps_info, 'GPSLongitudeRef')
                    if gps_longitude and gps_longitude_ref:
                        lon = convert_to_degrees(gps_longitude)
                        if gps_longitude_ref != "E":
                            lon = 0 - lon
                        return lon
                    else:
                        return None
                else:
                    return None
            except:
                return None

        def get_direction(exif_data):
            if 'GPSInfo' in exif_data:
                gps_info = exif_data["GPSInfo"]
                gps_direction = get_if_exist(gps_info, 'GPSImgDirection')
                if gps_direction:
                    return gps_direction
                else:
                    return None
            else:
                return None

        def get_date_time(exif_data):
            if 'DateTimeOriginal' in exif_data:
                date_and_time = exif_data['DateTimeOriginal']
                return date_and_time

        def get_image_height(exif_data):
            if 'ExifImageHeight' in exif_data:
                image_height = exif_data['ExifImageHeight']
                return image_height

        def get_image_width(exif_data):
            if 'ExifImageWidth' in exif_data:
                image_width = exif_data['ExifImageWidth']
                return image_width

        def get_altitude(exif_data):
            if 'GPSInfo' in exif_data:
                gps_info = exif_data["GPSInfo"]
                gps_altitude = get_if_exist(gps_info, 'GPSAltitude')
                if gps_altitude:
                    return gps_altitude
                else:
                    return None
            else:
                return None

        for item_photo_id_digit in args:
            self.stdout.write('Rebuilding missing peak photos ending in '+item_photo_id_digit+'...\n')

            from PIL import Image, ImageFilter, ExifTags
            from PIL.ExifTags import TAGS, GPSTAGS
            import cStringIO
            import os.path
            from django.core.files.storage import default_storage

            self.stdout.write('Finding user photos to rebuild...\n')

            path_string = 'items/users/%'

            sql = "select a.id, a.image, replace(a.image,'items/users/','') as basefile " + \
                "from items_itemphoto a " + \
                "where a.image like %s and a.missing = true " + \
                "and right(cast(a.id as text),1) = %s "

            cursor = connection.cursor()
            cursor.execute(sql, [path_string, item_photo_id_digit])
            photos = dictfetchall(cursor)

            for p in photos:

                upload_path = p['image']
                #upload_path = 'images/items/users/cache/%s.910x680_q95.jpg' % (p['basefile'])
                thumb_file_path = 'images/items/users/cache/%s' % (p['basefile'])

                self.stdout.write('Processing: %s' % (p['image']))

                extension = os.path.splitext(p['basefile'])[1][1:].lower()
                if extension == 'png':
                    extension = 'PNG'
                else:
                    extension = 'JPEG'

                lat = None
                lon = None
                image_height = None
                image_width = None

                try:
                    f = default_storage.open(upload_path, 'r')
                    image = Image.open(f)

                    #extract metadata
                    try:

                        exif_data = {}
                        info = image._getexif()
                        if info:
                            for tag, value in info.items():
                                decoded = TAGS.get(tag, tag)
                                if decoded == "GPSInfo":
                                    gps_data = {}
                                    for t in value:
                                        sub_decoded = GPSTAGS.get(t, t)
                                        gps_data[sub_decoded] = value[t]

                                    exif_data[decoded] = gps_data
                                else:
                                    exif_data[decoded] = value
                        date_time_original = get_date_time(exif_data)
                        image_height = get_image_height(exif_data)
                        image_width = get_image_width(exif_data)
                        altitude = get_altitude(exif_data)
                        image_direction = get_direction(exif_data)
                        lat = get_lat(exif_data)
                        lon = get_lon(exif_data)

                    except (AttributeError, KeyError, IndexError):
                        # cases: image don't have getexif
                        pass

                    try:
                        for orientation in ExifTags.TAGS.keys():
                            if ExifTags.TAGS[orientation]=='Orientation':
                                break
                        exif=dict(image._getexif().items())

                        if exif[orientation] == 3:
                            image=image.rotate(180, expand=True)
                        elif exif[orientation] == 6:
                            image=image.rotate(270, expand=True)
                        elif exif[orientation] == 8:
                            image=image.rotate(90, expand=True)

                    except (AttributeError, KeyError, IndexError):
                        # cases: image don't have getexif
                        pass

                    width, height = image.size

                    # 1920x1440 thumbnail
                    if width >= height:
                        # landscape orientation photo
                        if float(width) / float(height) > 1.333333:
                            # possible panoramic photo, let's keep aspect ratio intact
                            left = 0
                            upper = 0
                            right = width
                            lower = height
                        else:
                            delta = height - (width / 1.333333)
                            left = 0
                            upper = int(delta / 2)
                            right = width
                            lower = height - upper
                    else:
                        # portrait orientation photo
                        if float(height) / float(width) > 1.333333:
                            delta = height - (1.333333 * width)
                            left = 0
                            upper = int(delta / 2)
                            right = width
                            lower = height - upper
                        else:
                            delta = width - (height / 1.333333)
                            left = int(delta / 2)
                            upper = 0
                            right = width - left
                            lower = height

                    tmp_image = image.crop((left, upper, right, lower))
                    if width >= height:
                        new_width = 1920
                        if float(width) / float(height) > 1.333333:
                            # possible panoramic photo, let's keep aspect ratio intact
                            aspect_ratio = float(width) / float(height)
                            new_height = int(1920 / aspect_ratio)
                        else:
                            new_height = 1440
                    else:
                        new_width = 1440
                        new_height = 1920
                    tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
                    tmp_image = tmp_image.filter(ImageFilter.SHARPEN)
                    f_thumb = default_storage.open('%s.%s' % (thumb_file_path, '1920x1440_q95_crop.jpg'), "w")
                    # NOTE, we're saving the image into a cStringIO object to avoid writing to disk
                    out_img = cStringIO.StringIO()
                    # You MUST specify the file type because there is no file name to discern it from
                    tmp_image.save(out_img, format='JPEG', subsampling=0, quality=95)
                    f_thumb.write(out_img.getvalue())
                    f_thumb.close()

                    sql = "update items_itemphoto set missing = false, photo_lat = %s, photo_lng = %s, image_height = %s, image_width = %s where id = %s"
                    cursor = connection.cursor()
                    cursor.execute(sql, [lat, lon, image_height, image_width, p['id']])

                except Exception as e:
                    pass

            self.stdout.write('Finding main photos to rebuild...\n')

            path_string = 'items/main/%'

            sql = "select a.id, a.image, replace(a.image,'items/main/','') as basefile " + \
                "from items_itemphoto a " + \
                "where a.image like %s and a.missing = true " + \
                "and right(cast(a.id as text),1) = %s "

            cursor = connection.cursor()
            cursor.execute(sql, [path_string, item_photo_id_digit])
            photos = dictfetchall(cursor)

            for p in photos:

                upload_path = p['image']
                #upload_path = 'images/items/main/cache/%s.910x680_q95.jpg' % (p['basefile'])
                thumb_file_path = 'images/items/main/cache/%s' % (p['basefile'])

                self.stdout.write('Processing: %s' % (p['image']))

                extension = os.path.splitext(p['basefile'])[1][1:].lower()
                if extension == 'png':
                    extension = 'PNG'
                else:
                    extension = 'JPEG'

                lat = None
                lon = None
                image_height = None
                image_width = None

                try:
                    f = default_storage.open(upload_path, 'r')
                    image = Image.open(f)

                    # extract metadata
                    try:

                        exif_data = {}
                        info = image._getexif()
                        if info:
                            for tag, value in info.items():
                                decoded = TAGS.get(tag, tag)
                                if decoded == "GPSInfo":
                                    gps_data = {}
                                    for t in value:
                                        sub_decoded = GPSTAGS.get(t, t)
                                        gps_data[sub_decoded] = value[t]

                                    exif_data[decoded] = gps_data
                                else:
                                    exif_data[decoded] = value
                        date_time_original = get_date_time(exif_data)
                        image_height = get_image_height(exif_data)
                        image_width = get_image_width(exif_data)
                        altitude = get_altitude(exif_data)
                        image_direction = get_direction(exif_data)
                        lat = get_lat(exif_data)
                        lon = get_lon(exif_data)

                    except (AttributeError, KeyError, IndexError):
                        # cases: image don't have getexif
                        pass

                    try:
                        for orientation in ExifTags.TAGS.keys():
                            if ExifTags.TAGS[orientation]=='Orientation':
                                break
                        exif=dict(image._getexif().items())

                        if exif[orientation] == 3:
                            image=image.rotate(180, expand=True)
                        elif exif[orientation] == 6:
                            image=image.rotate(270, expand=True)
                        elif exif[orientation] == 8:
                            image=image.rotate(90, expand=True)

                    except (AttributeError, KeyError, IndexError):
                        # cases: image don't have getexif
                        pass

                    width, height = image.size

                    # 1920x1440 thumbnail
                    if width >= height:
                        # landscape orientation photo
                        if float(width) / float(height) > 1.333333:
                            # possible panoramic photo, let's keep aspect ratio intact
                            left = 0
                            upper = 0
                            right = width
                            lower = height
                        else:
                            delta = height - (width / 1.333333)
                            left = 0
                            upper = int(delta / 2)
                            right = width
                            lower = height - upper
                    else:
                        # portrait orientation photo
                        if float(height) / float(width) > 1.333333:
                            delta = height - (1.333333 * width)
                            left = 0
                            upper = int(delta / 2)
                            right = width
                            lower = height - upper
                        else:
                            delta = width - (height / 1.333333)
                            left = int(delta / 2)
                            upper = 0
                            right = width - left
                            lower = height

                    tmp_image = image.crop((left, upper, right, lower))
                    if width >= height:
                        new_width = 1920
                        if float(width) / float(height) > 1.333333:
                            # possible panoramic photo, let's keep aspect ratio intact
                            aspect_ratio = float(width) / float(height)
                            new_height = int(1920 / aspect_ratio)
                        else:
                            new_height = 1440
                    else:
                        new_width = 1440
                        new_height = 1920
                    tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
                    tmp_image = tmp_image.filter(ImageFilter.SHARPEN)
                    f_thumb = default_storage.open('%s.%s' % (thumb_file_path, '1920x1440_q95_crop.jpg'), "w")
                    # NOTE, we're saving the image into a cStringIO object to avoid writing to disk
                    out_img = cStringIO.StringIO()
                    # You MUST specify the file type because there is no file name to discern it from
                    tmp_image.save(out_img, format='JPEG', subsampling=0, quality=95)
                    f_thumb.write(out_img.getvalue())
                    f_thumb.close()

                    sql = "update items_itemphoto set missing = false, photo_lat = %s, photo_lng = %s, image_height = %s, image_width = %s where id = %s"
                    cursor = connection.cursor()
                    cursor.execute(sql, [lat, lon, image_height, image_width, p['id']])

                except Exception as e:
                    pass

        self.stdout.write('END\n')

from peakery.items.models import It<PERSON>, <PERSON>Rout<PERSON>, SummitLog
from django.core.management.base import BaseCommand
from django.db import connection
import sys
from peakery.utils.utils import dictfetchall
from django.template.defaultfilters import slugify

class Command(BaseCommand):
    help = 'Processes GPX files for summit logs.'
    args = '<summitlog_id_digit ...>'

    def handle(self, *args, **options):
        reload(sys)
        sys.setdefaultencoding('utf8')

        import gpxpy
        import gpxpy.gpx
        import math as mod_math
        from geopy import distance as gpdistance
        import gpxpy.geo as geo
        import os.path
        from django.core.files.storage import default_storage
        gpdistance.VincentyDistance.ELLIPSOID = 'WGS-84'

        for summitlog_id_digit in args:

            self.stdout.write('Finding summit logs...\n')

            sql = "select a.id, a.item_id, a.gpx_file, b.lat as peak_lat, b.long as peak_long " + \
                  "from items_summitlog a, items_item b " + \
                  "where right(cast(a.id as text),1) = %s and length(a.gpx_file) > 0 and (elevation_loss is null or max_elevation is null) and a.item_id = b.id "

            summitlogs = SummitLog.objects.raw(sql, [summitlog_id_digit])

            for s in summitlogs:
                gpx_path = s.gpx_file

                self.stdout.write('Processing: %s' % gpx_path)

                try:

                    success = 'false'
                    valid_file = 'false'
                    length_2d = 0
                    uphill = 0
                    downhill = 0
                    moving_time = 0
                    stopped_time = 0
                    moving_distance = 0
                    stopped_distance = 0
                    max_speed = 0
                    start_elevation = 0
                    max_elevation = 0
                    end_elevation = 0
                    start_lat = 0
                    start_lon = 0
                    end_lat = 0
                    end_lon = 0
                    total_hours = 0
                    total_minutes = 0
                    distance_to_summit = 0
                    distance_to_summit_temp = 0
                    distance_to_summit_time = 0
                    distance_to_summit_hours = 0
                    distance_to_summit_minutes = 0
                    geom = None
                    points = []

                    reached_any_summits = False
                    # parse gpx

                    f = default_storage.open(gpx_path, 'r')
                    gpx = gpxpy.parse(f)
                    length_2d = gpx.length_2d()
                    uphill, downhill = gpx.get_uphill_downhill()
                    moving_time, stopped_time, moving_distance, stopped_distance, max_speed = gpx.get_moving_data()

                    gpx_bounds = gpx.get_bounds()
                    time_bounds = gpx.get_time_bounds()

                    locations = []
                    distance_to_summit_temp = 0
                    distance_to_summit_time = 0
                    reached_summit = False
                    distance_to_summit = 0
                    peak_coords = (s.peak_lat, s.peak_long)
                    if length_2d is None:
                        length_2d = 0
                    if uphill is None:
                        uphill = 0
                    if downhill is None:
                        downhill = 0
                    if moving_time is None:
                        moving_time = 0
                    if stopped_time is None:
                        stopped_time = 0
                    if moving_distance is None:
                        moving_distance = 0
                    if stopped_distance is None:
                        stopped_distance = 0
                    if max_speed is None:
                        max_speed = 0

                    total_time = moving_time + stopped_time
                    total_minutes = mod_math.floor(total_time / 60.)
                    total_hours = mod_math.floor(total_minutes / 60.)
                    start_elevation = 0
                    max_elevation = 0
                    start_lat = 0
                    start_lon = 0
                    end_lat = 0
                    end_lon = 0
                    previous_point = None
                    points = []
                    if gpx.tracks:
                        for track in gpx.tracks:
                            for segment in track.segments:
                                for point in segment.points:
                                    if (start_elevation == 0):
                                        start_elevation = point.elevation
                                    if (start_lat == 0):
                                        start_lat = point.latitude
                                    if (start_lon == 0):
                                        start_lon = point.longitude
                                    if point.elevation > max_elevation:
                                        max_elevation = point.elevation
                                    end_elevation = point.elevation
                                    end_lat = point.latitude
                                    end_lon = point.longitude
                                    locations.append(point)
                                    points.append((point.longitude, point.latitude))
                                    current_point = (point.latitude, point.longitude)
                                    miles = gpdistance.distance(peak_coords, current_point).mi
                                    if previous_point is not None and distance_to_summit == 0:
                                        # add to running distance to summit
                                        distance_to_summit_temp = distance_to_summit_temp + point.distance_2d(
                                            previous_point)
                                        if point.time and previous_point.time:
                                            temp_time = point.time - previous_point.time
                                            distance_to_summit_time = distance_to_summit_time + temp_time.total_seconds()
                                        # calculate distance to summit
                                        if miles < 0.0621371:
                                            distance_to_summit = distance_to_summit_temp / 1609
                                            reached_summit = True
                                            reached_any_summits = True
                                    previous_point = point
                    elif gpx.routes:
                        for route in gpx.routes:
                            for point in route.points:
                                if (start_elevation == 0):
                                    start_elevation = point.elevation
                                if (start_lat == 0):
                                    start_lat = point.latitude
                                if (start_lon == 0):
                                    start_lon = point.longitude
                                end_elevation = point.elevation
                                if point.elevation > max_elevation:
                                    max_elevation = point.elevation
                                end_lat = point.latitude
                                end_lon = point.longitude
                                points.append((point.longitude, point.latitude))
                                current_point = (point.latitude, point.longitude)
                                miles = gpdistance.distance(peak_coords, current_point).mi
                                if previous_point is not None and distance_to_summit == 0:
                                    # add to running distance to summit
                                    distance_to_summit_temp = distance_to_summit_temp + point.distance_2d(previous_point)
                                    if point.time and previous_point.time:
                                        temp_time = point.time - previous_point.time
                                        distance_to_summit_time = distance_to_summit_time + temp_time.total_seconds()
                                    # calculate distance to summit
                                    if miles < 0.0621371:
                                        distance_to_summit = distance_to_summit_temp / 1609
                                        reached_summit = True
                                        reached_any_summits = True
                                previous_point = point

                    length_2d_saved = length_2d / 1609
                    start_elevation_saved = start_elevation / 0.3048
                    max_elevation_saved = max_elevation / 0.3048
                    end_elevation_saved = end_elevation / 0.3048
                    uphill_saved = uphill / 0.3048
                    downhill_saved = downhill / 0.3048
                    total_minutes = total_minutes % 60

                    if reached_summit:
                        distance_to_summit_minutes = mod_math.floor(distance_to_summit_time / 60.)
                        distance_to_summit_hours = mod_math.floor(distance_to_summit_minutes / 60.)
                        distance_to_summit_minutes = distance_to_summit_minutes % 60

                    sql = "update items_summitlog " + \
                          "set elevation_loss = %s, " + \
                          "max_elevation = %s " + \
                          "where id = %s "

                    cursor = connection.cursor()
                    cursor.execute(sql, [int(downhill_saved), int(max_elevation_saved), s.id])

                except:
                    self.stdout.write('Unable to parse GPX file.\n')

        self.stdout.write('END\n')

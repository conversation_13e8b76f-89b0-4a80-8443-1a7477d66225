import datetime
from django.core.management.base import BaseCommand
from django.db import connection, transaction

from peakery.items.models import ItemRegionRanking, ItemCountryRanking

DELETE_REGION_RANKING_QUERY = "DELETE FROM items_itemregionranking"
DELETE_COUNTRY_RANKING_QUERY = "DELETE FROM items_itemcountryranking"


RESET_REGION_RANKING_ID_SEQUENCE_SQL = "SELECT setval('items_itemregionranking_id_seq', 1, false);"
RESET_COUNTRY_RANKING_ID_SEQUENCE_SQL = "SELECT setval('items_itemcountryranking_id_seq', 1, false);"

BATCH_SIZE = 1000  # Process records in chunks


def update_items_region_ranking():
    print("Updating items_itemregionranking")
    sql = """
        SELECT 
            c.name AS region_name, 
            c.slug AS region_slug, 
            cc.slug AS country_slug, 
            a.id AS item_id,
            COALESCE(d.region_elevation_rank, 1) AS region_elevation_rank, 
            COALESCE(e.region_prominence_rank, 1) AS region_prominence_rank, 
            COALESCE(f.region_summits_rank, 1) AS region_summits_rank
        FROM items_item a
        JOIN items_item_region b ON b.item_id = a.id
        JOIN cities_region c ON c.id = b.region_id
        JOIN cities_country cc ON cc.id = c.country_id
        LEFT JOIN (
            SELECT 
                y.item_id, 
                y.region_id, 
                RANK() OVER (PARTITION BY y.region_id ORDER BY x.elevation DESC) AS region_elevation_rank
            FROM items_item x
            JOIN items_item_region y ON x.id = y.item_id
        ) d ON d.item_id = a.id AND d.region_id = b.region_id
        LEFT JOIN (
            SELECT 
                y.item_id, 
                y.region_id, 
                DENSE_RANK() OVER (PARTITION BY y.region_id ORDER BY x.prominence DESC) AS region_prominence_rank
            FROM items_item x
            JOIN items_item_region y ON x.id = y.item_id
        ) e ON e.item_id = a.id AND e.region_id = b.region_id
        LEFT JOIN (
            SELECT 
                y.item_id, 
                y.region_id, 
                RANK() OVER (PARTITION BY y.region_id ORDER BY x.summitlog_count DESC) AS region_summits_rank
            FROM items_item x
            JOIN items_item_region y ON x.id = y.item_id
        ) f ON f.item_id = a.id AND f.region_id = b.region_id
        ORDER BY c.name, region_elevation_rank;
    """

    with transaction.atomic():
        with connection.cursor() as cursor:
            cursor.execute(DELETE_REGION_RANKING_QUERY)
        with connection.cursor() as cursor:
            cursor.execute(RESET_REGION_RANKING_ID_SEQUENCE_SQL)

        with connection.cursor() as cursor:
            cursor.execute(sql)
            columns = [col[0] for col in cursor.description]

            while True:
                rows = cursor.fetchmany(BATCH_SIZE)
                if not rows:
                    break

                records = [
                    ItemRegionRanking(
                        item_id=row[columns.index("item_id")],
                        region_name=row[columns.index("region_name")],
                        region_slug=row[columns.index("region_slug")],
                        country_slug=row[columns.index("country_slug")],
                        region_elevation_rank=row[columns.index("region_elevation_rank")],
                        region_prominence_rank=row[columns.index("region_prominence_rank")] - 1,
                        region_summits_rank=row[columns.index("region_summits_rank")]
                    )
                    for row in rows
                ]
                ItemRegionRanking.objects.bulk_create(records, ignore_conflicts=True, batch_size=BATCH_SIZE)

    print("Updated records successfully.")


def update_items_country_ranking():
    print("Updating items_itemcountryranking")
    sql = """
        SELECT 
            c.name AS country_name, 
            c.slug AS country_slug, 
            a.id AS item_id,
            COALESCE(d.country_elevation_rank, 1) AS country_elevation_rank, 
            COALESCE(e.country_prominence_rank, 1) AS country_prominence_rank, 
            COALESCE(f.country_summits_rank, 1) AS country_summits_rank
        FROM items_item a
        JOIN items_item_country b ON b.item_id = a.id
        JOIN cities_country c ON c.id = b.country_id
        LEFT JOIN (
            SELECT 
                y.item_id, 
                y.country_id, 
                RANK() OVER (PARTITION BY y.country_id ORDER BY x.elevation DESC) AS country_elevation_rank
            FROM items_item x
            JOIN items_item_country y ON x.id = y.item_id
        ) d ON d.item_id = a.id AND d.country_id = b.country_id
        LEFT JOIN (
            SELECT 
                y.item_id, 
                y.country_id, 
                DENSE_RANK() OVER (PARTITION BY y.country_id ORDER BY x.prominence DESC) AS country_prominence_rank
            FROM items_item x
            JOIN items_item_country y ON x.id = y.item_id
        ) e ON e.item_id = a.id AND e.country_id = b.country_id
        LEFT JOIN (
            SELECT 
                y.item_id, 
                y.country_id, 
                RANK() OVER (PARTITION BY y.country_id ORDER BY x.summitlog_count DESC) AS country_summits_rank
            FROM items_item x
            JOIN items_item_country y ON x.id = y.item_id
        ) f ON f.item_id = a.id AND f.country_id = b.country_id
        ORDER BY c.name, country_elevation_rank;
    """

    with transaction.atomic():
        with connection.cursor() as cursor:
            cursor.execute(DELETE_COUNTRY_RANKING_QUERY)
        with connection.cursor() as cursor:
            cursor.execute(RESET_COUNTRY_RANKING_ID_SEQUENCE_SQL)

        with connection.cursor() as cursor:
            cursor.execute(sql)
            columns = [col[0] for col in cursor.description]

            while True:
                rows = cursor.fetchmany(BATCH_SIZE)
                if not rows:
                    break

                records = [
                    ItemCountryRanking(
                        item_id=row[columns.index("item_id")],
                        country_name=row[columns.index("country_name")],
                        country_slug=row[columns.index("country_slug")],
                        country_elevation_rank=row[columns.index("country_elevation_rank")],
                        country_prominence_rank=row[columns.index("country_prominence_rank")] - 1,
                        country_summits_rank=row[columns.index("country_summits_rank")]
                    )
                    for row in rows
                ]
                ItemCountryRanking.objects.bulk_create(records, ignore_conflicts=True, batch_size=BATCH_SIZE)

    print("Updated records successfully.")


class Command(BaseCommand):
    help = 'Updates several tables related to item rankings'

    def handle(self, *args, **options):
        if datetime.datetime.today().weekday() == 5:
            print("Starting update items rankings job")
            update_items_region_ranking()
            update_items_country_ranking()
            print("Finished update items rankings job")
        else:
            print("Today is not Saturday. Skipping update items rankings job")

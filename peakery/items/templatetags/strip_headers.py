from django import template

register = template.Library()

def strip_headers(input):
    start_index = 0
    try:
        keyword = '<!-- CONTENT BEGIN -->'
        start_index = input.index(keyword)
    except:
        pass

    if start_index > 0:
        start_index = start_index + len(keyword)
        return input[start_index:]
    else:
        return input

register.filter('strip_headers', strip_headers)
from django.urls import path, re_path
from django.views.decorators.csrf import csrf_exempt

# Import views from peakery.items and peakery.api
from peakery.items.views import (
    main_peaks_search, log_climb_multi_simple, edit_climb_multi_simple,
    item_add, item_add_from_map, item_edit_highlights, item_edit_correction_alternate_names,
    approve_item_correction, reject_item_correction, item_correction_add_photo_only,
    admin_item_correction, view_item_correction, approve_item_duplicate,
    combine_routes, edit_route, delete_route, download_gpx,
    peak_edit_info, peak_edit_info_from_map, get_routes,
    summit_add_photo_caption, summit_delete_photo, upload_photo,
    s3_summit_photo_upload, s3_summit_photo_init, upload_gpx,
    s3_summit_gpx_upload, s3_log_climb_gpx_upload, s3_route_gpx_upload,
    s3_peak_photo_upload, upload_route_photo, upload_main_peak_photo,
    summit_view, summit_edit, summit_download_gpx, summit_badges_by_user,
    summit_delete, summit_comments, summit_comments_by_user, summit_comment_edit,
    summit_comment_edit_delete, fix_item_location, undo_fix_item_location,
    delete_peak_from_map, undo_delete_peak_from_map,
    admin_update_peak_elevation, admin_update_peak_prominence,
    admin_update_peak_range, admin_update_peak_to_challenge, admin_set_item_lists,
    peak_delete_main_photo
)
from peakery.api.views import mobile_peaks_map

urlpatterns = [
    path('', main_peaks_search, name='main_peaks_search'),

    # Temporary Android API stuff
    path('map/', csrf_exempt(mobile_peaks_map), name='android_mobile_peaks_map'),

    # Log and Edit Climbs
    path('log_climb/', csrf_exempt(log_climb_multi_simple), name='log_climb_multi_simple'),
    path('edit_climb/', csrf_exempt(edit_climb_multi_simple), name='edit_climb_multi_simple'),

    # Item Management
    path('add/', csrf_exempt(item_add), name='item_add'),
    path('add_from_map/', csrf_exempt(item_add_from_map), name='item_add_from_map'),

    # Edit Info Section
    path('edit_highlights/<int:peak_id>/', csrf_exempt(item_edit_highlights), name='item_edit_highlights'),
    path('edit/item_edit_correction_alternate_names/', csrf_exempt(item_edit_correction_alternate_names), name='item_edit_correction_alternate_names'),
    path('edit/approve/<int:correction_id>/', csrf_exempt(approve_item_correction), name='approve_item_correction'),
    path('edit/reject/<int:correction_id>/', csrf_exempt(reject_item_correction), name='reject_item_correction'),
    path('edit/approve_photo_only/<int:correction_id>/', csrf_exempt(item_correction_add_photo_only), name='item_correction_add_photo_only'),
    path('edit/admin_item/<int:correction_id>/', csrf_exempt(admin_item_correction), name='admin_item_correction'),
    path('edit/view_item/<int:correction_id>/', view_item_correction, name='view_item_correction'),
    path('duplicate/approve/<int:duplicate_id>/', csrf_exempt(approve_item_duplicate), name='approve_item_duplicate'),

    # Peak Photo Management
    path('delete-main-photo/', csrf_exempt(peak_delete_main_photo), name='peak_delete_main_photo'),

    # Route Management
    path('<int:peak_id>/routes/combine/', csrf_exempt(combine_routes), name='combine_routes'),
    path('<int:peak_id>/routes/edit/<int:route_id>/', csrf_exempt(edit_route), name='edit_route'),
    path('<int:peak_id>/routes/delete/<int:route_id>/', csrf_exempt(delete_route), name='delete_route'),
    path('<int:peak_id>/routes/download/<int:route_id>/', download_gpx, name='download_gpx'),

    # Peak Management
    path('<int:peak_id>/edit/', csrf_exempt(peak_edit_info), name='peak_edit_info'),
    path('<int:peak_id>/edit_from_map/', csrf_exempt(peak_edit_info_from_map), name='peak_edit_info_from_map'),
    path('get_routes/<int:peak_id>/', get_routes, name='get_routes'),

    # Summit Management
    path('summit/add_photo_caption/', csrf_exempt(summit_add_photo_caption), name='summit_add_photo_caption'),
    path('summit/delete_photo/', csrf_exempt(summit_delete_photo), name='summit_delete_photo'),
    path('summit/upload/', csrf_exempt(upload_photo), name='upload_photo'),
    path('summit/s3_summit_photo_upload/', csrf_exempt(s3_summit_photo_upload), name='s3_summit_photo_upload'),
    path('summit/s3_summit_photo_init/', csrf_exempt(s3_summit_photo_init), name='s3_summit_photo_init'),
    path('summit/uploadgpx/', csrf_exempt(upload_gpx), name='summit_upload_gpx'),
    path('summit/s3_summit_gpx_upload/', csrf_exempt(s3_summit_gpx_upload), name='s3_summit_gpx_upload'),
    path('summit/s3_log_climb_gpx_upload/', csrf_exempt(s3_log_climb_gpx_upload), name='s3_log_climb_gpx_upload'),
    path('summit/s3_route_gpx_upload/', csrf_exempt(s3_route_gpx_upload), name='s3_route_gpx_upload'),
    path('summit/s3_peak_photo_upload/', csrf_exempt(s3_peak_photo_upload), name='s3_peak_photo_upload'),
    path('summit/uploadroutephoto/', csrf_exempt(upload_route_photo), name='upload_route_photo'),
    path('summit/upload-main-peak-photo/', csrf_exempt(upload_main_peak_photo), name='upload_main_peak_photo'),
    path('summit/<int:summit_id>/', summit_view, name='summit_view'),
    path('summit/edit/<int:summit_id>/', csrf_exempt(summit_edit), name='summit_edit'),
    path('summit/download/<int:summit_id>/', csrf_exempt(summit_download_gpx), name='summit_download_gpx'),
    re_path(r'^summit/badges_by_user/(?P<username>[-@.\w]+)/$', summit_badges_by_user, name='summit_badges_by_user'),
    path('summit/delete/', csrf_exempt(summit_delete), name='summit_delete'),

    # Summit Comments
    path('summit/comments/<int:summit_id>/', csrf_exempt(summit_comments), name='summit_comments'),
    re_path(r'^summit/comments/by_user/(?P<username>[-@.\w]+)/$', summit_comments_by_user, name='summit_comments_by_user'),
    path('summit/comments/edit/<int:comment_id>/', csrf_exempt(summit_comment_edit), name='summit_comment_edit'),
    path('summit/comments/delete/', csrf_exempt(summit_comment_edit_delete), name='summit_comment_edit_delete'),

    # Location Fixes and Deletions
    path('fix_item_location/', csrf_exempt(fix_item_location), name='fix_item_location'),
    path('undo_fix_item_location/', csrf_exempt(undo_fix_item_location), name='undo_fix_item_location'),
    path('delete_peak_from_map/', csrf_exempt(delete_peak_from_map), name='delete_peak_from_map'),
    path('undo_delete_peak_from_map/', csrf_exempt(undo_delete_peak_from_map), name='undo_delete_peak_from_map'),

    # Admin Management
    path('admin_update_peak_elevation/', csrf_exempt(admin_update_peak_elevation), name='admin_update_peak_elevation'),
    path('admin_update_peak_prominence/', csrf_exempt(admin_update_peak_prominence), name='admin_update_peak_prominence'),
    path('admin_update_peak_range/', csrf_exempt(admin_update_peak_range), name='admin_update_peak_range'),
    path('admin_update_peak_to_challenge/', csrf_exempt(admin_update_peak_to_challenge), name='admin_update_peak_to_challenge'),
    path('admin/set_item_lists/<int:item_id>/', csrf_exempt(admin_set_item_lists), name='admin_set_item_lists'),
]
import math

import requests
from django.http import HttpResponse, HttpResponseNotFound
from django.views.decorators.cache import cache_page
from pyproj import Transformer


@cache_page(60 * 60)
def mapbox_southafrica_ngi_proxy(request, z, x, y):
    """
    Proxy view for South Africa NGI tiles to bypass CORS restrictions.
    Handles TMS coordinate system (inverted Y) conversion.
    """
    try:
        # Convert TMS Y coordinate to standard Y coordinate
        # TMS uses inverted Y: y_tms = (2^z - 1) - y_standard
        # So: y_standard = (2^z - 1) - y_tms
        z_int = int(z)
        y_int = int(y)
        max_y = (2 ** z_int) - 1
        inverted_y = max_y - y_int

        # Construct the URL for the South Africa NGI tile server
        tile_url = f"https://htonl.dev.openstreetmap.org/ngi-tiles/tiles/50k/{z}/{x}/{inverted_y}.png"

        # Fetch the tile from the remote server
        response = requests.get(tile_url, timeout=10)

        if response.status_code == 200:
            # Return the tile with proper headers
            http_response = HttpResponse(response.content, content_type='image/png')
            return http_response
        else:
            # Return 404 if tile not found
            return HttpResponseNotFound("Tile not found")

    except Exception as e:
        # Return 500 for any other errors
        return HttpResponse(f"Error fetching tile: {str(e)}", status=500)


@cache_page(60 * 60)
def andorra_ideandorra_proxy(request, z, x, y):
    """
    Proxy view for Andorra IDE WMS tiles to bypass CORS restrictions.
    Handles coordinate conversion from Web Mercator (EPSG:3857) to WGS84 (EPSG:4326).
    """
    try:
        z_int = int(z)
        x_int = int(x)
        y_int = int(y)

        # Convert tile coordinates to Web Mercator bounds (EPSG:3857)
        def tile_to_web_mercator_bbox(x, y, z):
            n = 2.0 ** z
            lon_deg = x / n * 360.0 - 180.0
            lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * y / n)))
            lat_deg = math.degrees(lat_rad)

            lon_deg_max = (x + 1) / n * 360.0 - 180.0
            lat_rad_max = math.atan(math.sinh(math.pi * (1 - 2 * (y + 1) / n)))
            lat_deg_max = math.degrees(lat_rad_max)

            return lon_deg, lat_deg_max, lon_deg_max, lat_deg

        # Get bounding box in WGS84 (EPSG:4326)
        min_lon, min_lat, max_lon, max_lat = tile_to_web_mercator_bbox(x_int, y_int, z_int)

        # Construct WMS request URL
        wms_url = "https://www.ideandorra.ad/Serveis/wms_10k2003raster/wms"
        params = {
            'service': 'WMS',
            'request': 'GetMap',
            'layers': 'raster_10k_2003',
            'styles': '',
            'format': 'image/jpeg',
            'transparent': 'false',
            'version': '1.1.1',
            'width': '256',
            'height': '256',
            'srs': 'EPSG:4326',
            'bbox': f"{min_lon},{min_lat},{max_lon},{max_lat}"
        }

        # Fetch the tile from the WMS server
        response = requests.get(wms_url, params=params, timeout=10)

        if response.status_code == 200:
            # Return the tile with proper headers
            http_response = HttpResponse(response.content, content_type='image/jpeg')
            http_response['Cache-Control'] = 'public, max-age=3600'  # Cache for 1 hour
            return http_response
        else:
            # Return 404 if tile not found
            return HttpResponseNotFound("Tile not found")

    except Exception as e:
        # Return 500 for any other errors
        return HttpResponse(f"Error fetching tile: {str(e)}", status=500)


@cache_page(60 * 60)
def iceland_lmi_proxy(request, z, x, y):
    """
    Proxy view for Iceland LMÍ WMTS tiles to bypass CORS restrictions.
    Handles coordinate conversion from Web Mercator (EPSG:3857) to EPSG:3057 (Iceland Lambert 1993).
    """
    try:
        z_int = int(z)
        x_int = int(x)
        y_int = int(y)

        # Convert tile coordinates to Web Mercator bounds (EPSG:3857)
        def tile_to_web_mercator_bbox(x, y, z):
            n = 2.0 ** z
            lon_deg = x / n * 360.0 - 180.0
            lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * y / n)))
            lat_deg = math.degrees(lat_rad)

            lon_deg_max = (x + 1) / n * 360.0 - 180.0
            lat_rad_max = math.atan(math.sinh(math.pi * (1 - 2 * (y + 1) / n)))
            lat_deg_max = math.degrees(lat_rad_max)

            return lon_deg, lat_deg_max, lon_deg_max, lat_deg

        # Get bounding box in WGS84 (EPSG:4326)
        min_lon, min_lat, max_lon, max_lat = tile_to_web_mercator_bbox(x_int, y_int, z_int)

        # Transform from WGS84 (EPSG:4326) to Iceland Lambert 1993 (EPSG:3057)
        transformer = Transformer.from_crs("EPSG:4326", "EPSG:3057", always_xy=True)

        # Transform corner points
        min_x, min_y = transformer.transform(min_lon, min_lat)
        max_x, max_y = transformer.transform(max_lon, max_lat)

        # Construct WMTS request URL for Iceland LMÍ
        # The original URL template: https://gis.lmi.is/mapcache/isn93/wmts?layer=LMI_Kort&style=default&tilematrixset=EPSG%3A3057&Service=WMTS&Request=GetTile&Version=1.0.0&Format=image%2Fpng&TileMatrix={z}&TileCol={x}&TileRow={y}

        # For WMTS with EPSG:3057, we need to calculate the tile coordinates in that projection
        # This is complex, so let's try a simpler approach using WMS with bbox
        wms_url = "https://gis.lmi.is/mapcache/isn93/wms"
        params = {
            'service': 'WMS',
            'request': 'GetMap',
            'layers': 'LMI_Kort',
            'styles': 'default',
            'format': 'image/png',
            'transparent': 'false',
            'version': '1.1.1',
            'width': '256',
            'height': '256',
            'srs': 'EPSG:3057',
            'bbox': f"{min_x},{min_y},{max_x},{max_y}"
        }

        # Fetch the tile from the WMS server
        response = requests.get(wms_url, params=params, timeout=10)

        if response.status_code == 200:
            # Return the tile with proper headers
            http_response = HttpResponse(response.content, content_type='image/png')
            http_response['Cache-Control'] = 'public, max-age=3600'  # Cache for 1 hour
            return http_response
        else:
            # Return 404 if tile not found
            return HttpResponseNotFound("Tile not found")

    except Exception as e:
        # Return 500 for any other errors
        return HttpResponse(f"Error fetching tile: {str(e)}", status=500)

{% extends "base.html" %}
{% load avatar_tags %}


{% block title %}Top contributors{% endblock %}

{% block content %}

  <div id="about">
    <div class="header">
      <h1 id = "contributors-headline">Top Contributors</h1>
      <h6 id = "contributors-subtext">A permanent shrine to recognize peakery's top 50 contributors as they work to make peakery a better resource for everyone.  Want to contribute?  On any peak page, click the 'edit info' link in the Snapshot box to add peak data corrections.</h6>
    </div>
    
    <div class="body top-contributors">
      <table>
        {% for account in contributors %}
          <tr style="height: 170px;">
            <td style="width: 4em; text-align: right;"><h2 style="position: relative; top:-10px;">#{{ forloop.counter }}</h2></td>
            <td style="vertical-align: top;">
              <a href="{% url "user_profile" account %}" class="avatarLink" style="margin: 1em;">
                {% avatar account 130 %}
              </a>
            </td>
            <td style="position: relative; top:-10px;">
              <h2><a href="{{account.get_absolute_url}}">{{ account }}</a> &nbsp; {{ account.corrections_count }} corrections</h2>
              <p>
                Most recent correction for 
                <a href="{{ account.items_corrections.latest.item.get_absolute_url }}">{{ account.items_corrections.latest.item }}</a>

                {% with account.items_corrections.latest.item.get_ubication_names as ubication_names %}
                  {% if ubication_names %}
                    in
                    {% for location in ubication_names %}
                      <a href="{{ location.get_absolute_url }}">{{ location.get_ubication_onlyname_title }}</a>
                      {% if not forloop.last %} / {% endif %}
                    {% endfor %}
                  {% endif %}
                {% endwith %}
              </p>
            </td>
          </tr>
        {% endfor %}
      </table>
    </div>
    

  </div>

{% endblock %}

# Generated by Django 4.2.11 on 2024-07-30 17:09

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('cities', '0003_alter_cityhighlight_user_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='cityhighlight',
            name='user',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='city_highlights', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='cityhighlightloggroup',
            name='user',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='city_highlights_log_group', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='continenthighlight',
            name='user',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='continent_highlights', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='continenthighlightloggroup',
            name='user',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='continent_highlights_log_group', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='countryhighlight',
            name='user',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='country_highlights', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='countryhighlightloggroup',
            name='user',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='country_highlights_log_group', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='regionhighlight',
            name='user',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='region_highlights', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='regionhighlightloggroup',
            name='user',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='region_highlights_log_group', to=settings.AUTH_USER_MODEL),
        ),
    ]

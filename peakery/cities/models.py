from django.contrib.gis.db import models
from django.contrib.gis.geos import Point
from django.template.defaultfilters import slugify
from django.contrib.auth.models import User
from peakery.django_extensions.db.fields import CreationDateTimeField, ModificationDateTimeField
from peakery.items.utils import commify
from django.db.models import Manager as GeoManager


CONTINENTS = {
	"NA":"North-America",
	"AS":"Asia",
	"EU":"Europe",
	"AF":"Africa",
	"AN":"Antarctica",
	"SA":"South-America",
	"OC":"Oceania",
}


class Continent(models.Model):
	name = models.CharField(max_length = 200)
	code = models.CharField(max_length = 2, db_index=True)
	slug = models.CharField(max_length = 255, unique=True)
	lat = models.FloatField(blank=True, null=True, verbose_name='Latitude')
	long = models.FloatField(blank=True, null=True, verbose_name='Longitude')

	class Meta:
		ordering = ('name',)

	def get_ubication_onlyname_title(self):
		return self.__unicode__()

	def get_meta_description(self, peak_count):
		if peak_count > 1:
			description = "Includes %s %s mountains. See hiking info, trail maps, and trip reports for every peak." % (commify(int(peak_count)), self.name)
		else:
			description = "Includes %s mountains. See hiking info, trail maps, and trip reports for every peak." % self.name
		return description

	def get_meta_description_map(self, peak_count):
		if peak_count > 1:
			description = "Map of %s %s mountains showing elevation, prominence, popularity, and difficulty" % (commify(int(peak_count)), self.name)
		else:
			description = "Map of %s mountains showing elevation, prominence, popularity, and difficulty" % self.name
		return description

	def get_meta_description_peaks(self, peak_count):
		if peak_count > 1:
			description = "List of %s %s mountains showing elevation, prominence, and popularity" % (commify(int(peak_count)), self.name)
		else:
			description = "List of %s mountains showing elevation, prominence, and popularity" % self.name
		return description

	def get_meta_description_summits(self, summit_count):
		if summit_count > 1:
			description = "%s trip reports from %s mountains" % (commify(int(summit_count)), self.name)
		else:
			description = "Trip reports from %s mountains" % self.name
		return description

	def get_meta_description_challenges(self, challenge_count):
		if challenge_count > 1:
			description = "%s Peak Challenges in %s. Peak Challenges invite you to climb special collections of mountains ranging from gentle hikes to technical climbs." % (commify(int(challenge_count)), self.name)
		else:
			description = "Peak Challenges in %s. Peak Challenges invite you to climb special collections of mountains ranging from gentle hikes to technical climbs." % self.name
		return description


class ContinentHighlight(models.Model):
	user = models.ForeignKey(User, related_name='continent_highlights', on_delete=models.SET_DEFAULT, default=1)
	continent = models.ForeignKey(Continent, related_name='highlights', on_delete=models.DO_NOTHING)
	highlight = models.TextField()
	created = CreationDateTimeField()
	modified = ModificationDateTimeField()

	def __unicode__(self):
		return str(self.highlight)

	def __str__(self):
		return str(self.highlight)


class ContinentHighlightLogGroup(models.Model):
	user = models.ForeignKey(User, related_name='continent_highlights_log_group', on_delete=models.SET_DEFAULT, default=1)
	continent = models.ForeignKey(Continent, related_name='highlights_log_group', on_delete=models.DO_NOTHING)
	log_date = CreationDateTimeField()

	def __unicode__(self):
		return str(self.log_date)

class ContinentHighlightLog(models.Model):
	log_group_id = models.ForeignKey(ContinentHighlightLogGroup, related_name='continent_highlights_log', on_delete=models.DO_NOTHING)
	highlight = models.TextField()

	def __unicode__(self):
		return str(self.highlight)


class Country(models.Model):
	name = models.CharField(max_length = 200)
	code = models.CharField(max_length = 2, db_index=True)
	population = models.IntegerField()
	continent = models.CharField(max_length = 2)
	tld = models.CharField(max_length = 5, unique=True)
	slug = models.CharField(max_length = 255, unique=True)
	lat = models.FloatField(blank=True, null=True, verbose_name='Latitude')
	long = models.FloatField(blank=True, null=True, verbose_name='Longitude')
	map_zoom = models.IntegerField(null = True, blank = True)
	show_in_challenges = models.BooleanField(default=False,verbose_name='Show In World Challenges',help_text='Indicate if a Country should display on World Challenges view.')

	objects = GeoManager()

	class Meta:
		verbose_name_plural = "Countries"
		ordering = ('name',)
	def __unicode__(self):
		return self.name

	def __str__(self):
		return self.name

	@property
	def hierarchy(self):
		return [self]

	def get_total_items(self):
		return self.country_items.all().count()

	def get_total_without_region(self):
		raise Exception("obsolete")

	def make_slug(self):
		self.slug = slugify(self.name)
		self.save()

	def get_slug(self):
		return '%s-mountains' % self.slug

	def get_continent_slug(self):
		return CONTINENTS.get(self.continent)

	def get_continent_name(self):
		return CONTINENTS.get(self.continent).replace('-', ' ')

	def get_absolute_url(self):
		return '/%s-mountains/' % (self.slug)

	def get_absolute_url_from_slug(country_slug):
		return '/%s-mountains/' % (country_slug)

	def get_ubication_onlyname_title(self):
		return self.__unicode__()

	def get_meta_description(self, peak_count):
		if peak_count > 1:
			description = "Includes %s %s mountains. See hiking info, trail maps, and trip reports for every peak." % (commify(int(peak_count)), self.name)
		else:
			description = "Includes %s mountains. See hiking info, trail maps, and trip reports for every peak." % self.name
		return description

	def get_meta_description_map(self, peak_count):
		if peak_count > 1:
			description = "Map of %s %s mountains showing elevation, prominence, popularity, and difficulty" % (commify(int(peak_count)), self.name)
		else:
			description = "Map of %s mountains showing elevation, prominence, popularity, and difficulty" % self.name
		return description

	def get_meta_description_peaks(self, peak_count):
		if peak_count > 1:
			description = "List of %s %s mountains showing elevation, prominence, and popularity" % (commify(int(peak_count)), self.name)
		else:
			description = "List of %s mountains showing elevation, prominence, and popularity" % self.name
		return description

	def get_meta_description_summits(self, summit_count):
		if summit_count > 1:
			description = "%s trip reports from %s mountains" % (commify(int(summit_count)), self.name)
		else:
			description = "Trip reports from %s mountains" % self.name
		return description

	def get_meta_description_challenges(self, challenge_count):
		if challenge_count > 1:
			description = "%s Peak Challenges in %s. Peak Challenges invite you to climb special collections of mountains ranging from gentle hikes to technical climbs." % (commify(int(challenge_count)), self.name)
		else:
			description = "Peak Challenges in %s. Peak Challenges invite you to climb special collections of mountains ranging from gentle hikes to technical climbs." % self.name
		return description


class CountryHighlight(models.Model):
	user = models.ForeignKey(User, related_name='country_highlights', on_delete=models.SET_DEFAULT, default=1)
	country = models.ForeignKey(Country, related_name='highlights', on_delete=models.DO_NOTHING)
	highlight = models.TextField()
	created = CreationDateTimeField()
	modified = ModificationDateTimeField()

	def __unicode__(self):
		return str(self.highlight)


class CountryHighlightLogGroup(models.Model):
	user = models.ForeignKey(User, related_name='country_highlights_log_group', on_delete=models.SET_DEFAULT, default=1)
	country = models.ForeignKey(Country, related_name='highlights_log_group', on_delete=models.DO_NOTHING)
	log_date = CreationDateTimeField()

	def __unicode__(self):
		return str(self.log_date)


class CountryHighlightLog(models.Model):
	log_group_id = models.ForeignKey(CountryHighlightLogGroup, related_name='country_highlights_log', on_delete=models.DO_NOTHING)
	highlight = models.TextField()

	def __unicode__(self):
		return str(self.highlight)


class Region(models.Model):
	name = models.CharField(max_length = 200)
	slug = models.CharField(max_length = 200, db_index=True)
	code = models.CharField(max_length = 10, db_index=True)
	country = models.ForeignKey(Country, related_name='regions', on_delete=models.DO_NOTHING)
	lat = models.FloatField(blank=True, null=True, verbose_name='Latitude')
	long = models.FloatField(blank=True, null=True, verbose_name='Longitude')
	map_zoom = models.IntegerField(null = True, blank = True)
	show_in_challenges = models.BooleanField(default=False,verbose_name='Show In World Challenges',help_text='Indicate if a Region should display on World Challenges view.')
	google_region_name = models.CharField(max_length = 255, blank=True, null=True)
	google_region_name_alt = models.CharField(max_length = 255, blank=True, null=True)

	objects = GeoManager()

	class Meta:
		ordering = ('name',)

	def get_total_items(self):
		return self.region_items.all().count()

	def __unicode__(self):
		return "%s, %s" % (self.name, self.country)

	def __str__(self):
		return "%s, %s" % (self.name, self.country)

	def get_absolute_url(self):
		return '/%s-mountains/%s/' % (self.country.slug, self.slug)

	def get_absolute_url_from_slug(country_slug, region_slug):
		return '/%s-mountains/%s/' % (country_slug, region_slug)

	@property
	def hierarchy(self):
		list = self.country.hierarchy
		list.append(self)
		return list

	def get_ubication_onlyname_title(self):
		return '%s, %s' % (self.name, self.country.name)

	def get_ubication_onlyregionname_title(self):
		return '%s' % self.name

	def get_meta_description(self, peak_count):
		if peak_count > 1:
			description = "Includes %s %s mountains. See hiking info, trail maps, and trip reports for every peak." % (commify(int(peak_count)), self.name)
		else:
			description = "Includes %s mountains. See hiking info, trail maps, and trip reports for every peak." % self.name
		return description

	def get_meta_description_map(self, peak_count):
		if peak_count > 1:
			description = "Map of %s %s mountains showing elevation, prominence, popularity, and difficulty" % (commify(int(peak_count)), self.name)
		else:
			description = "Map of %s mountains showing elevation, prominence, popularity, and difficulty" % self.name
		return description

	def get_meta_description_peaks(self, peak_count):
		if peak_count > 1:
			description = "List of %s %s mountains showing elevation, prominence, and popularity" % (commify(int(peak_count)), self.name)
		else:
			description = "List of %s mountains showing elevation, prominence, and popularity" % self.name
		return description

	def get_meta_description_summits(self, summit_count):
		if summit_count > 1:
			description = "%s trip reports from %s mountains" % (commify(int(summit_count)), self.name)
		else:
			description = "Trip reports from %s mountains" % self.name
		return description

	def get_meta_description_challenges(self, challenge_count):
		if challenge_count > 1:
			description = "%s Peak Challenges in %s. Peak Challenges invite you to climb special collections of mountains ranging from gentle hikes to technical climbs." % (commify(int(challenge_count)), self.name)
		else:
			description = "Peak Challenges in %s. Peak Challenges invite you to climb special collections of mountains ranging from gentle hikes to technical climbs." % self.name
		return description


class RegionHighlight(models.Model):
	user = models.ForeignKey(User, related_name='region_highlights', on_delete=models.SET_DEFAULT, default=1)
	region = models.ForeignKey(Region, related_name='highlights', on_delete=models.DO_NOTHING)
	highlight = models.TextField()
	created = CreationDateTimeField()
	modified = ModificationDateTimeField()

	def __unicode__(self):
		return str(self.highlight)


class RegionHighlightLogGroup(models.Model):
	user = models.ForeignKey(User, related_name='region_highlights_log_group', on_delete=models.SET_DEFAULT, default=1)
	region = models.ForeignKey(Region, related_name='highlights_log_group', on_delete=models.DO_NOTHING)
	log_date = CreationDateTimeField()

	def __unicode__(self):
		return str(self.log_date)


class RegionHighlightLog(models.Model):
	log_group_id = models.ForeignKey(RegionHighlightLogGroup, related_name='region_highlights_log', on_delete=models.DO_NOTHING)
	highlight = models.TextField()

	def __unicode__(self):
		return str(self.highlight)


class CityManager(models.Manager):

#Roll back changes in method 12/03/2012
	def nearest_to_point(self, point, limit=None):
		if not limit:
			ret = self.distance(point).order_by('distance')[0]
		else:
			ret = self.distance(point).filter(location__distance_lt = (point, limit)).order_by('distance')[:1]
			if ret:
				ret = ret[0]
			else:
				ret = None
		return ret

	def nearest_to(self, lat, lon , limit = None):
		p = Point(float(lat), float(lon))
		return self.nearest_to_point(p, limit=limit)


class City(models.Model):
	name = models.CharField(max_length = 200)
	slug = models.CharField(null = True, blank = True, max_length = 200, db_index=True)
	region = models.ForeignKey(Region, related_name='cities', on_delete=models.DO_NOTHING)
	location = models.PointField()
	location_x = models.DecimalField(max_digits = 11, decimal_places = 8, db_index=True, verbose_name='Latitude')
	location_y = models.DecimalField(max_digits = 11, decimal_places = 8, db_index=True, verbose_name='Longitude')
	population = models.IntegerField()

	objects = CityManager()

	class Meta:
		ordering = ('name',)
		verbose_name_plural = 'cities'

	def __unicode__(self):
		return "%s, %s" % (self.name, self.region)

	def __str__(self):
		return "%s, %s" % (self.name, self.region)

	@property
	def hierarchy(self):
		list = self.region.hierarchy
		list.append(self)
		return list


class CityHighlight(models.Model):
	user = models.ForeignKey(User, related_name='city_highlights', on_delete=models.SET_DEFAULT, default=1)
	city = models.ForeignKey(City, related_name='highlights', on_delete=models.DO_NOTHING)
	highlight = models.TextField()
	created = CreationDateTimeField()
	modified = ModificationDateTimeField()

	def __unicode__(self):
		return str(self.highlight)


class CityHighlightLogGroup(models.Model):
	user = models.ForeignKey(User, related_name='city_highlights_log_group', on_delete=models.SET_DEFAULT, default=1)
	city = models.ForeignKey(City, related_name='highlights_log_group', on_delete=models.DO_NOTHING)
	log_date = CreationDateTimeField()

	def __unicode__(self):
		return str(self.log_date)


class CityHighlightLog(models.Model):
	log_group_id = models.ForeignKey(CityHighlightLogGroup, related_name='city_highlights_log', on_delete=models.DO_NOTHING)
	highlight = models.TextField()

	def __unicode__(self):
		return str(self.highlight)


class District(models.Model):
	name = models.CharField(max_length = 200)
	slug = models.CharField(max_length = 200, db_index=True)
	city = models.ForeignKey(City, on_delete=models.DO_NOTHING)
	location = models.PointField()
	population = models.IntegerField()
	location_x = models.DecimalField(max_digits = 11, decimal_places = 8, db_index=True)
	location_y = models.DecimalField(max_digits = 11, decimal_places = 8, db_index=True)
	objects = GeoManager()

	def __unicode__(self):
		return u"%s, %s" % (self.name, self.city)

	def __str__(self):
		return u"%s, %s" % (self.name, self.city)

	@property
	def hierarchy(self):
		list = self.city.hierarchy
		list.append(self)
		return list

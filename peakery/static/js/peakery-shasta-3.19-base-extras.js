var showAlerts = false;
var nextAlertsPage = 1;
var allNewsLoaded = false;
$(function(){

    $(window).resize(function() {
        //resize alerts for mobile widths
        var window_width = $(window).width();
        var wide_middle_width = window_width - 145;
        var middle_width = window_width - 235;
        if (window_width < 768) {
            $('#mobile-alerts-ul').find('.nav-alerts-li-wide-middle').width(wide_middle_width);
            $('#mobile-alerts-ul').find('.nav-alerts-li-middle').width(middle_width);
        }
    });

    //allow click anywhere in sub-menu rows
    $('.navbar-li-link').on('click', function(e) {
        var url = $(this).find('a').attr('href');
        var target = $(this).find('a').attr('target');
        if (!target) {
            target = '';
        }
        if (url) {
            openNavUrl(url, target);
        }
    });
    $('.navbar-li-link').on('mouseenter', function(e) {
        $(this).find('a').addClass('navbar-link-hover');
    });
    $('.navbar-li-link').on('mouseleave', function(e) {
        $(this).find('a').removeClass('navbar-link-hover');
    });

    // DESKTOP SEARCHBOX //
    var submitIcon = $('.searchbox-icon');
    var closeIcon = $('.searchbox-close-icon');
    var inputBox = $('.searchbox-input');
    var searchBox = $('.searchbox');
    var isOpen = false;
    $('#q1').autocomplete({
        serviceUrl: '/api/peaks/suggestions',
        minChars: 3,
        onSelect: function (suggestion) {
            //alert('You selected: ' + suggestion.value + ', ' + suggestion.data);
            window.location.href = suggestion.data;
            //window.location.href = '/peaks/?q='+suggestion.value;
        },
        onSearchStart: function (query) {
            $('#searchbox-icon').html('<i class="fa fa-spinner fa-spin"></i>');
        },
        onSearchComplete: function (query, suggestions) {
            $('#searchbox-icon').html('<i class="fas fa-times-circle"></i>');
        }
    });

    submitIcon.mouseup(function(){
        return false;
    });
    searchBox.mouseup(function(){
        return false;
    });

    $('#searchbox-icon').on('click', function(e) {
        $('#q1').autocomplete('hide');
        $('#q1').val('');
        $('#searchbox-icon').html('');
        $('#q1').focus();
    });

    // DROP NAV ON HOVER OR CLICK
    $('#navbar-you-dropdown-toggle').mouseenter(function() {
        $('.nav-you-dropdown').show();
        $('#navbar-you-avatar').css('-webkit-filter','brightness(1.1) saturate(1.5)');
        $('#navbar-you-avatar').css('-webkit-transition','brightness(1.1) saturate(1.5)');
    });
    $('#navbar-you-dropdown-toggle').mouseleave(function() {
        $('.nav-you-dropdown').hide();
        $('#navbar-you-avatar').css('-webkit-filter','none');
        $('#navbar-you-avatar').css('-webkit-transition','none');
    });
    $('.nav-you-dropdown').mouseenter(function() {
        $('.nav-you-dropdown').show();
        $('#navbar-you-avatar').css('-webkit-filter','brightness(1.1) saturate(1.5)');
        $('#navbar-you-avatar').css('-webkit-transition','brightness(1.1) saturate(1.5)');
    });
    $('.nav-you-dropdown').mouseleave(function() {
        $('.nav-you-dropdown').hide();
        $('#navbar-you-avatar').css('-webkit-filter','none');
        $('#navbar-you-avatar').css('-webkit-transition','none');
    });

    $('#nav-more-dropdown-link').mouseenter(function() {
        $('.nav-more-dropdown').show();
        $('#nav-more-dropdown-link').css('color','#fff');
    });
    $('#nav-more-dropdown-link').mouseleave(function() {
        $('.nav-more-dropdown').hide();
        $('#nav-more-dropdown-link').css('color','#ccc');
    });
    $('.nav-more-dropdown').mouseenter(function() {
        $('.nav-more-dropdown').show();
        $('#nav-more-dropdown-link').css('color','#fff');
    });
    $('.nav-more-dropdown').mouseleave(function() {
        $('.nav-more-dropdown').hide();
        $('#nav-more-dropdown-link').css('color','#ccc');
    });

    $('#nav-alerts').on('click touchstart', function(e) {
        if (!$('#nav-alerts-dropdown-div').is(':visible') && showAlerts) {
            $('body').css('overflow','hidden');
            $('#nav-alerts-dropdown-div').toggle();
        }
        //reset news count if necessary
        var news_count = $('#nav-alerts-count').html();
        if (news_count != '') {
          $.getJSON('/api/user/reset_news/', function(data) {
              if (data.result == 'success') {
                  //console.log('success');
                  $(".nav-member-news-count").addClass("nav-hidden");
                  $(".nav-member-news-count").html('');
                  $('#mobile-nav-alerts').hide();
                  $('.fa-bell').removeClass('faa-ring').removeClass('animated');
              }
          });
        }
    });

    $('#nav-alerts-container').mouseleave(function() {
        if ($('#nav-alerts-dropdown-div').is(':visible') && showAlerts) {
            $('body').css('overflow','auto');
            $('#nav-alerts-dropdown-div').toggle();
        }
        $('#nav-alerts').removeClass('nav-alerts-hover');
    });

    $('.nav-alerts-dropdown-tab').mouseleave(function() {
        if ($('#nav-alerts-dropdown-div').is(':visible') && showAlerts) {
            $('body').css('overflow','auto');
            $('#nav-alerts-dropdown-div').toggle();
        }
        $('#nav-alerts').removeClass('nav-alerts-hover');
    });

    $('#nav-alerts-container').mouseenter(function() {
        if (!$('#nav-alerts-dropdown-div').is(':visible') && showAlerts) {
            $('#nav-alerts-dropdown-div').toggle();
        }
        $('#nav-alerts').addClass('nav-alerts-hover');
    });

    $('.nav-alerts-dropdown-tab').mouseenter(function() {
        if (!$('#nav-alerts-dropdown-div').is(':visible') && showAlerts) {
            $('#nav-alerts-dropdown-div').toggle();
        }
        $('#nav-alerts').addClass('nav-alerts-hover');
    });

    $('#slideout-nav-alerts-btn').on('click touchstart', function(e) {
        $('#mobile-alerts-modal').modal('show');
        //resize for mobile widths
        var window_width = $(window).width();
        var wide_middle_width = window_width - 145;
        var middle_width = window_width - 235;
        if (window_width < 768) {
            $('#mobile-alerts-ul').find('.nav-alerts-li-wide-middle').width(wide_middle_width);
            $('#mobile-alerts-ul').find('.nav-alerts-li-middle').width(middle_width);
        }
        //reset news count if necessary
        var news_count = $('#nav-alerts-count').html();
        if (news_count != '') {
          $.getJSON('/api/user/reset_news/', function(data) {
              if (data.result == 'success') {
                  //console.log('success');
                  $(".nav-member-news-count").addClass("nav-hidden");
                  $(".nav-member-news-count").html('');
                  $('#mobile-nav-alerts').hide();
                  $('.fa-bell').removeClass('faa-ring').removeClass('animated');
              }
          });
        }
    });

    $('#mobile-nav-alerts').on('click touchstart', function(e) {
        $('#mobile-alerts-modal').modal('show');
        //resize for mobile widths
        var window_width = $(window).width();
        var wide_middle_width = window_width - 145;
        var middle_width = window_width - 235;
        if (window_width < 768) {
            $('#mobile-alerts-ul').find('.nav-alerts-li-wide-middle').width(wide_middle_width);
            $('#mobile-alerts-ul').find('.nav-alerts-li-middle').width(middle_width);
        }
        //reset news count if necessary
        var news_count = $('#nav-alerts-count').html();
        if (news_count != '') {
          $.getJSON('/api/user/reset_news/', function(data) {
              if (data.result == 'success') {
                  //console.log('success');
                  $(".nav-member-news-count").addClass("nav-hidden");
                  $(".nav-member-news-count").html('');
                  $('#mobile-nav-alerts').hide();
                  $('.fa-bell').removeClass('faa-ring').removeClass('animated');
              }
          });
        }
    });

    $('#nav-peaks-dropdown-link').mouseenter(function() {
        $('.nav-peaks-dropdown').show();
        $('#nav-peaks-dropdown-link').css('color','#fff');
    });
    $('#nav-peaks-dropdown-link').mouseleave(function() {
        $('.nav-peaks-dropdown').hide();
        $('#nav-peaks-dropdown-link').css('color','#ccc');
    });
    $('.nav-peaks-dropdown').mouseenter(function() {
        $('.nav-peaks-dropdown').show();
        $('#nav-peaks-dropdown-link').css('color','#fff');
    });
    $('.nav-peaks-dropdown').mouseleave(function() {
        $('.nav-peaks-dropdown').hide();
        $('#nav-peaks-dropdown-link').css('color','#ccc');
    });

    $('#nav-log-your-climb').mouseenter(function() {
        $('.log-climb-dropdown').show();
        $('#nav-log-your-climb').css('background','#F24100');
    });
    $('#nav-log-your-climb').mouseleave(function() {
        $('.log-climb-dropdown').hide();
        $('#nav-log-your-climb').css('background','#c13818');
    });
    $('.log-climb-dropdown').mouseenter(function() {
        $('.log-climb-dropdown').show();
        $('#nav-log-your-climb').css('background','#F24100');
    });
    $('.log-climb-dropdown').mouseleave(function() {
        $('.log-climb-dropdown').hide();
        $('#nav-log-your-climb').css('background','#c13818');
    });

    //alerts scrolling
    $("#nav-alerts-container").scroll(function() {
        var $this = $(this);
        var $results = $("#nav-alerts-ul");

        if (!$this.data("loading") && !allNewsLoaded) {

            if ($this.scrollTop() + $this.height() >= $results.height()) {
                loadNewsAlerts(nextAlertsPage);
            }
        }
    });
    //mobile alerts scrolling
    $("#mobile-alerts-container").scroll(function() {
        var $this = $(this);
        var $results = $("#mobile-alerts-ul");

        if (!$this.data("loading") && !allNewsLoaded) {

            if ($this.scrollTop() + $this.height() >= $results.height()) {
                loadNewsAlerts(nextAlertsPage);
            }
        }
    });

    $(document).mouseup(function(e) {
        var container = $('#nav-alerts-dropdown-div');
        showAlerts = false;
        var alertsVisible = $('#nav-alerts-dropdown-div').is(':visible');
        // if the target of the click isn't the container nor a descendant of the container
        if (!container.is(e.target) && container.has(e.target).length === 0) {
            if (!alertsVisible && (e.target.id == 'navbar-alerts' || e.target.id == 'nav-alerts' || e.target.id == 'nav-alerts-bell' || e.target.id == 'nav-alerts-count')) {
                showAlerts = true;
            }
            container.hide();
        }
    });

    $('#mobile-searchbox-icon').on('click touchstart', function(e) {
        $('#mobileq1').val('');
    });

    $('#mobileq1').on('input', function(e) {
        var temp = $('#mobileq1').val();
        if (temp.length == 0) {
            $('#mobile-searchbox-icon').html('<i class="fa fa-search"></i>');
        } else {
            $('#mobile-searchbox-icon').html('<i class="fas fa-times-circle"></i>');
        }
    });

    $('.toggle').on('click', function(e) {
        e.preventDefault();

        var $this = $(this);

        if ($this.next().hasClass('slideout-accordion-show')) {
            $this.next().removeClass('slideout-accordion-show');
            $this.next().slideUp(400);
            $this.find('.slideout-nav-rotate').toggleClass('slideout-nav-down');
        } else {
            $this.next().toggleClass('slideout-accordion-show');
            $this.next().slideDown(400);
            $this.find('.slideout-nav-rotate').toggleClass('slideout-nav-down');
        }
    });
    
    $.getJSON('/api/user/navdata/', function(data) {
        var username = data.username;
        var profile_url = data.profile_url;
        var avatar_url = data.avatar_url;
        var news_count = data.news_count;
        var is_logged_in = data.is_logged_in;
        var is_admin_user = data.is_admin_user;
        var is_mobile_app = data.is_mobile_app;
        if (is_admin_user) {
            $(".admin-user-nav").removeClass("nav-hidden");
        }
        if (is_mobile_app) {
            $('.drawer-hamburger').remove();
            $('#mobile-nav-logo').remove();
        }
        if (is_logged_in) {
            $("#navbar-you-avatar").attr("src", avatar_url);
            $(".logged-in-nav").removeClass("nav-hidden");
            $(".nav-member-profile-link").attr("href", profile_url);
            $(".nav-member-map-link").attr("href", profile_url + 'map/');
            $(".nav-member-badges-link").attr("href", profile_url + 'badges/');
            $(".nav-member-summits-link").attr("href", profile_url + 'summits/');
            $(".nav-member-challenges-link").attr("href", profile_url + 'challenges/');
            $(".nav-member-photos-link").attr("href", profile_url + 'photos/');
            $(".nav-member-avatar").attr("src", avatar_url);
            $(".nav-member-username").html(username);
            if (news_count > 0) {
                $('.fa-bell').addClass('faa-ring').addClass('animated');
                $(".nav-member-news-count").removeClass("nav-hidden");
                $(".nav-member-news-count").html(news_count);
                $(".nav-alerts-bell-div").removeClass("bell-without-alerts").addClass("bell-with-alerts");
                $(".nav-alerts-count-div").removeClass("bell-without-alerts").addClass("bell-with-alerts");
                $(".mobile-nav-alerts-bell-div").removeClass("bell-without-alerts").addClass("bell-with-alerts");
                $(".mobile-nav-count-bell-div").removeClass("bell-without-alerts").addClass("bell-with-alerts");
                $("#mobile-nav-alerts").show();
                loadNewsAlerts(1);
            } else {
                $(".nav-member-news-count").addClass("nav-hidden");
                $(".nav-member-news-count").html('');
                $(".nav-alerts-bell-div").removeClass("bell-with-alerts").addClass("bell-without-alerts");
                $(".nav-alerts-count-div").removeClass("bell-with-alerts").addClass("bell-without-alerts");
                $(".mobile-nav-alerts-bell-div").removeClass("bell-with-alerts").addClass("bell-without-alerts");
                $(".mobile-nav-count-bell-div").removeClass("bell-with-alerts").addClass("bell-without-alerts");
                $("#mobile-nav-alerts").hide();
                loadNewsAlerts(1);
            }
        } else {
            $(".logged-out-nav").removeClass("nav-hidden");
        }
        $(".nav-justified").show();
    });

    $('textarea.elastic').elastic();
    $('.drawer').drawer({
        iscroll: {
            preventDefault: true,
            click: true,
        },
    });
    $('#slideout-nav-join-btn').on('click', function(e) {
        e.preventDefault();
        $('.drawer').drawer('close');
        $('.navbar').css({"filter":"none"});
        $('#content-body').css({"filter":"none"});
    });
    $('#slideout-nav-login-btn').on('click', function(e) {
        e.preventDefault();
        $('.drawer').drawer('close');
        $('.navbar').css({"filter":"none"});
        $('#content-body').css({"filter":"none"});
    });
    $('#slideout-nav-log-climb-btn').on('click', function(e) {
        e.preventDefault();
        $('.drawer').drawer('close');
        $('.navbar').css({"filter":"none"});
        $('#content-body').css({"filter":"none"});
    });
    $('.slideout-links').on('click', function(e) {
        e.preventDefault();
       //$('.drawer').drawer('close');
        $('.navbar').css({"filter":"none"});
        $('#content-body').css({"filter":"none"});
        $('body').removeClass('drawer-open');
        $('body').removeClass('drawer-close');
        var new_url = $(this).attr('href');
        setTimeout(function(){
          window.location.href = new_url;
        }, 300);
    });
    $('.slideout-sublinks').on('click', function(e) {
       e.preventDefault();
       //$('.drawer').drawer('close');
        $('.navbar').css({"filter":"none"});
        $('#content-body').css({"filter":"none"});
        $('body').removeClass('drawer-open');
        $('body').removeClass('drawer-close');
        var new_url = $(this).attr('href');
        setTimeout(function(){
          window.location.href = new_url;
        }, 300);
    });
    $('.join-peakery').on('click', function(e) {
        $('#accounts-sign-up .modal-body').load('/accounts/register/?next=/accounts/sign-up-choose-name/1/',function(e) {
            $('#accounts-sign-up-title').html('<span style="">Sign up for free</span>');
            $('#accounts-sign-up').modal('show');
        });
    });
    $('#accounts-sign-up').on('click', '.sign-up-with-email', function(e) {
        $('#accounts-login').modal('hide');
        $('#accounts-sign-up .modal-body').load('/accounts/register/?next=/accounts/sign-up-choose-name/1/',function(e) {
            $('#accounts-sign-up-title').html('<span style="">Sign up for free</span>');
            $('#accounts-sign-up').modal('show');
        });
    });
    $('#forgot-password').on('click', function(e) {
        $('#accounts-login').modal('hide');
        $('#accounts-forgot-password').modal('show');
    });
    $('#remember-password').on('click', function(e) {
        $('#accounts-forgot-password').modal('hide');
        $('#accounts-login').modal('show');
    });
    $('#not-a-member').on('click', function(e) {
        $('#accounts-login').modal('hide');
        $('#accounts-sign-up .modal-body').load('/accounts/register/?next=/accounts/sign-up-choose-name/1/',function(e) {
            $('#accounts-sign-up-title').html('<span style="">Sign up for free</span>');
            $('#accounts-sign-up').modal('show');
        });
    });
    $('#already-a-member').on('click', function(e) {
        $('#accounts-sign-up').modal('hide');
        $('#accounts-login').modal('show');
    });
    $('#accounts-sign-up').on('click', '#already-a-member', function(e) {
        $('#accounts-sign-up').modal('hide');
        $('#accounts-login').modal('show');
    });
    $('#reset-my-password').on('click', function(e) {
        e.preventDefault();
        var post_url = '/accounts/password_reset/';
        $.ajax({
            type: "POST",
            url: post_url,
            data: $('#reset-password-form').serialize()
        }).done(function (html) {
            if (html == 'success') {
                //console.log('success');
                $('#accounts-forgot-password-title').html('Forgot password email sent');
                $('#reset-password-form').hide();
                $('#password-reset-email').html($('#id_email').val());
                $('#password-reset-success').fadeIn();

            } else {
                $("#password_reset_error").html(html);
                $("#password_reset_error").fadeIn();
            }
        });
    });
    $('#accounts-forgot-password').on('show.bs.modal', function() {
        $('#password-reset-success').hide();
        $('#password_reset_error').html('');
        $('#password_reset_error').hide();
        $('#reset-password-form').show();
    });
    $('#accounts-forgot-password').on('shown.bs.modal', function() {
        $('#id_email').val('');
        $('#id_email').parent().children('.holder').show();
        $('#id_email').focus();
    });

    //follower/following
    $('#nav-alerts-ul, #mobile-alerts-ul').on('click', '.unfollow-alert-sender', function() {
        var sender_id = $(this).data('sender_id');
        var btn = $(".unfollow-alert-"+sender_id);
        var follow_btn = $(".follow-alert-"+sender_id);
        btn.html('<i class="fa fa-spinner fa-spin"></i>');
        $.post('/accounts/unfollow/'+sender_id+'/',function(data){
            if (data == "True"){
                follow_btn.html('Follow back');
                $(".unfollow-alert-"+sender_id).hide();
                $(".follow-alert-"+sender_id).show();
            }
        });
    });

    $('#nav-alerts-ul, #mobile-alerts-ul').on('click', '.follow-alert-sender', function() {
        var sender_id = $(this).data('sender_id');
        var btn = $(".follow-alert-"+sender_id);
        var unfollow_btn = $(".unfollow-alert-"+sender_id);
        btn.html('<i class="fa fa-spinner fa-spin"></i>');
        $.post('/accounts/follow/'+sender_id+'/',function(data){
            if (data == "True"){
                unfollow_btn.html('You are following');
                $(".follow-alert-"+sender_id).hide();
                $(".unfollow-alert-"+sender_id).show();
            }
        });
    });

    //gpx uploader
    var gpxuploader = new qq.s3.FineUploader({
        debug: false,
        multiple: false,
        element: document.getElementById('gpx-file-1'),
        template: 'qq-gpx-template',
        request: {
            endpoint: 'https://peakery-media.s3.amazonaws.com',
            accessKey: 'AKIAJPWBM4YZXFHWBTPQ'
        },
        signature: {
            endpoint: '/api/s3signature/'
        },
        uploadSuccess: {
            endpoint: '/peaks/summit/s3_log_climb_gpx_upload/',
            params: {
            }
        },
        iframeSupport: {
            localBlankPagePath: '/api/s3blank/'
        },
        retry: {
           enableAuto: false // defaults to false
        },
        validation: {
            acceptFiles: ['.gpx'],
            allowedExtensions: ['gpx']
        },
        button: document.getElementById('gpx-upload-button'),
        text: {
            fileInputTitle: ''
        },
        callbacks: {
            onSubmitted: function(id, name) {
                $('#nav-log-your-climb').prop('disabled', true);
                $('#nav-log-your-climb').html('<i style="font-size: 22px;" class="fa fa-spinner fa-2x fa-spin" aria-hidden="true"></i>');
		$('#darkness').append('<div style="height: 100%; width: 100%; color: #fff; display: flex; justify-content: center; align-items: center;"><span style="width: 300px; text-align: center;">Finding peaks in your GPS track<br /><br /><div style="text-align: center;"><i style="font-size: 22px;" class="fa fa-spinner fa-2x fa-spin" aria-hidden="true"></i></div></span></div>');
		$('#darkness').show();
                //$('.log-climb-dropdown').hide();
            },
            onComplete: function(id, name, responseJSON, maybeXhr) {
                if (responseJSON.gpx_file != '' && responseJSON.valid_file == 'true') {
                    window.location.href = '/peaks/log_climb/?summits='+responseJSON.summit_logs;
                }
                else {
                    //alert("Sorry, that is not a valid GPX file");
		    $('#darkness').hide();
                    $('#message-modal-label').html('Error');
                    $('#message-modal-body').html('Sorry, that is not a valid GPX file. GPX files must include location, time and elevation data.');
                    $('#message-modal').modal('show');
                    $('#nav-log-your-climb').html('Log a climb');
                    $('#gpx-file-1').find('div.qq-upload-button').removeClass('qq-upload-loading');
                    $('#gpx-file-1').find('div.qq-upload-button').children('div').hide();
                    $('#gpx-upload-button-div').prop('disabled', false);
                }
            }
        },
        messages: {
            typeError: "Invalid file type. Please add only GPX files.",
            sizeError: "File is too large, maximum file size is 12MB.",
            minSizeError: "{file} is too small, minimum file size is {minSizeLimit}.",
            emptyError: "{file} is empty, please select files again without it.",
            allowedExtensionsError : "{file} is not allowed.",
            onLeave: "The files are being uploaded, if you leave now the upload will be cancelled."
        },
        showMessage: function (message) {
            $('#message-modal-label').html('Error');
            $('#message-modal-body').html(message);
            $('#message-modal').modal('show');
        },
        objectProperties: {
            acl: 'public-read',
            key: function (fileId) {

                var filename = gpxuploader.getName(fileId);
                var uuid = gpxuploader.getUuid(fileId);
                var ext = filename.substr(filename.lastIndexOf('.') + 1).toLowerCase();

                return  'gpx/' + uuid + '.' + ext;

            }
        }
    });

});

function openNavUrl(url, target) {
    if (target == '_blank') {
        window.open(url);
    } else {
        window.location.href = url;
    }
}

function loadNewsAlerts(page) {
    //fill latest news dropdown
    if (page == 1) {
        $('#nav-alerts-container').height(31);
        $('#mobile-alerts-container').height(31);
    }
    $.ajax({
        url: '/api/user/latest_news/?page='+page,
        dataType: 'json',
        beforeSend: function(xhr) {
            $("#nav-alerts-container").data('loading', true);
            $("#mobile-alerts-container").data('loading', true);
            $("#nav-alerts-ul").append('<li class="nav-alerts-loading"><i class="fa fa-spinner fa-spin"></i></li>');
            $("#mobile-alerts-ul").append('<li class="nav-alerts-loading"><i class="fa fa-spinner fa-spin"></i></li>');
        },
        success: function(data) {
            if (data.news.length > 0) {
                nextAlertsPage++;
                $('.nav-alerts-loading').fadeOut('fast', function() {
                    $(this).remove();
                });
                $.each( data, function( key, val ) {
                    if (key=='news') {
                        $.each( val, function( newskey, newsval ) {
                            var news_html;
                            var sender_avatar, sender_avatar_big;
                            if (newsval.sender_avatar_url != 'None' && newsval.sender_avatar_url != '') {
                                sender_avatar = '<img src="https://s3-us-west-1.amazonaws.com/peakery-media/' + newsval.sender_avatar_url + '" style="width: 40px;">';
                                sender_avatar_big = 'https://s3-us-west-1.amazonaws.com/peakery-media/' + newsval.sender_avatar_url_big;
                            } else {
                                sender_avatar = '<img src="https://s3-us-west-1.amazonaws.com/peakery-static/img/default-user-100x100.png" style="width: 40px;">';
                                sender_avatar_big = 'https://s3-us-west-1.amazonaws.com/peakery-static/img/default-user.png';
                            }
                            if (newsval.peak_thumbnail_url != 'None' && newsval.peak_thumbnail_url != '') {
                                peak_thumbnail_url = '<img src="https://s3-us-west-1.amazonaws.com/peakery-media/' + newsval.peak_thumbnail_url + '" style="width: 60px; height: 40px;">';
                            } else {
                                peak_thumbnail_url = '<img src="https://s3-us-west-1.amazonaws.com/peakery-static/img/spacer.png" style="width: 1px; height: 40px;">';
                            }
                            switch (newsval.notice_type) {
                                case 'new_comment_summitlog':
                                    news_html = '<li class="nav-alerts-li"><a href="/' + newsval.peak_slug + '/summits/' + newsval.summitlog_id + '/" class="nav-alerts-link"><div class="nav-alerts-li-div"><div class="nav-alerts-li-left">' + sender_avatar + '</div><div class="nav-alerts-li-wide-middle"><span class="nav-alerts-headline">' + newsval.sender + '</span> commented on your summit of <span class"nav-alerts-headline">' + newsval.peak_name + '</span></div><div class="nav-alerts-li-right">' + peak_thumbnail_url + '</div></div></a></li>';
                                    $('.nav-alerts-dropdown').append(news_html);
                                    break;
                                case 'new_like_summitlog':
                                    news_html = '<li class="nav-alerts-li"><a href="/' + newsval.peak_slug + '/summits/' + newsval.summitlog_id + '/" class="nav-alerts-link"><div class="nav-alerts-li-div"><div class="nav-alerts-li-left">' + sender_avatar + '</div><div class="nav-alerts-li-wide-middle"><span class="nav-alerts-headline">' + newsval.sender + '</span> liked your summit of <span class="nav-alerts-headline">' + newsval.peak_name + '</span></div><div class="nav-alerts-li-right">' + peak_thumbnail_url + '</div></div></a></li>';
                                    $('.nav-alerts-dropdown').append(news_html);
                                    break;
                                case 'new_follower':
                                    if (newsval.following > 0) {
                                        follow_button_html = '<a href="javascript:void(0);" data-sender_id="' + newsval.sender_id + '" class="follow-alert-' + newsval.sender_id + ' follow-alert-sender peakeryFollowButton followButton btn btn-secondary" style="border-radius: 4px; font-size: 12px; height: 40px; width: 140px; padding: 11px; display: none;">Follow back</a><a href="javascript:void(0);" data-sender_id="' + newsval.sender_id + '" class="unfollow-alert-' + newsval.sender_id + ' unfollow-alert-sender peakeryFollowButton btn btn-secondary unfollowButton" style="border-radius: 4px; font-size: 12px; height: 40px; width: 140px; padding: 11px; background-color: rgb(0, 179, 48); border-color: rgb(0, 179, 48);">You are following</a>';
                                    } else {
                                        follow_button_html = '<a href="javascript:void(0);" data-sender_id="' + newsval.sender_id + '" class="follow-alert-' + newsval.sender_id + ' follow-alert-sender peakeryFollowButton followButton btn btn-secondary" style="border-radius: 4px; font-size: 12px; height: 40px; width: 140px; padding: 11px;">Follow back</a><a href="javascript:void(0);" data-sender_id="' + newsval.sender_id + '" class="unfollow-alert-' + newsval.sender_id + ' unfollow-alert-sender peakeryFollowButton btn btn-secondary unfollowButton" style="border-radius: 4px; font-size: 12px; height: 40px; width: 140px; padding: 11px; background-color: rgb(0, 179, 48); border-color: rgb(0, 179, 48); display: none;">You are following</a>';
                                    }
                                    //follow_button_html = '';
                                    news_html = '<li class="nav-alerts-li"><div class="nav-alerts-li-div"><div class="nav-alerts-li-left"><a href="/members/' + newsval.sender + '/" class="nav-alerts-link">' + sender_avatar + '</a></div><div class="nav-alerts-li-middle"><a href="/members/' + newsval.sender + '/" class="nav-alerts-link"><span class="nav-alerts-headline">' + newsval.sender + '</span> is now following you.</a></div><div class="nav-alerts-li-wide-right">' + follow_button_html + '</div></div></li>';
                                    $('.nav-alerts-dropdown').append(news_html);
                                    break;
                                case 'user_added_to_summit_log':
                                    news_html = '<li class="nav-alerts-li"><a href="/' + newsval.peak_slug + '/summits/' + newsval.summitlog_id + '/" class="nav-alerts-link"><div class="nav-alerts-li-div"><div class="nav-alerts-li-left">' + sender_avatar + '</div><div class="nav-alerts-li-wide-middle"><span class="nav-alerts-headline">' + newsval.sender + '</span> added you to their summit of <span class="nav-alerts-headline">' + newsval.peak_name + '</span></div><div class="nav-alerts-li-right">' + peak_thumbnail_url + '</div></div></a></li>';
                                    $('.nav-alerts-dropdown').append(news_html);
                                    break;
                                case 'non_following_comment_summitlog':
                                    news_html = '<li class="nav-alerts-li"><a href="/' + newsval.peak_slug + '/summits/' + newsval.summitlog_id + '/" class="nav-alerts-link"><div class="nav-alerts-li-div"><div class="nav-alerts-li-left">' + sender_avatar + '</div><div class="nav-alerts-li-wide-middle"><span class="nav-alerts-headline">' + newsval.sender + '</span> also commented on <span class="nav-alerts-headline">' + newsval.username + '\'s</span> summit of <span class="nav-alerts-headline">' + newsval.peak_name + '</span></div><div class="nav-alerts-li-right">' + peak_thumbnail_url + '</div></div></a></li>';
                                    $('.nav-alerts-dropdown').append(news_html);
                                    break;
                            }
                        });
                    }
                });
                $('#nav-alerts-container').height(580);
                $('#mobile-alerts-container').height(580);
                $("#nav-alerts-container").removeData("loading");
                $("#mobile-alerts-container").removeData("loading");
                //resize for mobile widths
                var window_width = $(window).width();
                var wide_middle_width = window_width - 145;
                var middle_width = window_width - 235;
                if (window_width < 768) {
                    $('#mobile-alerts-ul').find('.nav-alerts-li-wide-middle').width(wide_middle_width);
                    $('#mobile-alerts-ul').find('.nav-alerts-li-middle').width(middle_width);
                }
            } else {
                allNewsLoaded = true;
                $('.nav-alerts-loading').fadeOut('fast', function() {
                    $(this).remove();
                });
                $("#nav-alerts-ul").append('<li style="text-align: center;" id="nav-alerts-all-loaded">No more news</li>');
                $("#mobile-alerts-ul").append('<li style="text-align: center;" id="mobile-alerts-all-loaded">No more news</li>');
                $("#nav-alerts-all-loaded").fadeOut(1000, function() {
                    $(this).remove();
                });
                $("#mobile-alerts-all-loaded").fadeOut(1000, function() {
                    $(this).remove();
                });
                $("#nav-alerts-container").removeData("loading");
                $("#mobile-alerts-container").removeData("loading");
            }
        }
    });
}
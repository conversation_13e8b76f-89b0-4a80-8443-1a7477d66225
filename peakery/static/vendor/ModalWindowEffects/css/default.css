/* General Demo Style */
@import url(https://fonts.googleapis.com/css?family=Lato:300,400,700);

@font-face {
	font-family: 'codropsicons';
	src:url('../fonts/codropsicons/codropsicons.eot');
	src:url('../fonts/codropsicons/codropsicons.eot?#iefix') format('embedded-opentype'),
		url('../fonts/codropsicons/codropsicons.woff') format('woff'),
		url('../fonts/codropsicons/codropsicons.ttf') format('truetype'),
		url('../fonts/codropsicons/codropsicons.svg#codropsicons') format('svg');
	font-weight: normal;
	font-style: normal;
}

*, *:after, *:before { -webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box; }
body, html { font-size: 100%; padding: 0; margin: 0; }

/* Clearfix hack by <PERSON>: http://nicolasgallagher.com/micro-clearfix-hack/ */
.clearfix:before, .clearfix:after { content: " "; display: table; }
.clearfix:after { clear: both; }

body {
	font-family: 'Lato', Calibri, Arial, sans-serif;
	color: #fff;
	background: #e74c3c;
}

a {
	color: #c0392b;
	text-decoration: none;
}

a:hover,
a:active {
	color: #333;
}

/* Header Style */
.main,
.md-container > header {
	margin: 0 auto;
	padding: 2em;
}

.md-container > header {
	text-align: center;
	background: #d94839;
	padding: 3em;
}

.md-container > header h1 {
	font-size: 2.625em;
	line-height: 1.3;
	margin: 0;
	font-weight: 300;
}

.md-container > header span {
	display: block;
	font-size: 60%;
	opacity: 0.7;
	padding: 0 0 0.6em 0.1em;
}

/* Main Content */
.main {
	max-width: 69em;
}

.column {
	float: left;
	width: 50%;
	padding: 0 2em;
	min-height: 300px;
	position: relative;
}

.column:nth-child(2) {
	box-shadow: -1px 0 0 rgba(0,0,0,0.1);
}

.column p {
	font-weight: 300;
	font-size: 2em;
	padding: 0;
	margin: 0;
	text-align: right;
	line-height: 1.5;
}

/* To Navigation Style */
.codrops-top {
	background: #fff;
	background: rgba(255, 255, 255, 0.2);
	text-transform: uppercase;
	width: 100%;
	font-size: 0.69em;
	line-height: 2.2;
}

.codrops-top a {
	padding: 0 1em;
	letter-spacing: 0.1em;
	display: inline-block;
}

.codrops-top a:hover {
	color: #e74c3c;
	background: rgba(255,255,255,0.6);
}

.codrops-top span.right {
	float: right;
}

.codrops-top span.right a {
	float: left;
	display: block;
}

.codrops-icon:before {
	font-family: 'codropsicons';
	margin: 0 4px;
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;
	-webkit-font-smoothing: antialiased;
}

.codrops-icon-drop:before {
	content: "\e001";
}

.codrops-icon-prev:before {
	content: "\e004";
}

@media screen and (max-width: 46.0625em) {
	.column {
		width: 100%;
		min-width: auto;
		min-height: auto;
		padding: 1em; 
	}

	.column p {
		text-align: left;
		font-size: 1.5em;
	}

	.column:nth-child(2) {
		box-shadow: 0 -1px 0 rgba(0,0,0,0.1);
	}
}

@media screen and (max-width: 25em) {

	.codrops-icon span {
		display: none;
	}

}
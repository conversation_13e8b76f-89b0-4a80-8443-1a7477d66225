{% load json_filters %}
{"active_members": "{{ active_members }}", "members": [
    {% for m in members %}
        {"id": "{{ m.member.id }}", "username": "{{ m.member.username }}", "avatar_url": "{{ m.member.avatar_url|urlencode }}", "member_count": "{{ m.member.member_count }}", "count_type": "{{ m.member.count_type }}", "count_type_plural": "{{ m.member.count_type_plural }}", "peaks": [
        {% if m.peaks %}
            {% for p in m.peaks %}
                {"peak_id": "{{ p.id }}", "peak_name": "{{ p.name }}", "slug": "{{ p.slug }}", "peak_thumbnail_url": "{{ p.peak_thumbnail_url }}", "last_summit_date": "{{ p.max_date }}"}{% if not forloop.last %},{% endif %}
            {% endfor %}
        {% endif %}
        ]}{% if not forloop.last %},{% endif %}
    {% endfor %}
]}
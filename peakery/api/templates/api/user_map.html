{% load json_filters %}
{"parameters": [
    {"keyword": "{{ keyword }}", "near": "{{ near }}", "near_query": "{{ near_query }}", "state_id": "{{ state_id }}", "elev_min": "{{ elev_min }}", "elev_max": "{{ elev_max }}", "prom_min": "{{ prom_min }}", "prom_max": "{{ prom_max }}", "summits_min": "{{ summits_min }}", "summits_max": "{{ summits_max }}", "difficulty_min": "{{ difficulty_min }}", "difficulty_max": "{{ difficulty_max }}", "length_min": "{{ length_min }}", "length_max": "{{ length_max }}", "vertical_min": "{{ vertical_min }}", "vertical_max": "{{ vertical_max }}", "last_climbed_min": "{{ last_climbed_min }}", "last_climbed_max": "{{ last_climbed_max }}", "classics": "{{ classics }}", "in_challenge": "{{ in_challenge }}", "you_climbed": "{{ you_climbed }}", "has_gps": "{{ has_gps }}", "lat": "{{ lat }}", "lng": "{{ lng }}", "bounds": "{{ bounds }}"}
], "peaks": [
    {% for p in peaks %}
        {"id": "{{ p.peak.id }}", "name": {{ p.peak.name|jsonify }}, "slug": "{{ p.peak.slug }}", "is_classic": "{{ p.peak.is_classic }}", "kom_user": "{{ p.peak.kom_user }}", "first_ascent_user": "{{ p.peak.first_ascent_user }}", "summit_stewards": {% if p.peak.summit_stewards %}{{ p.peak.summit_stewards|jsonify }}{% else %}""{% endif %}, "lat": "{{ p.peak.lat }}", "lng": "{{ p.peak.lng }}", "peak_highlights": {{ p.peak.peak_highlights|jsonify }}, "summit_count": "{{ p.peak.summitlog_count }}", "elevation": "{{ p.peak.elevation }}", "prominence": "{{ p.peak.prominence }}", "your_summits": "{{ p.peak.your_summits }}", "your_attempts": "{{ p.peak.your_attempts }}", "challenge_count": "{{ p.peak.challenge_count }}", "thumbnail_url": "{{ p.peak.thumbnail_url }}", "region": [
        {% if p.region %}
            {% for r in p.region %}
                {"region_id": "{{ r.id }}", "region_name": "{{ r.name }}", "region_slug": "{{ r.get_absolute_url }}", "country_name": "{{ r.country.name }}"}{% if not forloop.last %},{% endif %}
            {% endfor %}
        {% endif %}
        ], "country": [
            {% if p.country %}
                {% for c in p.country %}
                    {"country_id": "{{ c.id }}", "country_name": "{{ c.name }}", "country_slug": "{{ c.get_absolute_url }}"}{% if not forloop.last %},{% endif %}
                {% endfor %}
            {% endif %}
        ]}{% if not forloop.last %},{% endif %}
    {% endfor %}
]}